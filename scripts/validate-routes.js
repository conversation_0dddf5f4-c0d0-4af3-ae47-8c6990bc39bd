#!/usr/bin/env node

const http = require('http');
const fs = require('fs');
const path = require('path');

// Base URL for testing
const BASE_URL = 'http://localhost:3000';

// Extract routes from the routes page
const routesFromPage = [
  // Core pages
  '/',
  '/about',
  '/contact',
  '/routes',
  
  // Original structure
  '/destinations',
  '/destinations/marrakech',
  '/destinations/fes',
  '/destinations/chefchaouen',
  '/destinations/sahara',
  '/destinations/essaouira',
  '/destinations/casablanca',
  '/explore',
  '/experiences',
  '/cultural-immersion',
  '/trip-builder',
  '/dashboard',
  
  // International structure
  '/morocco/destinations',
  '/morocco/destinations/marrakech',
  '/morocco/destinations/fes',
  '/morocco/destinations/chefchaouen',
  '/morocco/destinations/sahara',
  '/morocco/destinations/essaouira',
  '/morocco/destinations/casablanca',
  '/morocco/explore',
  '/morocco/experiences',
  '/morocco/cultural-immersion',
  
  // Japan routes (if implemented)
  '/japan/destinations',
  '/japan/explore',
  '/japan/experiences',
  
  // Admin and user pages
  '/admin',
  '/admin/dashboard',
  '/admin/destinations',
  '/admin/experiences',
  '/admin/users',
  '/admin/analytics',
  '/user/profile',
  '/user/trips',
  '/user/preferences',
  
  // Auth pages
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  
  // API routes (should return JSON)
  '/api/destinations',
  '/api/experiences',
  '/api/countries'
];

// Function to test a single route
function testRoute(route) {
  return new Promise((resolve) => {
    const url = `${BASE_URL}${route}`;
    
    const req = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const result = {
          route,
          status: res.statusCode,
          success: res.statusCode >= 200 && res.statusCode < 400,
          contentType: res.headers['content-type'] || 'unknown',
          hasContent: data.length > 0,
          isHtml: (res.headers['content-type'] || '').includes('text/html'),
          isJson: (res.headers['content-type'] || '').includes('application/json'),
          error: null
        };
        
        // Check for common error patterns in HTML
        if (result.isHtml) {
          // More specific error detection - look for actual error pages, not embedded error handling
          result.hasError = data.includes('<title>Application error') ||
                           (data.includes('This page could not be found') && data.includes('<h1') && data.includes('404')) ||
                           data.includes('<title>500') ||
                           data.includes('Unhandled Runtime Error');
          result.hasReactError = data.includes('Element type is invalid') ||
                                data.includes('Cannot read properties of undefined') ||
                                data.includes('Check the render method');
        }
        
        resolve(result);
      });
    });
    
    req.on('error', (error) => {
      resolve({
        route,
        status: 0,
        success: false,
        error: error.message,
        hasContent: false
      });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        route,
        status: 0,
        success: false,
        error: 'Timeout',
        hasContent: false
      });
    });
  });
}

// Function to test all routes
async function validateAllRoutes() {
  console.log('🚀 Starting route validation...\n');
  
  const results = [];
  const errors = [];
  const warnings = [];
  
  for (const route of routesFromPage) {
    process.stdout.write(`Testing ${route}... `);
    
    const result = await testRoute(route);
    results.push(result);
    
    if (result.success) {
      if (result.hasError || result.hasReactError) {
        console.log('⚠️  WARNING - Has errors in content');
        warnings.push(result);
      } else {
        console.log('✅ OK');
      }
    } else {
      console.log(`❌ FAILED (${result.status || 'ERROR'})`);
      errors.push(result);
    }
  }
  
  // Summary
  console.log('\n📊 VALIDATION SUMMARY');
  console.log('='.repeat(50));
  console.log(`Total routes tested: ${results.length}`);
  console.log(`✅ Successful: ${results.filter(r => r.success && !r.hasError && !r.hasReactError).length}`);
  console.log(`⚠️  Warnings: ${warnings.length}`);
  console.log(`❌ Errors: ${errors.length}`);
  
  if (warnings.length > 0) {
    console.log('\n⚠️  WARNINGS (Routes with content errors):');
    warnings.forEach(w => {
      console.log(`  ${w.route} - Status: ${w.status}`);
    });
  }
  
  if (errors.length > 0) {
    console.log('\n❌ ERRORS:');
    errors.forEach(e => {
      console.log(`  ${e.route} - Status: ${e.status} - ${e.error || 'Failed'}`);
    });
  }
  
  // Save detailed results
  const reportPath = path.join(__dirname, 'route-validation-report.json');
  fs.writeFileSync(reportPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    summary: {
      total: results.length,
      successful: results.filter(r => r.success && !r.hasError && !r.hasReactError).length,
      warnings: warnings.length,
      errors: errors.length
    },
    results,
    warnings,
    errors
  }, null, 2));
  
  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  
  return {
    success: errors.length === 0,
    results,
    warnings,
    errors
  };
}

// Run validation
if (require.main === module) {
  validateAllRoutes()
    .then(({ success }) => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Validation script failed:', error);
      process.exit(1);
    });
}

module.exports = { validateAllRoutes, testRoute };
