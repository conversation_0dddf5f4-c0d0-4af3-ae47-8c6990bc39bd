{"timestamp": "2025-08-17T00:13:53.265Z", "summary": {"total": 44, "successful": 10, "warnings": 0, "errors": 34}, "results": [{"route": "/", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/about", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/contact", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/routes", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/destinations", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/marrakech", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/fes", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/chefchaouen", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/sahara", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/essaouira", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/casablanca", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/explore", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/experiences", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/cultural-immersion", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/trip-builder", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/dashboard", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/marrakech", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/fes", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/chefchaouen", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/sahara", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/essaouira", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/casablanca", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/explore", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/experiences", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/cultural-immersion", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/japan/destinations", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/japan/explore", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/japan/experiences", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin/dashboard", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin/destinations", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin/experiences", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin/users", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin/analytics", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/user/profile", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/user/trips", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/user/preferences", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/auth/login", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/auth/register", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/auth/forgot-password", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/api/destinations", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/api/experiences", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/api/countries", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}], "warnings": [], "errors": [{"route": "/", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/about", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/contact", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/routes", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/trip-builder", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/dashboard", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/marrakech", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/fes", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/chefchaouen", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/sahara", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/essaouira", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/destinations/casablanca", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/explore", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/experiences", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/morocco/cultural-immersion", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/japan/destinations", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/japan/explore", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/japan/experiences", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin/dashboard", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin/destinations", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin/experiences", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin/users", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/admin/analytics", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/user/profile", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/user/trips", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/user/preferences", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/auth/login", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/auth/register", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/auth/forgot-password", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/api/destinations", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/api/experiences", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}, {"route": "/api/countries", "status": 500, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": false, "hasReactError": false}]}