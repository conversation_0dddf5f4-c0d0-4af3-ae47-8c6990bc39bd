{"timestamp": "2025-08-16T23:46:35.825Z", "summary": {"total": 44, "successful": 10, "warnings": 24, "errors": 10}, "results": [{"route": "/", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/about", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/contact", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/routes", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/destinations", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/marrakech", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/fes", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/chefchaouen", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/sahara", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/essaouira", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/destinations/casablanca", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/explore", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/experiences", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/cultural-immersion", "status": 307, "success": true, "contentType": "unknown", "hasContent": true, "isHtml": false, "isJson": false, "error": null}, {"route": "/trip-builder", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/dashboard", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/marrakech", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/fes", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/chefchaouen", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/sahara", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/essaouira", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/casablanca", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/explore", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/experiences", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/cultural-immersion", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/japan/destinations", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/japan/explore", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/japan/experiences", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/admin", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/admin/dashboard", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/admin/destinations", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/admin/experiences", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/admin/users", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/admin/analytics", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/user/profile", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/user/trips", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/user/preferences", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/auth/login", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/auth/register", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/auth/forgot-password", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/api/destinations", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/api/experiences", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/api/countries", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}], "warnings": [{"route": "/", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/about", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/contact", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/routes", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/trip-builder", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/dashboard", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/marrakech", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/fes", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/chefchaouen", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/sahara", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/essaouira", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/destinations/casablanca", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/explore", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/experiences", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/morocco/cultural-immersion", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/japan/destinations", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/japan/explore", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/japan/experiences", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/admin", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/admin/destinations", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/admin/experiences", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/api/destinations", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/api/experiences", "status": 200, "success": true, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}], "errors": [{"route": "/admin/dashboard", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/admin/users", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/admin/analytics", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/user/profile", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/user/trips", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/user/preferences", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/auth/login", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/auth/register", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/auth/forgot-password", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}, {"route": "/api/countries", "status": 404, "success": false, "contentType": "text/html; charset=utf-8", "hasContent": true, "isHtml": true, "isJson": false, "error": null, "hasError": true, "hasReactError": false}]}