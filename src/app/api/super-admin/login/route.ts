import { NextRequest, NextResponse } from 'next/server'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { supabase } from '@/lib/supabase'

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Query super admin users table
    const { data: adminUser, error } = await supabase
      .from('super_admin_users')
      .select('*')
      .eq('email', email.toLowerCase())
      .eq('is_active', true)
      .single()

    if (error || !adminUser) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, adminUser.password_hash)
    if (!isValidPassword) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      )
    }

    // Get role permissions
    const { data: roleData, error: roleError } = await supabase
      .from('super_admin_roles')
      .select('permissions')
      .eq('role_name', adminUser.role)
      .single()

    if (roleError) {
      console.error('Failed to fetch role permissions:', roleError)
      return NextResponse.json(
        { error: 'Authentication error' },
        { status: 500 }
      )
    }

    // Update last login
    await supabase
      .from('super_admin_users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', adminUser.id)

    // Create JWT token
    const token = jwt.sign(
      {
        userId: adminUser.id,
        email: adminUser.email,
        role: adminUser.role
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    )

    // Prepare user data (exclude sensitive information)
    const userData = {
      id: adminUser.id,
      email: adminUser.email,
      name: adminUser.name,
      role: adminUser.role,
      permissions: roleData.permissions,
      countryAccess: adminUser.country_access || [],
      isActive: adminUser.is_active,
      lastLogin: adminUser.last_login,
      createdAt: adminUser.created_at
    }

    return NextResponse.json({
      user: userData,
      token
    })

  } catch (error) {
    console.error('Super admin login error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Create default super admin user if none exists
export async function GET() {
  try {
    // Check if any super admin users exist
    const { data: existingUsers, error } = await supabase
      .from('super_admin_users')
      .select('id')
      .limit(1)

    if (error) {
      console.error('Error checking super admin users:', error)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    if (existingUsers && existingUsers.length > 0) {
      return NextResponse.json({ message: 'Super admin users already exist' })
    }

    // Create default super admin user
    const defaultPassword = 'admin123' // Change this in production!
    const hashedPassword = await bcrypt.hash(defaultPassword, 12)

    const { data: newUser, error: createError } = await supabase
      .from('super_admin_users')
      .insert({
        email: '<EMAIL>',
        name: 'Super Administrator',
        password_hash: hashedPassword,
        role: 'super_admin',
        country_access: ['*'],
        is_active: true,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    if (createError) {
      console.error('Error creating default super admin:', createError)
      return NextResponse.json({ error: 'Failed to create default user' }, { status: 500 })
    }

    return NextResponse.json({
      message: 'Default super admin user created',
      email: '<EMAIL>',
      password: defaultPassword,
      warning: 'Please change the default password immediately!'
    })

  } catch (error) {
    console.error('Error in super admin setup:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
