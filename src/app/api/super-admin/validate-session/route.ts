import { NextRequest, NextResponse } from 'next/server'
import jwt from 'jsonwebtoken'
import { supabase } from '@/lib/supabase'

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key'

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'No valid authorization header' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7) // Remove 'Bearer ' prefix

    // Verify JWT token
    let decoded: any
    try {
      decoded = jwt.verify(token, JWT_SECRET)
    } catch (jwtError) {
      return NextResponse.json(
        { error: 'Invalid or expired token' },
        { status: 401 }
      )
    }

    // Fetch current user data from database
    const { data: adminUser, error } = await supabase
      .from('super_admin_users')
      .select('*')
      .eq('id', decoded.userId)
      .eq('is_active', true)
      .single()

    if (error || !adminUser) {
      return NextResponse.json(
        { error: 'User not found or inactive' },
        { status: 401 }
      )
    }

    // Get role permissions
    const { data: roleData, error: roleError } = await supabase
      .from('super_admin_roles')
      .select('permissions')
      .eq('role_name', adminUser.role)
      .single()

    if (roleError) {
      console.error('Failed to fetch role permissions:', roleError)
      return NextResponse.json(
        { error: 'Authentication error' },
        { status: 500 }
      )
    }

    // Prepare user data (exclude sensitive information)
    const userData = {
      id: adminUser.id,
      email: adminUser.email,
      name: adminUser.name,
      role: adminUser.role,
      permissions: roleData.permissions,
      countryAccess: adminUser.country_access || [],
      isActive: adminUser.is_active,
      lastLogin: adminUser.last_login,
      createdAt: adminUser.created_at
    }

    return NextResponse.json(userData)

  } catch (error) {
    console.error('Session validation error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
