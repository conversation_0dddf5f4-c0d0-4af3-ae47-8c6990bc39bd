'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { CountryDataDemo } from '@/components/country-data/country-data-demo'
import { Globe, ChevronDown } from 'lucide-react'

const AVAILABLE_COUNTRIES = [
  { code: 'MAR', name: 'Morocco', flag: '🇲🇦' },
  { code: 'JPN', name: 'Japan', flag: '🇯🇵' },
  { code: 'ITA', name: 'Italy', flag: '🇮🇹' },
  { code: 'ESP', name: 'Spain', flag: '🇪🇸' }
]

export default function CountryDataDemoPage() {
  const [selectedCountry, setSelectedCountry] = useState('MAR')
  const [showCountrySelector, setShowCountrySelector] = useState(false)

  const currentCountry = AVAILABLE_COUNTRIES.find(c => c.code === selectedCountry) || AVAILABLE_COUNTRIES[0]

  return (
    <div className="min-h-screen bg-slate-50">
      <Header />
      
      <main className="pt-20">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-600 to-purple-700 text-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <Globe className="w-8 h-8 text-white" />
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                Country Data Management System
              </h1>
              
              <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
                Comprehensive demonstration of our country-specific data management system featuring 
                destinations, experiences, cultural content, weather data, and multi-currency support.
              </p>

              {/* Country Selector */}
              <div className="relative inline-block">
                <button
                  onClick={() => setShowCountrySelector(!showCountrySelector)}
                  className="flex items-center gap-3 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl px-6 py-3 text-white hover:bg-white/30 transition-all duration-200"
                >
                  <span className="text-2xl">{currentCountry.flag}</span>
                  <span className="font-semibold">{currentCountry.name}</span>
                  <ChevronDown className={`w-4 h-4 transition-transform ${showCountrySelector ? 'rotate-180' : ''}`} />
                </button>

                {showCountrySelector && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95, y: -10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-xl border border-slate-200 py-2 z-50"
                  >
                    {AVAILABLE_COUNTRIES.map((country) => (
                      <button
                        key={country.code}
                        onClick={() => {
                          setSelectedCountry(country.code)
                          setShowCountrySelector(false)
                        }}
                        className={`w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-slate-50 transition-colors ${
                          selectedCountry === country.code ? 'bg-blue-50 text-blue-700' : 'text-slate-700'
                        }`}
                      >
                        <span className="text-xl">{country.flag}</span>
                        <span className="font-medium">{country.name}</span>
                      </button>
                    ))}
                  </motion.div>
                )}
              </div>
            </motion.div>
          </div>
        </section>

        {/* Demo Content */}
        <section className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              key={selectedCountry} // Re-render when country changes
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <CountryDataDemo countryCode={selectedCountry} />
            </motion.div>
          </div>
        </section>

        {/* Features Overview */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-slate-900 mb-4">
                System Features
              </h2>
              <p className="text-lg text-slate-600 max-w-3xl mx-auto">
                Our Country Data Management System provides comprehensive infrastructure 
                for managing international travel platforms with cultural sensitivity and accuracy.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  title: 'Country Data Schema',
                  description: 'Comprehensive data structure for countries, destinations, experiences, and cultural information.',
                  features: ['Destination Management', 'Experience Catalog', 'Cultural Customs', 'Local Guides']
                },
                {
                  title: 'Multi-Currency Support',
                  description: 'Real-time currency conversion and country-specific pricing with automatic rate updates.',
                  features: ['Currency Conversion', 'Price Formatting', 'Exchange Rates', 'Regional Pricing']
                },
                {
                  title: 'Weather & Seasonal Data',
                  description: 'Seasonal recommendations, weather forecasts, and climate information for travel planning.',
                  features: ['Current Weather', 'Seasonal Data', 'Travel Recommendations', 'Climate Info']
                },
                {
                  title: 'Cultural Content Management',
                  description: 'Culturally sensitive content with validation workflows and local expert review.',
                  features: ['Cultural Customs', 'Language Phrases', 'Etiquette Guidelines', 'Validation System']
                },
                {
                  title: 'Local Guide Network',
                  description: 'Comprehensive guide management with specializations, ratings, and availability.',
                  features: ['Guide Profiles', 'Specializations', 'Availability', 'Rating System']
                },
                {
                  title: 'Search & Discovery',
                  description: 'Advanced search capabilities across destinations, experiences, and cultural content.',
                  features: ['Smart Search', 'Filtering', 'Recommendations', 'Personalization']
                }
              ].map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="bg-slate-50 rounded-xl p-6"
                >
                  <h3 className="text-lg font-semibold text-slate-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-slate-600 mb-4">
                    {feature.description}
                  </p>
                  <ul className="space-y-2">
                    {feature.features.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-center gap-2 text-sm text-slate-700">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                        {item}
                      </li>
                    ))}
                  </ul>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Technical Implementation */}
        <section className="py-16 bg-slate-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold mb-4">
                Technical Implementation
              </h2>
              <p className="text-lg text-slate-300 max-w-3xl mx-auto">
                Built with modern TypeScript architecture, comprehensive type safety, 
                and scalable service patterns for international platform management.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-slate-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4">Services Architecture</h3>
                <ul className="space-y-2 text-slate-300">
                  <li>• CountryDataService - Core data management</li>
                  <li>• CurrencyService - Multi-currency support</li>
                  <li>• WeatherService - Climate and seasonal data</li>
                  <li>• CulturalContentService - Cultural validation</li>
                </ul>
              </div>

              <div className="bg-slate-800 rounded-xl p-6">
                <h3 className="text-xl font-semibold mb-4">React Hooks</h3>
                <ul className="space-y-2 text-slate-300">
                  <li>• useCountryData - Comprehensive country hook</li>
                  <li>• useWeatherData - Weather and climate</li>
                  <li>• useCurrencyConverter - Currency operations</li>
                  <li>• useDestinationData - Destination-specific data</li>
                </ul>
              </div>
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  )
}
