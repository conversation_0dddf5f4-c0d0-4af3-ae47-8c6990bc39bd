@tailwind base;
@tailwind components;
@tailwind utilities;

/* Mobile optimizations */
@media (max-width: 768px) {
  .trip-summary-widget {
    bottom: 80px !important;
    right: 16px !important;
    left: 16px !important;
    width: auto !important;
  }
  
  .destination-selector-mobile {
    grid-template-columns: 1fr !important;
  }
  
  .sticky-continue-button {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: white;
    border-top: 1px solid #e5e7eb;
    z-index: 50;
  }
}

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Playfair+Display:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-white text-gray-900 font-sans;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-display;
  }
}

@layer components {
  /* Modern Button System */
  .btn {
    @apply inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white shadow-lg hover:shadow-xl px-8 py-4 rounded-xl focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-white hover:bg-neutral-50 text-neutral-700 border-2 border-neutral-200 hover:border-neutral-300 px-8 py-4 rounded-xl focus:ring-neutral-500 shadow-sm hover:shadow-md;
  }
  
  .btn-accent {
    @apply btn bg-gradient-to-r from-morocco-500 to-morocco-600 hover:from-morocco-600 hover:to-morocco-700 text-white shadow-lg hover:shadow-xl px-8 py-4 rounded-xl focus:ring-morocco-500;
  }
  
  .btn-ghost {
    @apply btn bg-transparent hover:bg-neutral-100 text-neutral-700 hover:text-neutral-900 px-6 py-3 rounded-lg focus:ring-neutral-500;
  }
  
  .btn-sm {
    @apply px-4 py-2 text-sm rounded-lg;
  }
  
  .btn-lg {
    @apply px-10 py-5 text-lg rounded-2xl;
  }
  
  /* Enhanced Card System */
  .card {
    @apply bg-white rounded-2xl shadow-sm border border-neutral-100 p-8 transition-all duration-200 hover:shadow-lg hover:border-neutral-200;
  }
  
  .card-elevated {
    @apply card shadow-lg border-neutral-200 hover:shadow-xl;
  }
  
  .card-feature {
    @apply card bg-gradient-to-br from-white to-neutral-50 border-neutral-200 hover:shadow-xl hover:scale-[1.02] transition-all duration-300;
  }
  
  /* Modern Input System */
  .input-field {
    @apply w-full px-4 py-4 border-2 border-neutral-200 rounded-xl bg-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 placeholder:text-neutral-400;
  }
  
  .input-field:hover {
    @apply border-neutral-300;
  }
  
  /* Typography Enhancements */
  .heading-xl {
    @apply text-5xl md:text-6xl lg:text-7xl font-display font-bold tracking-tight;
  }
  
  .heading-lg {
    @apply text-3xl md:text-4xl lg:text-5xl font-display font-bold tracking-tight;
  }
  
  .heading-md {
    @apply text-2xl md:text-3xl font-display font-semibold tracking-tight;
  }
  
  .text-body-lg {
    @apply text-lg md:text-xl leading-relaxed text-neutral-600;
  }
  
  .text-body {
    @apply text-base leading-relaxed text-neutral-600;
  }
  
  /* Layout Utilities */
  .section-padding {
    @apply py-20 lg:py-32;
  }
  
  .container-custom {
    @apply max-w-7xl mx-auto px-6 lg:px-8;
  }
  
  /* Gradient Overlays */
  .gradient-overlay {
    @apply absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent;
  }
  
  .gradient-overlay-light {
    @apply absolute inset-0 bg-gradient-to-t from-white/90 via-white/50 to-transparent;
  }
  
  /* Modern Shadows */
  .shadow-soft {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
  }
  
  .shadow-medium {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  .shadow-strong {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}