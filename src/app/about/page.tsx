'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Head<PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { EnhancedTabNavigation } from '@/components/ui/enhanced-tab-navigation'
import {
  Heart,
  Users,
  Globe,
  Award,
  MapPin,
  Star,
  Quote,
  ArrowRight,
  Play,
  CheckCircle,
  Target,
  Eye,
  Compass,
  BookOpen
} from 'lucide-react'

export default function AboutPage() {
  const [activeTab, setActiveTab] = useState('story')

  const tabs = [
    { id: 'story', label: 'Our Story', icon: BookOpen },
    { id: 'values', label: 'Values', icon: Heart },
    { id: 'timeline', label: 'Timeline', icon: MapPin },
    { id: 'team', label: 'Team', icon: Users },
    { id: 'testimonials', label: 'Testimonials', icon: Star }
  ]

  const stats = [
    { number: '2,500+', label: 'Happy Travelers', icon: Users },
    { number: '50+', label: 'Destinations', icon: MapPin },
    { number: '15+', label: 'Years Experience', icon: Award },
    { number: '4.9/5', label: 'Average Rating', icon: Star }
  ]

  const values = [
    {
      icon: Heart,
      title: 'Authentic Connections',
      description: 'We believe travel should create genuine connections between cultures, not just photo opportunities.'
    },
    {
      icon: Users,
      title: 'Local Expertise',
      description: 'Our network of local guides are cultural ambassadors who share their homeland with passion and pride.'
    },
    {
      icon: Globe,
      title: 'Sustainable Tourism',
      description: 'We\'re committed to tourism that benefits local communities and preserves Morocco\'s cultural heritage.'
    },
    {
      icon: Award,
      title: 'Excellence in Service',
      description: 'Every detail matters. We strive for perfection in creating unforgettable travel experiences.'
    }
  ]

  const team = [
    {
      name: 'Amina Benali',
      role: 'Founder & CEO',
      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=300&h=300&fit=crop&crop=face',
      bio: 'Born in Casablanca, Amina founded ComeToMorocco with a vision to share the authentic beauty of her homeland.',
      experience: '15 years in tourism'
    },
    {
      name: 'Omar Alaoui',
      role: 'Head of Cultural Experiences',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face',
      bio: 'A historian and cultural expert who designs our most immersive traditional experiences.',
      experience: '12 years in cultural tourism'
    },
    {
      name: 'Laila Mansouri',
      role: 'Director of Agent Relations',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=300&h=300&fit=crop&crop=face',
      bio: 'Laila manages our network of expert local guides and ensures quality standards across all experiences.',
      experience: '10 years in hospitality'
    },
    {
      name: 'Youssef Kadiri',
      role: 'Head of Technology',
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face',
      bio: 'Tech innovator creating digital solutions that enhance the travel planning and experience process.',
      experience: '8 years in travel tech'
    }
  ]

  const milestones = [
    {
      year: '2009',
      title: 'The Beginning',
      description: 'Amina starts guiding small groups of travelers through Marrakech, focusing on authentic cultural experiences.'
    },
    {
      year: '2012',
      title: 'First Team',
      description: 'Expansion to include 5 expert local guides across Marrakech, Fes, and the Atlas Mountains.'
    },
    {
      year: '2015',
      title: 'Digital Platform',
      description: 'Launch of our first digital platform connecting travelers with vetted local experts.'
    },
    {
      year: '2018',
      title: 'National Coverage',
      description: 'Network grows to cover all major destinations across Morocco with 50+ expert guides.'
    },
    {
      year: '2021',
      title: 'Sustainability Focus',
      description: 'Launch of our sustainable tourism initiative supporting local communities and conservation.'
    },
    {
      year: '2024',
      title: 'Innovation Leader',
      description: 'Introduction of AI-powered trip planning and immersive cultural experience platform.'
    }
  ]

  const testimonials = [
    {
      text: "ComeToMorocco didn't just show us Morocco - they helped us understand it. The cultural insights and connections we made will last a lifetime.",
      author: "Sarah & Michael Johnson",
      location: "California, USA",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100&h=100&fit=crop&crop=face"
    },
    {
      text: "The level of personalization and attention to detail was extraordinary. Our guide became like family, sharing stories and traditions that no guidebook could capture.",
      author: "Emma Rodriguez",
      location: "Madrid, Spain", 
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?w=100&h=100&fit=crop&crop=face"
    },
    {
      text: "As a solo female traveler, I felt completely safe and empowered. The team went above and beyond to ensure I had an authentic yet comfortable experience.",
      author: "Lisa Chen",
      location: "Toronto, Canada",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100&h=100&fit=crop&crop=face"
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-primary-50 via-white to-sand-50">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="heading-xl text-neutral-900 mb-6">
              Bridging Cultures,
              <span className="block bg-gradient-to-r from-primary-500 to-morocco-500 bg-clip-text text-transparent">
                Creating Connections
              </span>
            </h1>
            <p className="text-body-lg text-neutral-600 mb-8">
              For over 15 years, we've been more than a travel company. We're cultural ambassadors, 
              storytellers, and bridge-builders connecting travelers with the authentic heart of Morocco.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <button className="btn-primary btn-lg">
                <Play className="mr-3 h-5 w-5" />
                Watch Our Story
              </button>
              <button className="btn-secondary btn-lg">
                Meet Our Team
                <ArrowRight className="ml-3 h-5 w-5" />
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="container-custom">
          <div className="grid md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="w-8 h-8 text-primary-600" />
                </div>
                <div className="text-3xl font-bold text-neutral-900 mb-2">{stat.number}</div>
                <div className="text-neutral-600">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Tab Navigation */}
      <section className="py-8 bg-white border-b border-neutral-200">
        <div className="container-custom">
          <EnhancedTabNavigation
            tabs={tabs}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            variant="underline"
            className="justify-center"
          />
        </div>
      </section>

      {/* Tab Content */}
      <div className="min-h-screen">
        {activeTab === 'story' && (
          <section className="section-padding bg-gradient-to-b from-neutral-50 to-white">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="heading-lg text-neutral-900 mb-6">Our Story</h2>
              <div className="space-y-6 text-body text-neutral-600">
                <p>
                  It started with a simple belief: that travel should transform you, not just transport you. 
                  In 2009, Amina Benali began guiding small groups through the winding alleys of Marrakech, 
                  sharing not just the sights, but the soul of Morocco.
                </p>
                <p>
                  What began as intimate walking tours has grown into Morocco's most trusted cultural travel platform. 
                  But our mission remains unchanged: to create authentic connections between travelers and the 
                  rich tapestry of Moroccan culture.
                </p>
                <p>
                  Today, our network of expert local guides doesn't just show you Morocco—they welcome you into it. 
                  Every experience is designed to go beyond the surface, revealing the stories, traditions, 
                  and genuine warmth that make Morocco truly magical.
                </p>
              </div>
              
              <div className="mt-8 p-6 bg-primary-50 rounded-2xl">
                <Quote className="w-8 h-8 text-primary-500 mb-4" />
                <p className="text-primary-900 font-medium italic">
                  "We don't just want you to visit Morocco. We want Morocco to visit you—to touch your heart, 
                  expand your mind, and leave you forever changed."
                </p>
                <p className="text-primary-700 text-sm mt-3">— Amina Benali, Founder</p>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <img
                src="https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=600&h=800&fit=crop"
                alt="Morocco landscape"
                className="w-full h-96 object-cover rounded-2xl shadow-lg"
              />
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-white rounded-2xl shadow-lg p-4 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600">15+</div>
                  <div className="text-xs text-neutral-600">Years of Excellence</div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
          </section>
        )}

        {activeTab === 'values' && (
          <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="heading-lg text-neutral-900 mb-6">Our Values</h2>
            <p className="text-body-lg text-neutral-600 max-w-3xl mx-auto">
              These principles guide everything we do, from selecting our guides to designing experiences 
              that honor Morocco's rich cultural heritage.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="card-feature text-center"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center mx-auto mb-6">
                  <value.icon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="heading-md text-neutral-900 mb-4">{value.title}</h3>
                <p className="text-body text-neutral-600">{value.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
          </section>
        )}

        {activeTab === 'timeline' && (
          <section className="section-padding bg-gradient-to-b from-neutral-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="heading-lg text-neutral-900 mb-6">Our Journey</h2>
            <p className="text-body-lg text-neutral-600 max-w-3xl mx-auto">
              From humble beginnings to becoming Morocco's leading cultural travel platform, 
              here are the key milestones in our story.
            </p>
          </motion.div>

          <div className="relative max-w-4xl mx-auto">
            {/* Timeline Line */}
            <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-500 to-sand-500" />
            
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="relative flex items-start gap-8"
                >
                  {/* Timeline Dot */}
                  <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold shadow-lg">
                    {milestone.year}
                  </div>
                  
                  {/* Content */}
                  <div className="flex-1 card-feature">
                    <h3 className="heading-md text-neutral-900 mb-2">{milestone.title}</h3>
                    <p className="text-body text-neutral-600">{milestone.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
          </section>
        )}

        {activeTab === 'team' && (
          <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="heading-lg text-neutral-900 mb-6">Meet Our Team</h2>
            <p className="text-body-lg text-neutral-600 max-w-3xl mx-auto">
              The passionate individuals behind ComeToMorocco, dedicated to sharing the authentic beauty 
              and culture of Morocco with the world.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="card-feature text-center group"
              >
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-24 h-24 rounded-full mx-auto mb-6 ring-4 ring-white shadow-lg group-hover:scale-105 transition-transform duration-300"
                />
                <h3 className="heading-md text-neutral-900 mb-1">{member.name}</h3>
                <p className="text-primary-600 font-medium mb-3">{member.role}</p>
                <p className="text-body text-neutral-600 mb-3">{member.bio}</p>
                <p className="text-sm text-neutral-500">{member.experience}</p>
              </motion.div>
            ))}
          </div>
        </div>
          </section>
        )}

        {activeTab === 'testimonials' && (
          <section className="section-padding bg-gradient-to-b from-neutral-50 to-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="heading-lg text-neutral-900 mb-6">What Travelers Say</h2>
            <p className="text-body-lg text-neutral-600 max-w-3xl mx-auto">
              The real measure of our success is in the stories and memories our travelers take home.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="card"
              >
                <Quote className="w-8 h-8 text-primary-500 mb-4" />
                <p className="text-neutral-700 mb-6 italic">"{testimonial.text}"</p>
                <div className="flex items-center gap-3">
                  <img
                    src={testimonial.image}
                    alt={testimonial.author}
                    className="w-12 h-12 rounded-full"
                  />
                  <div>
                    <p className="font-medium text-neutral-900">{testimonial.author}</p>
                    <p className="text-sm text-neutral-500">{testimonial.location}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
          </section>
        )}
      </div>

      {/* CTA Section */}
      <section className="section-padding bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div className="container-custom text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="heading-lg text-white mb-6">Ready to Experience Morocco?</h2>
            <p className="text-body-lg text-primary-100 mb-8 max-w-2xl mx-auto">
              Join thousands of travelers who have discovered the authentic heart of Morocco with us. 
              Your transformative journey awaits.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="btn-secondary btn-lg">
                <Compass className="mr-3 h-5 w-5" />
                Start Planning
              </button>
              <button className="btn-primary btn-lg bg-white text-primary-600 hover:bg-neutral-100">
                <Users className="mr-3 h-5 w-5" />
                Meet Our Agents
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  )
}