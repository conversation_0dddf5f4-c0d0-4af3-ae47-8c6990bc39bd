import { Metadata } from 'next'
import Link from 'next/link'
import {
  Globe,
  MapPin,
  Calendar,
  Shield,
  Palette,
  Zap,
  ExternalLink,
  Home,
  Building,
  User,
  Code,
  Database
} from 'lucide-react'

export const metadata: Metadata = {
  title: 'Routes Directory - Ex-plore Development',
  description: 'Complete directory of all routes and URLs in the Ex-plore international platform for development and testing.',
  robots: {
    index: false,
    follow: false,
  },
}

interface RouteGroup {
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  routes: Route[]
}

interface Route {
  path: string
  name: string
  description: string
  status: 'active' | 'redirect' | 'coming-soon' | 'legacy'
  redirectsTo?: string
}

export default function RoutesPage() {
  const routeGroups: RouteGroup[] = [
    {
      title: 'Homepage & Core',
      description: 'Main entry points and core platform pages',
      icon: Home,
      routes: [
        {
          path: '/',
          name: 'Homepage',
          description: 'Main landing page with country selection',
          status: 'active'
        },
        {
          path: '/explore',
          name: 'Explore Morocco',
          description: 'Interactive exploration and discovery page',
          status: 'active'
        },
        {
          path: '/search',
          name: 'Search',
          description: 'Global search functionality',
          status: 'active'
        },
        {
          path: '/about',
          name: 'About Us',
          description: 'Company information and mission',
          status: 'active'
        },
        {
          path: '/contact',
          name: 'Contact',
          description: 'Contact forms and information',
          status: 'active'
        },
        {
          path: '/help',
          name: 'Help Center',
          description: 'FAQ and support documentation',
          status: 'active'
        },
        {
          path: '/routes',
          name: 'Routes Directory',
          description: 'This page - complete directory of all app routes',
          status: 'active'
        }
      ]
    },
    {
      title: 'International Platform Foundation',
      description: 'New international platform features (Task 7 implementation)',
      icon: Globe,
      routes: [
        {
          path: '/countries',
          name: 'Country Selection',
          description: 'Choose your travel destination country',
          status: 'active'
        },
        {
          path: '/morocco',
          name: 'Morocco Homepage',
          description: 'Morocco country homepage (international structure)',
          status: 'active'
        },
        {
          path: '/morocco/explore',
          name: 'Morocco Explore',
          description: 'Interactive Morocco exploration with country context',
          status: 'active'
        },
        {
          path: '/morocco/experiences',
          name: 'Morocco Experiences',
          description: 'Morocco-specific experiences and activities',
          status: 'active'
        },
        {
          path: '/morocco/destinations',
          name: 'Morocco Destinations',
          description: 'Morocco destinations overview',
          status: 'active'
        },
        {
          path: '/morocco/destinations/marrakech',
          name: 'Morocco - Marrakech',
          description: 'Marrakech in Morocco international context',
          status: 'active'
        },
        {
          path: '/morocco/destinations/fes',
          name: 'Morocco - Fes',
          description: 'Fes in Morocco international context',
          status: 'active'
        },
        {
          path: '/morocco/destinations/casablanca',
          name: 'Morocco - Casablanca',
          description: 'Casablanca in Morocco international context',
          status: 'active'
        },
        {
          path: '/morocco/destinations/essaouira',
          name: 'Morocco - Essaouira',
          description: 'Essaouira in Morocco international context',
          status: 'active'
        },
        {
          path: '/morocco/destinations/chefchaouen',
          name: 'Morocco - Chefchaouen',
          description: 'Chefchaouen in Morocco international context',
          status: 'active'
        },
        {
          path: '/morocco/destinations/sahara',
          name: 'Morocco - Sahara',
          description: 'Sahara Desert in Morocco international context',
          status: 'active'
        },
        {
          path: '/morocco/cultural-immersion',
          name: 'Morocco Cultural',
          description: 'Morocco cultural immersion experiences',
          status: 'active'
        },
        {
          path: '/international-demo',
          name: 'Platform Demo',
          description: 'Interactive demo of international platform features',
          status: 'active'
        },
        {
          path: '/japan',
          name: 'Japan Homepage',
          description: 'Japan country homepage (template ready)',
          status: 'coming-soon'
        },
        {
          path: '/italy',
          name: 'Italy Homepage',
          description: 'Italy country homepage (template ready)',
          status: 'coming-soon'
        }
      ]
    },
    {
      title: 'Destinations & Travel',
      description: 'Morocco destinations with enhanced Airial components',
      icon: MapPin,
      routes: [
        {
          path: '/destinations',
          name: 'All Destinations',
          description: 'Morocco destinations overview with filtering',
          status: 'active'
        },
        {
          path: '/destinations/marrakech',
          name: 'Marrakech - The Red City',
          description: 'Enhanced activity cards integrated',
          status: 'active'
        },
        {
          path: '/destinations/fes',
          name: 'Fes - The Spiritual Capital',
          description: 'Interactive storytelling with enhanced activity cards',
          status: 'active'
        },
        {
          path: '/destinations/chefchaouen',
          name: 'Chefchaouen - The Blue Pearl',
          description: 'Mountain city with enhanced activity cards',
          status: 'active'
        },
        {
          path: '/destinations/sahara',
          name: 'Sahara Desert',
          description: 'Immersive desert experience',
          status: 'active'
        },
        {
          path: '/destinations/essaouira',
          name: 'Essaouira - The Windy City',
          description: 'Coastal destination with enhanced activity cards',
          status: 'active'
        },
        {
          path: '/destinations/casablanca',
          name: 'Casablanca - Economic Heart',
          description: 'Modern Morocco with enhanced activity cards',
          status: 'active'
        },
        {
          path: '/experiences',
          name: 'All Experiences',
          description: 'Browse activities with enhanced tab navigation',
          status: 'active'
        },
        {
          path: '/experiences/example-id',
          name: 'Experience Details',
          description: 'Individual experience pages with booking (dynamic route)',
          status: 'active'
        },
        {
          path: '/cultural-immersion',
          name: 'Cultural Learning',
          description: 'Cultural education with progressive disclosure',
          status: 'active'
        },
        {
          path: '/agents',
          name: 'Local Agents',
          description: 'Browse and connect with local travel agents',
          status: 'active'
        },
        {
          path: '/agents/example-id',
          name: 'Agent Profile',
          description: 'Individual agent profiles and booking (dynamic route)',
          status: 'active'
        },
        {
          path: '/destinations/fes',
          name: 'Fes',
          description: 'Imperial City - detailed destination page',
          status: 'active'
        },
        {
          path: '/destinations/casablanca',
          name: 'Casablanca',
          description: 'Modern Morocco - destination page',
          status: 'active'
        },
        {
          path: '/destinations/chefchaouen',
          name: 'Chefchaouen',
          description: 'The Blue Pearl - destination page',
          status: 'active'
        },
        {
          path: '/destinations/essaouira',
          name: 'Essaouira',
          description: 'Coastal charm - destination page',
          status: 'active'
        },
        {
          path: '/destinations/sahara',
          name: 'Sahara Desert',
          description: 'Desert adventure - destination page',
          status: 'active'
        },
        {
          path: '/experiences',
          name: 'Experiences',
          description: 'Authentic cultural activities and experiences',
          status: 'active'
        },
        {
          path: '/cultural-immersion',
          name: 'Cultural Learning',
          description: 'Learn Moroccan customs, traditions, and etiquette',
          status: 'active'
        },
        {
          path: '/explore',
          name: 'Trip Planning',
          description: 'Interactive trip planning and exploration tools',
          status: 'active'
        }
      ]
    },
    {
      title: 'Trip Planning & User Management',
      description: 'Trip building, user dashboards, and account management',
      icon: Calendar,
      routes: [
        {
          path: '/trip-builder',
          name: 'Trip Builder',
          description: 'Interactive trip planning with timeline trip planner',
          status: 'active'
        },
        {
          path: '/dashboard',
          name: 'User Dashboard',
          description: 'Personal dashboard with visual trip overview',
          status: 'active'
        },
        {
          path: '/agent-dashboard',
          name: 'Agent Dashboard',
          description: 'Dashboard for travel agents and partners',
          status: 'active'
        },
        {
          path: '/login',
          name: 'Login',
          description: 'User authentication page',
          status: 'active'
        },
        {
          path: '/signup',
          name: 'Sign Up',
          description: 'User registration page',
          status: 'active'
        },
        {
          path: '/trip-builder',
          name: 'Trip Builder',
          description: 'Interactive trip planning dashboard',
          status: 'active'
        },
        {
          path: '/dashboard',
          name: 'User Dashboard',
          description: 'Personal user dashboard and trip management',
          status: 'active'
        }
      ]
    },
    {
      title: 'Development & Demo Pages',
      description: 'Development tools, demos, and testing pages',
      icon: Code,
      routes: [
        {
          path: '/ui-showcase',
          name: 'UI Component Showcase',
          description: 'Interactive showcase of all Airial-inspired components',
          status: 'active'
        },
        {
          path: '/api-demo',
          name: 'API Integration Demo',
          description: 'Demonstration of external API integrations',
          status: 'active'
        },
        {
          path: '/live-enhancement',
          name: 'Live Enhancement Features',
          description: 'Real-time features and live data demonstration',
          status: 'active'
        },
        {
          path: '/international-demo',
          name: 'International Platform Demo',
          description: 'Multi-country platform demonstration',
          status: 'active'
        },
        {
          path: '/login',
          name: 'Sign In',
          description: 'User login page',
          status: 'active'
        },
        {
          path: '/signup',
          name: 'Sign Up',
          description: 'User registration page',
          status: 'active'
        }
      ]
    },
    {
      title: 'Admin & Management',
      description: 'Administrative interfaces and management tools',
      icon: Shield,
      routes: [
        {
          path: '/admin',
          name: 'Admin Dashboard',
          description: 'Main administrative dashboard',
          status: 'active'
        },
        {
          path: '/admin/content',
          name: 'Content Management',
          description: 'Manage destinations, experiences, and content',
          status: 'active'
        },
        {
          path: '/agent-dashboard',
          name: 'Agent Dashboard',
          description: 'Local agent and guide management interface',
          status: 'active'
        },
        {
          path: '/agents',
          name: 'Agents Directory',
          description: 'Browse and manage local agents and guides',
          status: 'active'
        }
      ]
    },
    {
      title: 'Features & Enhancements',
      description: 'Advanced features and platform enhancements',
      icon: Zap,
      routes: [
        {
          path: '/live-enhancement',
          name: 'Live Enhancement',
          description: 'Real-time features and live enhancements',
          status: 'active'
        },
        {
          path: '/ui-showcase',
          name: 'UI Components',
          description: 'Showcase of all UI components and design system',
          status: 'active'
        }
      ]
    },
    {
      title: 'Static Pages',
      description: 'Information and support pages',
      icon: Building,
      routes: [
        {
          path: '/about',
          name: 'About Us',
          description: 'About Ex-plore International platform',
          status: 'active'
        },
        {
          path: '/contact',
          name: 'Contact',
          description: 'Contact information and support',
          status: 'active'
        },
        {
          path: '/help',
          name: 'Help Center',
          description: 'Help documentation and FAQs',
          status: 'active'
        }
      ]
    },
    {
      title: 'API Endpoints',
      description: 'Backend API routes and integrations',
      icon: Database,
      routes: [
        {
          path: '/api/integrations/recommendations',
          name: 'Recommendations API',
          description: 'AI-powered travel recommendations endpoint',
          status: 'active'
        },
        {
          path: '/api/integrations/analytics',
          name: 'Analytics API',
          description: 'User behavior and platform analytics endpoint',
          status: 'active'
        },
        {
          path: '/api/bookings/receipt',
          name: 'Booking Receipt API',
          description: 'Generate booking receipts and confirmations',
          status: 'active'
        },
        {
          path: '/api/webhooks/stripe',
          name: 'Stripe Webhooks',
          description: 'Handle Stripe payment webhooks',
          status: 'active'
        }
      ]
    }
  ]

  const getStatusColor = (status: Route['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'redirect':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'coming-soon':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'legacy':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: Route['status']) => {
    switch (status) {
      case 'active':
        return '✅'
      case 'redirect':
        return '↗️'
      case 'coming-soon':
        return '🚧'
      case 'legacy':
        return '📦'
      default:
        return '❓'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100">
      <div className="container-custom py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-neutral-900 mb-4">
            🗺️ Routes Directory
          </h1>
          <p className="text-lg text-neutral-600 max-w-3xl mx-auto">
            Complete directory of all routes and URLs in the Ex-plore international platform.
            Use this page during development to easily access and test all features.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
          <div className="bg-white rounded-xl p-4 shadow-sm text-center">
            <div className="text-2xl font-bold text-green-600">
              {routeGroups.reduce((acc, group) => acc + group.routes.filter(r => r.status === 'active').length, 0)}
            </div>
            <div className="text-sm text-neutral-600">Active Routes</div>
          </div>
          <div className="bg-white rounded-xl p-4 shadow-sm text-center">
            <div className="text-2xl font-bold text-blue-600">
              {routeGroups.reduce((acc, group) => acc + group.routes.filter(r => r.status === 'redirect').length, 0)}
            </div>
            <div className="text-sm text-neutral-600">Redirects</div>
          </div>
          <div className="bg-white rounded-xl p-4 shadow-sm text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {routeGroups.reduce((acc, group) => acc + group.routes.filter(r => r.status === 'coming-soon').length, 0)}
            </div>
            <div className="text-sm text-neutral-600">Coming Soon</div>
          </div>
          <div className="bg-white rounded-xl p-4 shadow-sm text-center">
            <div className="text-2xl font-bold text-neutral-600">
              {routeGroups.reduce((acc, group) => acc + group.routes.length, 0)}
            </div>
            <div className="text-sm text-neutral-600">Total Routes</div>
          </div>
        </div>

        {/* Route Groups */}
        <div className="space-y-8">
          {routeGroups.map((group, groupIndex) => {
            const Icon = group.icon
            return (
              <div key={groupIndex} className="bg-white rounded-2xl shadow-lg overflow-hidden">
                {/* Group Header */}
                <div className="bg-gradient-to-r from-neutral-900 to-neutral-700 p-6 text-white">
                  <div className="flex items-center gap-3 mb-2">
                    <Icon className="w-6 h-6" />
                    <h2 className="text-2xl font-bold">{group.title}</h2>
                  </div>
                  <p className="text-neutral-200">{group.description}</p>
                </div>

                {/* Routes List */}
                <div className="p-6">
                  <div className="grid gap-4">
                    {group.routes.map((route, routeIndex) => (
                      <div
                        key={routeIndex}
                        className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg hover:shadow-md transition-shadow"
                      >
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <span className="text-lg">{getStatusIcon(route.status)}</span>
                            <h3 className="font-semibold text-neutral-900">{route.name}</h3>
                            <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(route.status)}`}>
                              {route.status.replace('-', ' ')}
                            </span>
                          </div>
                          <p className="text-sm text-neutral-600 mb-2">{route.description}</p>
                          <code className="text-xs bg-neutral-100 px-2 py-1 rounded text-neutral-700">
                            {route.path}
                          </code>
                          {route.redirectsTo && (
                            <div className="mt-2">
                              <span className="text-xs text-blue-600">
                                → Redirects to: <code className="bg-blue-50 px-1 py-0.5 rounded">{route.redirectsTo}</code>
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2 ml-4">
                          {route.status === 'active' && (
                            <Link
                              href={route.path}
                              className="flex items-center gap-1 px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-sm"
                            >
                              Visit
                              <ExternalLink className="w-3 h-3" />
                            </Link>
                          )}
                          {route.status === 'redirect' && route.redirectsTo && (
                            <Link
                              href={route.redirectsTo}
                              className="flex items-center gap-1 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                            >
                              Visit New
                              <ExternalLink className="w-3 h-3" />
                            </Link>
                          )}
                          {route.status === 'coming-soon' && (
                            <span className="px-3 py-2 bg-neutral-200 text-neutral-500 rounded-lg text-sm cursor-not-allowed">
                              Coming Soon
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Development Notes */}
        <div className="mt-12 bg-blue-50 rounded-2xl p-8 border border-blue-200">
          <h3 className="text-xl font-bold text-blue-900 mb-4">
            🛠️ Development Notes
          </h3>
          <div className="grid md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-semibold text-blue-800 mb-2">Current Status</h4>
              <ul className="space-y-1 text-blue-700">
                <li>• All existing pages work exactly as before</li>
                <li>• International platform foundation is implemented</li>
                <li>• Country context system is available for components</li>
                <li>• Ready for future country expansion</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold text-blue-800 mb-2">Task 7 Completed</h4>
              <ul className="space-y-1 text-blue-700">
                <li>• ✅ Country selection system (/countries)</li>
                <li>• ✅ Country context & theming framework</li>
                <li>• ✅ SEO optimization for international</li>
                <li>• ✅ Scalable architecture foundation</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-100 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">🎯 What Was Implemented</h4>
            <p className="text-blue-700 text-sm">
              <strong>Task 7: Build International Platform Foundation</strong> - The foundation for multi-country
              support is now complete. All existing Morocco pages continue to work unchanged, while the new
              international structure is ready for future expansion to other countries.
            </p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 text-center">
          <div className="flex flex-wrap justify-center gap-4">
            <Link
              href="/international-demo"
              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-primary-600 to-teal-600 text-white rounded-lg hover:opacity-90 transition-opacity"
            >
              <Palette className="w-4 h-4" />
              Platform Demo
            </Link>
            <Link
              href="/countries"
              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-orange-500 to-red-600 text-white rounded-lg hover:opacity-90 transition-opacity"
            >
              <Globe className="w-4 h-4" />
              Country Selection
            </Link>
            <Link
              href="/ui-showcase"
              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-600 text-white rounded-lg hover:opacity-90 transition-opacity"
            >
              <Code className="w-4 h-4" />
              UI Components
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}