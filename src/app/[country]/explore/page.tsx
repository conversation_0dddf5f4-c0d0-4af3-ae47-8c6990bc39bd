'use client'

import { useEffect } from 'react'
import { useCountryContext } from '@/lib/country-context'
import ExplorePage from '@/app/explore/page'

interface CountryExplorePageProps {
  params: { country: string }
}

export default function CountryExplorePage({ params }: CountryExplorePageProps) {
  const { currentCountry, availableCountries, switchCountry } = useCountryContext()

  // Update country context based on URL parameter
  useEffect(() => {
    if (availableCountries.length > 0) {
      const targetCountry = availableCountries.find(c =>
        c.code.toLowerCase() === params.country.toLowerCase() ||
        c.name.toLowerCase() === params.country.toLowerCase()
      )

      // Switch to the country from URL if different from current and if it exists
      if (targetCountry && currentCountry.code.toLowerCase() !== params.country.toLowerCase()) {
        switchCountry(params.country)
      }
    }
  }, [params.country, availableCountries, currentCountry, switchCountry])

  // Render the original beautiful explore page
  // The country context will automatically provide the right country data
  return <ExplorePage />
}