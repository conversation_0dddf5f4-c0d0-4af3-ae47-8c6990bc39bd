'use client'

import { useEffect } from 'react'
import { notFound } from 'next/navigation'
import { useCountryContext } from '@/lib/country-context'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'

interface CountryLayoutProps {
  children: React.ReactNode
  params: { country: string }
}

export default function CountryLayout({ children, params }: CountryLayoutProps) {
  const { availableCountries, switchCountry, currentCountry, isLoading } = useCountryContext()
  
  useEffect(() => {
    if (!isLoading && availableCountries.length > 0) {
      const countryExists = availableCountries.find(
        c => c.code.toLowerCase() === params.country.toLowerCase() ||
            c.name.toLowerCase() === params.country.toLowerCase()
      )

      // Switch to the country from URL if different from current
      if (currentCountry.code.toLowerCase() !== params.country.toLowerCase()) {
        // Only switch if the country exists in available countries
        if (countryExists) {
          switchCountry(params.country)
        }
      }
    }
  }, [params.country, availableCountries, switchCountry, currentCountry, isLoading])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {children}
    </div>
  )
}