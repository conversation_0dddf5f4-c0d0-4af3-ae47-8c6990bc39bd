import { Metadata } from 'next'
import { TikTokStyleDiscovery } from '@/components/discovery/tiktok-style-discovery'
import { getCountryByCode } from '@/lib/countries'
import { notFound } from 'next/navigation'

interface DiscoverPageProps {
  params: {
    country: string
  }
  searchParams: {
    category?: string
    autoplay?: string
  }
}

export async function generateMetadata({ params }: DiscoverPageProps): Promise<Metadata> {
  const country = getCountryByCode(params.country.toUpperCase())
  
  if (!country) {
    return {
      title: 'Discover - Come to Morocco',
      description: 'Discover amazing travel content and experiences'
    }
  }

  return {
    title: `Discover ${country.displayName} - Come to Morocco`,
    description: `Discover amazing travel content, experiences, and authentic moments from ${country.displayName}. Get inspired by real travelers and local creators.`,
    keywords: [
      `${country.displayName} travel`,
      'travel discovery',
      'travel inspiration',
      'authentic experiences',
      'travel content',
      'social travel',
      'travel videos',
      'travel photography'
    ],
    openGraph: {
      title: `Discover ${country.displayName} - Come to Morocco`,
      description: `Discover amazing travel content and authentic experiences from ${country.displayName}`,
      type: 'website',
      locale: 'en_US',
      siteName: 'Come to Morocco'
    },
    twitter: {
      card: 'summary_large_image',
      title: `Discover ${country.displayName} - Come to Morocco`,
      description: `Discover amazing travel content and authentic experiences from ${country.displayName}`
    }
  }
}

export default function DiscoverPage({ params, searchParams }: DiscoverPageProps) {
  const country = getCountryByCode(params.country.toUpperCase())
  
  if (!country) {
    notFound()
  }

  const autoPlay = searchParams.autoplay !== 'false'
  const category = searchParams.category

  return (
    <div className="min-h-screen bg-black">
      {/* Full-screen TikTok-style discovery interface */}
      <TikTokStyleDiscovery 
        category={category}
        autoPlay={autoPlay}
        className="w-full h-screen"
      />
      
      {/* Hidden content for SEO */}
      <div className="sr-only">
        <h1>Discover {country.displayName}</h1>
        <p>
          Explore authentic travel content from {country.displayName}. Watch videos, 
          see photos, and get inspired by real travelers and local creators sharing 
          their experiences, tips, and hidden gems.
        </p>
        
        <h2>What You'll Discover</h2>
        <ul>
          <li>Authentic travel experiences and moments</li>
          <li>Local insights from verified creators</li>
          <li>Hidden gems and secret spots</li>
          <li>Cultural experiences and traditions</li>
          <li>Food and culinary adventures</li>
          <li>Photography and scenic locations</li>
          <li>Adventure and outdoor activities</li>
          <li>Nightlife and entertainment</li>
        </ul>
        
        <h2>Featured Categories</h2>
        <ul>
          <li>Nature & Adventure</li>
          <li>Food & Culture</li>
          <li>Photography</li>
          <li>Culture & Nightlife</li>
          <li>Hidden Gems</li>
          <li>Local Experiences</li>
        </ul>
      </div>
    </div>
  )
}
