import { Metadata } from 'next'
import { TikTokStyleDiscovery } from '@/components/discovery/tiktok-style-discovery'
import { notFound } from 'next/navigation'

interface DiscoverPageProps {
  params: {
    country: string
  }
  searchParams: {
    category?: string
    autoplay?: string
  }
}

export async function generateMetadata({ params }: DiscoverPageProps): Promise<Metadata> {
  // For now, use a simple country name mapping since we can't access context in metadata
  const countryNames: Record<string, string> = {
    'ma': 'Morocco',
    'jp': 'Japan',
    'es': 'Spain',
    'it': 'Italy'
  }

  const countryName = countryNames[params.country.toLowerCase()] || 'Morocco'

  return {
    title: `Discover ${countryName} - Come to Morocco`,
    description: `Discover amazing travel content, experiences, and authentic moments from ${countryName}. Get inspired by real travelers and local creators.`,
    keywords: [
      `${countryName} travel`,
      'travel discovery',
      'travel inspiration',
      'authentic experiences',
      'travel content',
      'social travel',
      'travel videos',
      'travel photography'
    ],
    openGraph: {
      title: `Discover ${countryName} - Come to Morocco`,
      description: `Discover amazing travel content and authentic experiences from ${countryName}`,
      type: 'website',
      locale: 'en_US',
      siteName: 'Come to Morocco'
    },
    twitter: {
      card: 'summary_large_image',
      title: `Discover ${countryName} - Come to Morocco`,
      description: `Discover amazing travel content and authentic experiences from ${countryName}`
    }
  }
}

export default function DiscoverPage({ params, searchParams }: DiscoverPageProps) {
  // Simple validation - check if country code is valid
  const validCountries = ['ma', 'jp', 'es', 'it']
  const countryCode = params.country.toLowerCase()

  if (!validCountries.includes(countryCode)) {
    notFound()
  }

  const autoPlay = searchParams.autoplay !== 'false'
  const category = searchParams.category

  // Country name mapping for SEO content
  const countryNames: Record<string, string> = {
    'ma': 'Morocco',
    'jp': 'Japan',
    'es': 'Spain',
    'it': 'Italy'
  }

  const countryName = countryNames[countryCode] || 'Morocco'

  return (
    <div className="min-h-screen bg-black">
      {/* Full-screen TikTok-style discovery interface */}
      <TikTokStyleDiscovery
        category={category}
        autoPlay={autoPlay}
        className="w-full h-screen"
      />

      {/* Hidden content for SEO */}
      <div className="sr-only">
        <h1>Discover {countryName}</h1>
        <p>
          Explore authentic travel content from {countryName}. Watch videos,
          see photos, and get inspired by real travelers and local creators sharing
          their experiences, tips, and hidden gems.
        </p>
        
        <h2>What You'll Discover</h2>
        <ul>
          <li>Authentic travel experiences and moments</li>
          <li>Local insights from verified creators</li>
          <li>Hidden gems and secret spots</li>
          <li>Cultural experiences and traditions</li>
          <li>Food and culinary adventures</li>
          <li>Photography and scenic locations</li>
          <li>Adventure and outdoor activities</li>
          <li>Nightlife and entertainment</li>
        </ul>
        
        <h2>Featured Categories</h2>
        <ul>
          <li>Nature & Adventure</li>
          <li>Food & Culture</li>
          <li>Photography</li>
          <li>Culture & Nightlife</li>
          <li>Hidden Gems</li>
          <li>Local Experiences</li>
        </ul>
      </div>
    </div>
  )
}
