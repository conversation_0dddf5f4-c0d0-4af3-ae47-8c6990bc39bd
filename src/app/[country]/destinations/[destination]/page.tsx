'use client'

import { useState, useEffect } from 'react'
import { notFound } from 'next/navigation'
import { useCountryContext } from '@/lib/country-context'

// Import the original destination page components based on the destination
import MarrakechPage from '@/app/destinations/marrakech/page'
import FesPage from '@/app/destinations/fes/page'
import ChefchaouenPage from '@/app/destinations/chefchaouen/page'
import SaharaPage from '@/app/destinations/sahara/page'
import EssaouiraPage from '@/app/destinations/essaouira/page'
import CasablancaPage from '@/app/destinations/casablanca/page'

interface CountryDestinationPageProps {
  params: {
    country: string
    destination: string
  }
}

export default function CountryDestinationPage({ params }: CountryDestinationPageProps) {
  const { currentCountry, availableCountries } = useCountryContext()
  const [countryData, setCountryData] = useState<any>(null)

  const validDestinations = [
    'marrakech', 'fes', 'chefchaouen', 'sahara', 'essaouira', 'casablanca'
  ]

  // Get country-specific data
  useEffect(() => {
    const loadCountryData = async () => {
      try {
        // Find the country context based on the URL parameter
        const targetCountry = availableCountries.find(c =>
          c.code.toLowerCase() === params.country.toLowerCase() ||
          c.name.toLowerCase() === params.country.toLowerCase()
        ) || currentCountry

        setCountryData(targetCountry)
      } catch (error) {
        console.error('Failed to load country data:', error)
        setCountryData(currentCountry)
      }
    }

    if (availableCountries.length > 0) {
      loadCountryData()
    }
  }, [params.country, availableCountries, currentCountry])

  if (!validDestinations.includes(params.destination)) {
    notFound()
  }

  // Render the appropriate destination page component
  // This preserves all the original beautiful design and functionality
  switch (params.destination) {
    case 'marrakech':
      return <MarrakechPage />
    case 'fes':
      return <FesPage />
    case 'chefchaouen':
      return <ChefchaouenPage />
    case 'sahara':
      return <SaharaPage />
    case 'essaouira':
      return <EssaouiraPage />
    case 'casablanca':
      return <CasablancaPage />
    default:
      notFound()
  }
}