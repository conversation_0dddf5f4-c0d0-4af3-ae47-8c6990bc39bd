'use client'

import { useState, useEffect } from 'react'
import { notFound } from 'next/navigation'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { useCountryContext } from '@/lib/country-context'
import { LoadingSpinner } from '@/components/ui/loading-states'

interface CountryDestinationPageProps {
  params: {
    country: string
    destination: string
  }
}

export default function CountryDestinationPage({ params }: CountryDestinationPageProps) {
  const { currentCountry, availableCountries } = useCountryContext()
  const [isLoading, setIsLoading] = useState(true)
  const [countryData, setCountryData] = useState<any>(null)

  const validDestinations = [
    'marrakech', 'fes', 'chefchaouen', 'sahara', 'essaouira', 'casablanca'
  ]

  // Get country-specific data
  useEffect(() => {
    const loadCountryData = async () => {
      try {
        setIsLoading(true)

        // Find the country context based on the URL parameter
        const targetCountry = availableCountries.find(c =>
          c.code.toLowerCase() === params.country.toLowerCase() ||
          c.name.toLowerCase() === params.country.toLowerCase()
        ) || currentCountry

        setCountryData(targetCountry)
      } catch (error) {
        console.error('Failed to load country data:', error)
        setCountryData(currentCountry)
      } finally {
        setIsLoading(false)
      }
    }

    if (availableCountries.length > 0) {
      loadCountryData()
    }
  }, [params.country, availableCountries, currentCountry])

  if (!validDestinations.includes(params.destination)) {
    notFound()
  }

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="min-h-screen bg-neutral-50 flex items-center justify-center">
          <div className="text-center">
            <LoadingSpinner size="lg" />
            <p className="text-neutral-600 mt-4">Loading {params.destination} in {params.country}...</p>
          </div>
        </div>
        <Footer />
      </div>
    )
  }

  const displayCountry = countryData || currentCountry
  const destinationName = params.destination.charAt(0).toUpperCase() + params.destination.slice(1)

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-primary-50 to-sand-50">
        <div className="container-custom">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="heading-xl text-neutral-900 mb-6">
              {destinationName}, {displayCountry.name}
              <span className="block bg-gradient-to-r from-primary-500 to-morocco-500 bg-clip-text text-transparent">
                Discover This Destination
              </span>
            </h1>
            <p className="text-body-lg text-neutral-600 mb-8">
              Explore {destinationName} in {displayCountry.name} with country-specific insights and experiences.
              This is the country-specific destination page for {destinationName} in {displayCountry.name} ({displayCountry.code}).
            </p>
          </div>
        </div>
      </section>

      {/* Content Section */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="text-center">
            <h2 className="heading-lg text-neutral-900 mb-8">
              {destinationName} in {displayCountry.name}
            </h2>
            <p className="text-body text-neutral-600 mb-8">
              This page is now rendering country-specific destination content instead of redirecting!
              Country: {displayCountry.code} | Destination: {params.destination}
            </p>
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h3 className="font-semibold text-green-800 mb-2">✅ Success!</h3>
              <p className="text-green-700">
                The country-specific destination routing is now working. No more redirects causing client-side exceptions!
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}