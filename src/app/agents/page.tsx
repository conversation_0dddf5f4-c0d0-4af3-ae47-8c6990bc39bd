'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { 
  Star, 
  MapPin, 
  Languages, 
  Award, 
  Heart, 
  MessageCircle, 
  Calendar,
  Users,
  Filter,
  Search,
  CheckCircle,
  Globe,
  Camera,
  Utensils,
  Mountain,
  Palette,
  Music,
  Book,
  Compass,
  Phone,
  Mail,
  ArrowRight
} from 'lucide-react'

interface Agent {
  id: string
  name: string
  avatar: string
  title: string
  location: string
  rating: number
  reviewCount: number
  yearsExperience: number
  languages: string[]
  specializations: string[]
  bio: string
  personalityTraits: string[]
  culturalExpertise: string[]
  priceRange: string
  responseTime: string
  availabilityStatus: 'available' | 'busy' | 'offline'
  totalClients: number
  repeatClientRate: number
  certifications: string[]
  interests: string[]
  travelStyle: string[]
  regions: string[]
  testimonials: {
    client: string
    text: string
    rating: number
    trip: string
  }[]
}

interface FilterState {
  specialization: string
  language: string
  region: string
  priceRange: string
  availability: string
  travelStyle: string
}

export default function AgentsPage() {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(null)
  const [filters, setFilters] = useState<FilterState>({
    specialization: '',
    language: '',
    region: '',
    priceRange: '',
    availability: '',
    travelStyle: ''
  })
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'match'>('grid')

  const agents: Agent[] = [
    {
      id: 'youssef-alami',
      name: 'Youssef Alami',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      title: 'Cultural Heritage Specialist',
      location: 'Marrakech',
      rating: 4.9,
      reviewCount: 247,
      yearsExperience: 8,
      languages: ['Arabic', 'French', 'English', 'Spanish'],
      specializations: ['Cultural Tours', 'Historical Sites', 'Traditional Crafts', 'Family Travel'],
      bio: 'Born and raised in Marrakech, I have been sharing the magic of Morocco with travelers for over 8 years. My passion lies in connecting visitors with the authentic soul of our culture through storytelling, traditional experiences, and hidden gems that only locals know.',
      personalityTraits: ['Patient', 'Storyteller', 'Cultural Bridge', 'Family-oriented'],
      culturalExpertise: ['Berber Culture', 'Islamic Architecture', 'Traditional Music', 'Culinary Heritage'],
      priceRange: '€80-120/day',
      responseTime: '< 2 hours',
      availabilityStatus: 'available',
      totalClients: 247,
      repeatClientRate: 85,
      certifications: ['Licensed Tour Guide', 'First Aid Certified', 'Cultural Heritage Specialist'],
      interests: ['Photography', 'Traditional Music', 'Cooking', 'History'],
      travelStyle: ['Cultural Immersion', 'Family-Friendly', 'Luxury', 'Authentic'],
      regions: ['Marrakech', 'Atlas Mountains', 'Essaouira', 'Ouarzazate'],
      testimonials: [
        {
          client: 'Sarah Johnson',
          text: 'Youssef made our family trip unforgettable. His stories brought every corner of Marrakech to life, and our kids were captivated throughout.',
          rating: 5,
          trip: 'Marrakech Family Adventure'
        },
        {
          client: 'Michael Chen',
          text: 'An incredible cultural guide who goes beyond the surface. Youssef\'s knowledge of Berber traditions is unmatched.',
          rating: 5,
          trip: 'Atlas Mountains Cultural Trek'
        }
      ]
    },
    {
      id: 'fatima-benali',
      name: 'Fatima Benali',
      avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=150&h=150&fit=crop&crop=face',
      title: 'Adventure & Desert Expert',
      location: 'Merzouga',
      rating: 4.8,
      reviewCount: 189,
      yearsExperience: 6,
      languages: ['Arabic', 'French', 'English'],
      specializations: ['Desert Expeditions', 'Adventure Tours', 'Photography Tours', 'Stargazing'],
      bio: 'Adventure enthusiast and desert expert. I lead expeditions into the Sahara and Atlas Mountains, creating unforgettable experiences for thrill-seekers and nature lovers. My goal is to show you the raw beauty and power of Morocco\'s landscapes.',
      personalityTraits: ['Adventurous', 'Safety-focused', 'Energetic', 'Nature lover'],
      culturalExpertise: ['Nomadic Traditions', 'Desert Survival', 'Astronomy', 'Wildlife'],
      priceRange: '€90-140/day',
      responseTime: '< 4 hours',
      availabilityStatus: 'available',
      totalClients: 189,
      repeatClientRate: 78,
      certifications: ['Mountain Guide License', 'Desert Navigation Specialist', 'Photography Guide'],
      interests: ['Rock Climbing', 'Astrophotography', 'Hiking', 'Wildlife Conservation'],
      travelStyle: ['Adventure', 'Photography', 'Solo Travel', 'Small Groups'],
      regions: ['Sahara Desert', 'Atlas Mountains', 'Fes', 'Meknes'],
      testimonials: [
        {
          client: 'Emma Rodriguez',
          text: 'Fatima is fearless and knowledgeable. Our desert expedition was challenging but incredibly rewarding thanks to her expertise.',
          rating: 5,
          trip: 'Sahara Adventure Expedition'
        },
        {
          client: 'David Park',
          text: 'Best photography guide in Morocco! Fatima knows all the secret spots and perfect lighting conditions.',
          rating: 5,
          trip: 'Atlas Mountains Photo Tour'
        }
      ]
    },
    {
      id: 'hassan-ouali',
      name: 'Hassan Ouali',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      title: 'Culinary & Arts Specialist',
      location: 'Fes',
      rating: 4.9,
      reviewCount: 156,
      yearsExperience: 12,
      languages: ['Arabic', 'French', 'English', 'Italian'],
      specializations: ['Culinary Tours', 'Artisan Workshops', 'Luxury Travel', 'Wine & Dine'],
      bio: 'With over 12 years in hospitality and cultural tourism, I specialize in Morocco\'s rich culinary heritage and traditional crafts. I believe food and art are the best ways to understand a culture\'s soul.',
      personalityTraits: ['Gourmet', 'Artistic', 'Sophisticated', 'Detail-oriented'],
      culturalExpertise: ['Moroccan Cuisine', 'Traditional Crafts', 'Wine Culture', 'Luxury Hospitality'],
      priceRange: '€100-180/day',
      responseTime: '< 1 hour',
      availabilityStatus: 'busy',
      totalClients: 156,
      repeatClientRate: 92,
      certifications: ['Culinary Guide Certified', 'Sommelier Level 2', 'Luxury Travel Specialist'],
      interests: ['Cooking', 'Wine Tasting', 'Art Collection', 'Fine Dining'],
      travelStyle: ['Luxury', 'Culinary', 'Romantic', 'Sophisticated'],
      regions: ['Fes', 'Meknes', 'Rabat', 'Casablanca'],
      testimonials: [
        {
          client: 'Isabella Martinez',
          text: 'Hassan transformed our honeymoon into a culinary journey. Every meal was a masterpiece, every experience perfectly curated.',
          rating: 5,
          trip: 'Romantic Culinary Escape'
        },
        {
          client: 'James Wilson',
          text: 'Exceptional attention to detail and incredible knowledge of Moroccan cuisine. Hassan is a true professional.',
          rating: 5,
          trip: 'Luxury Food & Wine Tour'
        }
      ]
    },
    {
      id: 'aicha-amrani',
      name: 'Aicha Amrani',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=150&h=150&fit=crop&crop=face',
      title: 'Women\'s Travel & Wellness Expert',
      location: 'Marrakech',
      rating: 4.9,
      reviewCount: 203,
      yearsExperience: 7,
      languages: ['Arabic', 'French', 'English', 'German'],
      specializations: ['Women\'s Travel', 'Wellness Retreats', 'Hammam Experiences', 'Solo Travel'],
      bio: 'I specialize in creating safe, empowering, and transformative experiences for women travelers. From wellness retreats to solo adventures, I ensure every woman feels confident and inspired during her Morocco journey.',
      personalityTraits: ['Empowering', 'Nurturing', 'Confident', 'Wellness-focused'],
      culturalExpertise: ['Women\'s Traditions', 'Wellness Practices', 'Hammam Rituals', 'Meditation'],
      priceRange: '€85-130/day',
      responseTime: '< 3 hours',
      availabilityStatus: 'available',
      totalClients: 203,
      repeatClientRate: 88,
      certifications: ['Women\'s Travel Specialist', 'Wellness Coach', 'Hammam Therapist'],
      interests: ['Yoga', 'Meditation', 'Wellness', 'Women\'s Empowerment'],
      travelStyle: ['Wellness', 'Solo Travel', 'Women\'s Groups', 'Spiritual'],
      regions: ['Marrakech', 'Essaouira', 'Atlas Mountains', 'Agadir'],
      testimonials: [
        {
          client: 'Lisa Thompson',
          text: 'Aicha made me feel so safe and empowered during my solo trip. She\'s not just a guide, she\'s a sister and mentor.',
          rating: 5,
          trip: 'Solo Women\'s Wellness Retreat'
        },
        {
          client: 'Maria Garcia',
          text: 'The perfect guide for women travelers. Aicha understands our needs and creates magical experiences.',
          rating: 5,
          trip: 'Women\'s Cultural Immersion'
        }
      ]
    }
  ]

  const specializations = ['Cultural Tours', 'Adventure Tours', 'Culinary Tours', 'Desert Expeditions', 'Photography Tours', 'Wellness Retreats', 'Family Travel', 'Luxury Travel']
  const languages = ['Arabic', 'French', 'English', 'Spanish', 'German', 'Italian']
  const regions = ['Marrakech', 'Fes', 'Sahara Desert', 'Atlas Mountains', 'Casablanca', 'Essaouira']
  const travelStyles = ['Cultural Immersion', 'Adventure', 'Luxury', 'Family-Friendly', 'Solo Travel', 'Photography', 'Culinary', 'Wellness']

  const filteredAgents = agents.filter(agent => {
    const matchesSearch = searchQuery === '' || 
      agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      agent.specializations.some(spec => spec.toLowerCase().includes(searchQuery.toLowerCase())) ||
      agent.location.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesSpecialization = filters.specialization === '' || 
      agent.specializations.includes(filters.specialization)

    const matchesLanguage = filters.language === '' || 
      agent.languages.includes(filters.language)

    const matchesRegion = filters.region === '' || 
      agent.regions.includes(filters.region)

    const matchesTravelStyle = filters.travelStyle === '' || 
      agent.travelStyle.includes(filters.travelStyle)

    const matchesAvailability = filters.availability === '' || 
      agent.availabilityStatus === filters.availability

    return matchesSearch && matchesSpecialization && matchesLanguage && 
           matchesRegion && matchesTravelStyle && matchesAvailability
  })

  const getSpecializationIcon = (specialization: string) => {
    switch (specialization.toLowerCase()) {
      case 'cultural tours': return <Book className="w-4 h-4" />
      case 'adventure tours': return <Mountain className="w-4 h-4" />
      case 'culinary tours': return <Utensils className="w-4 h-4" />
      case 'photography tours': return <Camera className="w-4 h-4" />
      case 'desert expeditions': return <Compass className="w-4 h-4" />
      case 'wellness retreats': return <Heart className="w-4 h-4" />
      default: return <Star className="w-4 h-4" />
    }
  }

  const getAvailabilityColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-700'
      case 'busy': return 'bg-yellow-100 text-yellow-700'
      case 'offline': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-primary-50 to-sand-50">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="heading-xl text-neutral-900 mb-6">
              Meet Your Perfect
              <span className="block bg-gradient-to-r from-primary-500 to-morocco-500 bg-clip-text text-transparent">
                Morocco Expert
              </span>
            </h1>
            <p className="text-body-lg text-neutral-600 mb-8">
              Connect with passionate local experts who will transform your Morocco journey 
              into an unforgettable personal adventure tailored just for you.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <div className="flex bg-neutral-100 rounded-lg p-1">
                {['grid', 'list', 'match'].map((mode) => (
                  <button
                    key={mode}
                    onClick={() => setViewMode(mode as any)}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 capitalize ${
                      viewMode === mode
                        ? 'bg-white text-primary-600 shadow-sm'
                        : 'text-neutral-600 hover:text-neutral-900'
                    }`}
                  >
                    {mode === 'match' ? 'Smart Match' : mode}
                  </button>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Search & Filters */}
      <section className="py-8 border-b border-neutral-200">
        <div className="container-custom">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
                <input
                  type="text"
                  placeholder="Search by name, specialization, or location..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="input-field pl-10"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex flex-wrap gap-3">
              <select
                value={filters.specialization}
                onChange={(e) => setFilters({...filters, specialization: e.target.value})}
                className="input-field min-w-[150px]"
              >
                <option value="">All Specializations</option>
                {specializations.map(spec => (
                  <option key={spec} value={spec}>{spec}</option>
                ))}
              </select>

              <select
                value={filters.language}
                onChange={(e) => setFilters({...filters, language: e.target.value})}
                className="input-field min-w-[120px]"
              >
                <option value="">All Languages</option>
                {languages.map(lang => (
                  <option key={lang} value={lang}>{lang}</option>
                ))}
              </select>

              <select
                value={filters.region}
                onChange={(e) => setFilters({...filters, region: e.target.value})}
                className="input-field min-w-[120px]"
              >
                <option value="">All Regions</option>
                {regions.map(region => (
                  <option key={region} value={region}>{region}</option>
                ))}
              </select>

              <select
                value={filters.availability}
                onChange={(e) => setFilters({...filters, availability: e.target.value})}
                className="input-field min-w-[120px]"
              >
                <option value="">All Availability</option>
                <option value="available">Available</option>
                <option value="busy">Busy</option>
              </select>
            </div>
          </div>
        </div>
      </section>

      {/* Agents Grid */}
      <section className="section-padding">
        <div className="container-custom">
          {viewMode === 'grid' && (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredAgents.map((agent, index) => (
                <motion.div
                  key={agent.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="card-elevated group cursor-pointer"
                  onClick={() => setSelectedAgent(agent.id)}
                >
                  {/* Agent Header */}
                  <div className="relative mb-6">
                    <img
                      src={agent.avatar}
                      alt={agent.name}
                      className="w-20 h-20 rounded-full mx-auto mb-4 ring-4 ring-white shadow-lg"
                    />
                    <div className={`absolute top-0 right-0 px-2 py-1 rounded-full text-xs font-medium ${getAvailabilityColor(agent.availabilityStatus)}`}>
                      {agent.availabilityStatus}
                    </div>
                  </div>

                  <div className="text-center mb-6">
                    <h3 className="heading-md text-neutral-900 mb-1">{agent.name}</h3>
                    <p className="text-sm text-primary-600 font-medium mb-2">{agent.title}</p>
                    <div className="flex items-center justify-center gap-1 text-sm text-neutral-500 mb-3">
                      <MapPin className="w-4 h-4" />
                      <span>{agent.location}</span>
                    </div>
                    
                    <div className="flex items-center justify-center gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="font-medium">{agent.rating}</span>
                        <span className="text-neutral-500">({agent.reviewCount})</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Award className="w-4 h-4 text-primary-500" />
                        <span>{agent.yearsExperience}y exp</span>
                      </div>
                    </div>
                  </div>

                  {/* Specializations */}
                  <div className="mb-6">
                    <div className="flex flex-wrap gap-2 justify-center">
                      {agent.specializations.slice(0, 3).map((spec, i) => (
                        <div key={i} className="flex items-center gap-1 bg-neutral-100 px-2 py-1 rounded-full text-xs">
                          {getSpecializationIcon(spec)}
                          <span>{spec}</span>
                        </div>
                      ))}
                      {agent.specializations.length > 3 && (
                        <span className="text-xs text-neutral-500">+{agent.specializations.length - 3} more</span>
                      )}
                    </div>
                  </div>

                  {/* Languages */}
                  <div className="mb-6">
                    <div className="flex items-center justify-center gap-1 text-sm text-neutral-600">
                      <Languages className="w-4 h-4" />
                      <span>{agent.languages.slice(0, 3).join(', ')}</span>
                      {agent.languages.length > 3 && <span>+{agent.languages.length - 3}</span>}
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-6 text-center">
                    <div>
                      <div className="text-lg font-bold text-primary-600">{agent.totalClients}</div>
                      <div className="text-xs text-neutral-500">Happy Clients</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-primary-600">{agent.repeatClientRate}%</div>
                      <div className="text-xs text-neutral-500">Return Rate</div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <button className="btn-secondary btn-sm flex-1">
                      <MessageCircle className="w-4 h-4 mr-2" />
                      Message
                    </button>
                    <Link href={`/agents/${agent.id}`} className="btn-primary btn-sm flex-1 text-center">
                      View Profile
                      <ArrowRight className="w-4 h-4 ml-2 inline" />
                    </Link>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {viewMode === 'list' && (
            <div className="space-y-6">
              {filteredAgents.map((agent, index) => (
                <motion.div
                  key={agent.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="card-elevated"
                >
                  <div className="flex flex-col lg:flex-row gap-6">
                    {/* Agent Info */}
                    <div className="flex items-start gap-4">
                      <img
                        src={agent.avatar}
                        alt={agent.name}
                        className="w-16 h-16 rounded-full ring-2 ring-neutral-200"
                      />
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-2">
                          <div>
                            <h3 className="heading-md text-neutral-900">{agent.name}</h3>
                            <p className="text-sm text-primary-600 font-medium">{agent.title}</p>
                            <div className="flex items-center gap-1 text-sm text-neutral-500">
                              <MapPin className="w-4 h-4" />
                              <span>{agent.location}</span>
                            </div>
                          </div>
                          <div className={`px-2 py-1 rounded-full text-xs font-medium ${getAvailabilityColor(agent.availabilityStatus)}`}>
                            {agent.availabilityStatus}
                          </div>
                        </div>
                        
                        <p className="text-sm text-neutral-600 mb-4 line-clamp-2">{agent.bio}</p>
                        
                        <div className="flex flex-wrap gap-2 mb-4">
                          {agent.specializations.slice(0, 4).map((spec, i) => (
                            <div key={i} className="flex items-center gap-1 bg-neutral-100 px-2 py-1 rounded-full text-xs">
                              {getSpecializationIcon(spec)}
                              <span>{spec}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Stats & Actions */}
                    <div className="lg:w-64 flex-shrink-0">
                      <div className="grid grid-cols-3 gap-4 mb-4 text-center">
                        <div>
                          <div className="flex items-center justify-center gap-1">
                            <Star className="w-4 h-4 text-yellow-500 fill-current" />
                            <span className="font-bold text-neutral-900">{agent.rating}</span>
                          </div>
                          <div className="text-xs text-neutral-500">{agent.reviewCount} reviews</div>
                        </div>
                        <div>
                          <div className="font-bold text-neutral-900">{agent.yearsExperience}y</div>
                          <div className="text-xs text-neutral-500">Experience</div>
                        </div>
                        <div>
                          <div className="font-bold text-neutral-900">{agent.repeatClientRate}%</div>
                          <div className="text-xs text-neutral-500">Return rate</div>
                        </div>
                      </div>
                      
                      <div className="text-center mb-4">
                        <div className="font-semibold text-primary-600">{agent.priceRange}</div>
                        <div className="text-xs text-neutral-500">Response: {agent.responseTime}</div>
                      </div>
                      
                      <div className="flex gap-2">
                        <button className="btn-secondary btn-sm flex-1">
                          <MessageCircle className="w-4 h-4 mr-2" />
                          Message
                        </button>
                        <Link 
                          href={`/agents/${agent.id}`}
                          className="btn-primary btn-sm flex-1 text-center"
                        >
                          View Profile
                        </Link>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {viewMode === 'match' && (
            <div className="text-center py-20">
              <Compass className="w-16 h-16 text-primary-500 mx-auto mb-6" />
              <h3 className="heading-md text-neutral-900 mb-4">Smart Agent Matching</h3>
              <p className="text-body text-neutral-600 mb-8 max-w-2xl mx-auto">
                Our AI-powered matching system is coming soon. Answer a few questions about your travel style, 
                preferences, and interests to find your perfect Morocco expert.
              </p>
              <button 
                onClick={() => setViewMode('grid')}
                className="btn-primary"
              >
                Browse All Agents
              </button>
            </div>
          )}

          {filteredAgents.length === 0 && viewMode !== 'match' && (
            <div className="text-center py-20">
              <Search className="w-16 h-16 text-neutral-300 mx-auto mb-6" />
              <h3 className="heading-md text-neutral-900 mb-4">No Agents Found</h3>
              <p className="text-body text-neutral-600 mb-8">
                Try adjusting your search criteria or filters to find more agents.
              </p>
              <button 
                onClick={() => {
                  setSearchQuery('')
                  setFilters({
                    specialization: '',
                    language: '',
                    region: '',
                    priceRange: '',
                    availability: '',
                    travelStyle: ''
                  })
                }}
                className="btn-primary"
              >
                Clear All Filters
              </button>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  )
}