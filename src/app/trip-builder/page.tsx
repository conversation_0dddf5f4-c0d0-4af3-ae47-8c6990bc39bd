'use client'

import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { TripBuilderDashboard } from '@/components/trip-builder/trip-builder-dashboard'
import { TimelineTripPlanner } from '@/components/ui/timeline-trip-planner'
import { MultimediaExperience } from '@/components/immersive-content/multimedia-experience'
import { ContextualSuggestions } from '@/components/smart-recommendations/contextual-suggestions'
import { RegistrationPrompts } from '@/components/trip-builder/registration-prompts'
import { triggerSaveTrip } from '@/components/auth/registration-triggers'
import { useAuth } from '@/lib/auth/auth-context'

export default function TripBuilderPage() {
  const { user } = useAuth()

  // Sample itinerary data for the timeline planner
  const sampleItinerary = [
    {
      day: 1,
      date: 'March 15, 2024',
      location: 'Marrakech',
      totalCost: 180,
      activities: [
        {
          id: 'activity-1',
          title: 'Jemaa el-Fnaa Square Experience',
          description: 'Immerse yourself in the heart of Marrakech at this UNESCO World Heritage site, where storytellers, musicians, and food vendors create an unforgettable atmosphere.',
          time: '9:00 AM',
          duration: '3 hours',
          location: 'Medina, Marrakech',
          category: 'cultural' as const,
          imageUrl: '/images/destinations/marrakech.jpg',
          culturalSignificance: 'This square has been the social and economic heart of Marrakech for over 1000 years, representing the authentic soul of Moroccan culture.',
          price: 45,
          rating: 4.8,
          tags: ['UNESCO', 'Traditional', 'Photography', 'Local Culture']
        },
        {
          id: 'activity-2',
          title: 'Traditional Moroccan Cooking Class',
          description: 'Learn to prepare authentic tagines and couscous with a local family in their traditional riad.',
          time: '2:00 PM',
          duration: '4 hours',
          location: 'Traditional Riad, Marrakech',
          category: 'culinary' as const,
          imageUrl: '/images/destinations/marrakech.jpg',
          culturalSignificance: 'Moroccan cuisine reflects the country\'s rich history of trade and cultural exchange across Africa, Arabia, and Europe.',
          price: 85,
          rating: 4.9,
          tags: ['Hands-on', 'Family Experience', 'Traditional Recipes', 'Authentic']
        }
      ]
    },
    {
      day: 2,
      date: 'March 16, 2024',
      location: 'Fes',
      totalCost: 220,
      activities: [
        {
          id: 'activity-3',
          title: 'Fes Medina Walking Tour',
          description: 'Navigate the world\'s largest car-free urban area with a local guide who knows every hidden corner.',
          time: '10:00 AM',
          duration: '4 hours',
          location: 'Fes el-Bali, Fes',
          category: 'cultural' as const,
          imageUrl: '/images/destinations/fes.jpg',
          culturalSignificance: 'Fes el-Bali is a UNESCO World Heritage site and represents one of the world\'s best-preserved medieval cities.',
          price: 65,
          rating: 4.7,
          tags: ['UNESCO', 'Medieval', 'Architecture', 'History']
        },
        {
          id: 'activity-4',
          title: 'Traditional Leather Tannery Visit',
          description: 'Witness the ancient art of leather making at the famous Chouara Tannery, unchanged for centuries.',
          time: '3:00 PM',
          duration: '2 hours',
          location: 'Chouara Tannery, Fes',
          category: 'cultural' as const,
          imageUrl: '/images/destinations/fes.jpg',
          culturalSignificance: 'The tanneries of Fes have operated using the same techniques for over 1000 years, representing living heritage.',
          price: 35,
          rating: 4.5,
          tags: ['Traditional Crafts', 'Artisan', 'Heritage', 'Authentic']
        }
      ]
    }
  ]

  const handleSave = (trip: any) => {
    if (!user) {
      triggerSaveTrip()
      return
    }
    console.log('Trip saved:', trip)
    // Handle trip saving logic for authenticated users
  }

  const handleBooking = (trip: any) => {
    if (!user) {
      triggerSaveTrip()
      return
    }
    console.log('Proceeding to booking:', trip)
    // Handle booking flow
  }

  const handleActivitySelect = (activity: any) => {
    console.log('Activity selected:', activity)
    // Handle activity selection logic
  }

  const handleDayExpand = (day: number) => {
    console.log('Day expanded:', day)
    // Handle day expansion tracking
  }

  return (
    <div className="min-h-screen bg-neutral-50">
      <Header />
      <div className="pt-20 space-y-8">
        <div className="container-custom">
          <TripBuilderDashboard />
        </div>
        
        <div className="container-custom">
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-neutral-900 mb-4">Your Morocco Journey</h2>
              <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
                Plan your perfect itinerary with our interactive timeline planner
              </p>
            </div>
            <TimelineTripPlanner
              itinerary={sampleItinerary}
              onActivitySelect={handleActivitySelect}
              onDayExpand={handleDayExpand}
            />
          </div>
        </div>
        
        <div className="container-custom">
          <MultimediaExperience />
        </div>
        
        <div className="container-custom">
          <ContextualSuggestions />
        </div>
        
        {!user && (
          <div className="container-custom">
            <RegistrationPrompts />
          </div>
        )}
      </div>
      <Footer />
    </div>
  )
}