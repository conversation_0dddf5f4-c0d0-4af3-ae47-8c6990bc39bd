'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  MessageSquare,
  Send,
  Mail,
  Phone,
  Video,
  Bell,
  Users,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  Search,
  Filter,
  Plus,
  Eye,
  Reply,
  Forward,
  Archive,
  Star,
  Paperclip,
  Globe,
  MapPin,
  TrendingUp
} from 'lucide-react'
import { useSuperAdmin } from '@/lib/auth/super-admin-auth'

interface Communication {
  id: string
  type: 'message' | 'email' | 'notification' | 'announcement'
  subject: string
  content: string
  sender: {
    id: string
    name: string
    email: string
    role: 'admin' | 'guide' | 'provider' | 'customer'
    avatar: string
  }
  recipients: {
    id: string
    name: string
    email: string
    role: string
    readAt?: Date
  }[]
  priority: 'low' | 'normal' | 'high' | 'urgent'
  status: 'draft' | 'sent' | 'delivered' | 'read' | 'replied'
  countryCode?: string
  category: 'support' | 'booking' | 'quality' | 'marketing' | 'system' | 'training'
  attachments: {
    id: string
    name: string
    url: string
    size: number
  }[]
  isStarred: boolean
  createdAt: Date
  sentAt?: Date
  readAt?: Date
  repliedAt?: Date
}

interface QualityCheck {
  id: string
  targetType: 'guide' | 'provider' | 'experience'
  targetId: string
  targetName: string
  checkType: 'routine' | 'complaint' | 'audit' | 'follow_up'
  status: 'scheduled' | 'in_progress' | 'completed' | 'failed'
  score?: number
  checklist: {
    item: string
    status: 'pass' | 'fail' | 'na'
    notes?: string
  }[]
  inspector: string
  scheduledDate: Date
  completedDate?: Date
  findings: string
  recommendations: string[]
  followUpRequired: boolean
  countryCode: string
}

export default function CommunicationsPage() {
  const { user, hasPermission } = useSuperAdmin()
  const [activeTab, setActiveTab] = useState<'messages' | 'quality'>('messages')
  const [communications, setCommunications] = useState<Communication[]>([])
  const [qualityChecks, setQualityChecks] = useState<QualityCheck[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data
  useEffect(() => {
    const mockCommunications: Communication[] = [
      {
        id: '1',
        type: 'email',
        subject: 'Quality Assurance Follow-up Required',
        content: 'Dear Ahmed, following our recent quality check, we need to discuss some improvements for your desert tours. Please schedule a call at your earliest convenience.',
        sender: {
          id: 'admin-1',
          name: 'Quality Team',
          email: '<EMAIL>',
          role: 'admin',
          avatar: '/images/avatars/admin.jpg'
        },
        recipients: [
          {
            id: 'guide-1',
            name: 'Ahmed El Mansouri',
            email: '<EMAIL>',
            role: 'guide',
            readAt: new Date('2024-01-24')
          }
        ],
        priority: 'high',
        status: 'read',
        countryCode: 'MAR',
        category: 'quality',
        attachments: [
          {
            id: '1',
            name: 'quality-report.pdf',
            url: '/documents/quality-report.pdf',
            size: 245760
          }
        ],
        isStarred: true,
        createdAt: new Date('2024-01-23'),
        sentAt: new Date('2024-01-23'),
        readAt: new Date('2024-01-24')
      },
      {
        id: '2',
        type: 'message',
        subject: 'Booking Inquiry - Atlas Mountains Trek',
        content: 'Hi, I have a customer interested in a 3-day Atlas Mountains trek for next month. Can you confirm availability and pricing?',
        sender: {
          id: 'provider-1',
          name: 'Atlas Adventures Morocco',
          email: '<EMAIL>',
          role: 'provider',
          avatar: '/images/avatars/hassan.jpg'
        },
        recipients: [
          {
            id: 'admin-1',
            name: 'Booking Team',
            email: '<EMAIL>',
            role: 'admin'
          }
        ],
        priority: 'normal',
        status: 'sent',
        countryCode: 'MAR',
        category: 'booking',
        attachments: [],
        isStarred: false,
        createdAt: new Date('2024-01-25'),
        sentAt: new Date('2024-01-25')
      },
      {
        id: '3',
        type: 'notification',
        subject: 'New Training Module Available',
        content: 'A new training module on "Sustainable Tourism Practices" is now available. Please complete it within 2 weeks.',
        sender: {
          id: 'system',
          name: 'Training System',
          email: '<EMAIL>',
          role: 'admin',
          avatar: '/images/avatars/system.jpg'
        },
        recipients: [
          {
            id: 'guide-1',
            name: 'Ahmed El Mansouri',
            email: '<EMAIL>',
            role: 'guide'
          },
          {
            id: 'guide-2',
            name: 'Yuki Tanaka',
            email: '<EMAIL>',
            role: 'guide'
          }
        ],
        priority: 'normal',
        status: 'delivered',
        category: 'training',
        attachments: [],
        isStarred: false,
        createdAt: new Date('2024-01-22'),
        sentAt: new Date('2024-01-22')
      }
    ]

    const mockQualityChecks: QualityCheck[] = [
      {
        id: '1',
        targetType: 'guide',
        targetId: 'guide-1',
        targetName: 'Ahmed El Mansouri',
        checkType: 'routine',
        status: 'completed',
        score: 8.5,
        checklist: [
          { item: 'Professional appearance', status: 'pass' },
          { item: 'Punctuality', status: 'pass' },
          { item: 'Knowledge of local culture', status: 'pass' },
          { item: 'Safety protocols followed', status: 'fail', notes: 'Need to improve first aid kit maintenance' },
          { item: 'Customer interaction', status: 'pass' },
          { item: 'Equipment condition', status: 'pass' }
        ],
        inspector: 'Quality Inspector - Morocco',
        scheduledDate: new Date('2024-01-20'),
        completedDate: new Date('2024-01-20'),
        findings: 'Overall excellent performance with minor safety protocol improvements needed.',
        recommendations: [
          'Update first aid kit monthly',
          'Complete advanced safety training',
          'Maintain equipment checklist'
        ],
        followUpRequired: true,
        countryCode: 'MAR'
      },
      {
        id: '2',
        targetType: 'provider',
        targetId: 'provider-2',
        targetName: 'Tokyo Cultural Experiences',
        checkType: 'audit',
        status: 'in_progress',
        checklist: [
          { item: 'Business license validity', status: 'pass' },
          { item: 'Insurance coverage', status: 'pass' },
          { item: 'Facility cleanliness', status: 'pass' },
          { item: 'Staff training records', status: 'na' },
          { item: 'Customer feedback handling', status: 'pass' }
        ],
        inspector: 'Quality Inspector - Japan',
        scheduledDate: new Date('2024-01-25'),
        findings: 'Audit in progress - preliminary findings positive.',
        recommendations: [],
        followUpRequired: false,
        countryCode: 'JPN'
      },
      {
        id: '3',
        targetType: 'provider',
        targetId: 'provider-3',
        targetName: 'Barcelona Gourmet Tours',
        checkType: 'complaint',
        status: 'scheduled',
        checklist: [
          { item: 'Food safety protocols', status: 'na' },
          { item: 'Guide qualifications', status: 'na' },
          { item: 'Route safety', status: 'na' },
          { item: 'Customer service standards', status: 'na' }
        ],
        inspector: 'Quality Inspector - Spain',
        scheduledDate: new Date('2024-01-28'),
        findings: 'Investigation scheduled following customer complaints.',
        recommendations: [],
        followUpRequired: true,
        countryCode: 'ESP'
      }
    ]
    
    setCommunications(mockCommunications)
    setQualityChecks(mockQualityChecks)
    setIsLoading(false)
  }, [])

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-700 bg-red-100'
      case 'high': return 'text-orange-700 bg-orange-100'
      case 'normal': return 'text-blue-700 bg-blue-100'
      case 'low': return 'text-gray-700 bg-gray-100'
      default: return 'text-gray-700 bg-gray-100'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-700 bg-green-100'
      case 'in_progress': return 'text-blue-700 bg-blue-100'
      case 'scheduled': return 'text-yellow-700 bg-yellow-100'
      case 'failed': return 'text-red-700 bg-red-100'
      case 'sent': return 'text-green-700 bg-green-100'
      case 'delivered': return 'text-blue-700 bg-blue-100'
      case 'read': return 'text-purple-700 bg-purple-100'
      default: return 'text-gray-700 bg-gray-100'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email': return <Mail className="w-4 h-4" />
      case 'message': return <MessageSquare className="w-4 h-4" />
      case 'notification': return <Bell className="w-4 h-4" />
      case 'announcement': return <Users className="w-4 h-4" />
      default: return <MessageSquare className="w-4 h-4" />
    }
  }

  const filteredCommunications = communications.filter(comm => {
    const matchesCategory = selectedCategory === 'all' || comm.category === selectedCategory
    const matchesStatus = selectedStatus === 'all' || comm.status === selectedStatus
    const matchesSearch = searchQuery === '' || 
      comm.subject.toLowerCase().includes(searchQuery.toLowerCase()) ||
      comm.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      comm.sender.name.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesCategory && matchesStatus && matchesSearch
  })

  const filteredQualityChecks = qualityChecks.filter(check => {
    const matchesStatus = selectedStatus === 'all' || check.status === selectedStatus
    const matchesSearch = searchQuery === '' || 
      check.targetName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      check.findings.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesStatus && matchesSearch
  })

  if (!hasPermission('canManageContent')) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold">Access Denied</h3>
          <p className="text-red-600">You don't have permission to manage communications.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Communications & Quality</h1>
          <p className="text-gray-600">Manage partner communications and quality assurance processes</p>
        </div>
        
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <TrendingUp className="w-4 h-4" />
            Analytics
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Plus className="w-4 h-4" />
            New Message
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Messages</p>
              <p className="text-2xl font-bold text-gray-900">{communications.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Quality Checks</p>
              <p className="text-2xl font-bold text-green-600">{qualityChecks.length}</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending Actions</p>
              <p className="text-2xl font-bold text-yellow-600">
                {qualityChecks.filter(q => q.followUpRequired).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Quality Score</p>
              <p className="text-2xl font-bold text-purple-600">
                {qualityChecks.filter(q => q.score).length > 0 
                  ? (qualityChecks.filter(q => q.score).reduce((sum, q) => sum + (q.score || 0), 0) / qualityChecks.filter(q => q.score).length).toFixed(1)
                  : 'N/A'
                }
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('messages')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'messages'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <MessageSquare className="w-4 h-4 inline mr-2" />
              Communications ({communications.length})
            </button>
            <button
              onClick={() => setActiveTab('quality')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'quality'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <CheckCircle className="w-4 h-4 inline mr-2" />
              Quality Checks ({qualityChecks.length})
            </button>
          </nav>
        </div>

        <div className="p-6">
          {/* Filters */}
          <div className="flex items-center gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder={`Search ${activeTab}...`}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {activeTab === 'messages' && (
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Categories</option>
                <option value="support">Support</option>
                <option value="booking">Booking</option>
                <option value="quality">Quality</option>
                <option value="marketing">Marketing</option>
                <option value="training">Training</option>
              </select>
            )}

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              {activeTab === 'messages' ? (
                <>
                  <option value="draft">Draft</option>
                  <option value="sent">Sent</option>
                  <option value="delivered">Delivered</option>
                  <option value="read">Read</option>
                </>
              ) : (
                <>
                  <option value="scheduled">Scheduled</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                </>
              )}
            </select>
          </div>

          {/* Content */}
          {activeTab === 'messages' ? (
            <div className="space-y-4">
              {filteredCommunications.map((comm, index) => (
                <motion.div
                  key={comm.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="bg-gray-50 rounded-lg p-6"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start gap-4">
                      <div className="w-10 h-10 bg-white rounded-full overflow-hidden">
                        <img
                          src={comm.sender.avatar}
                          alt={comm.sender.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(comm.sender.name)}&background=random`
                          }}
                        />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="font-semibold text-gray-900">{comm.subject}</h4>
                          <span className={`px-2 py-1 text-xs rounded-full ${getTypeIcon(comm.type)} ${getPriorityColor(comm.priority)}`}>
                            {getTypeIcon(comm.type)}
                            <span className="ml-1">{comm.priority}</span>
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(comm.status)}`}>
                            {comm.status}
                          </span>
                          {comm.isStarred && (
                            <Star className="w-4 h-4 text-yellow-500 fill-current" />
                          )}
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                          <span>From: {comm.sender.name}</span>
                          <span>To: {comm.recipients.length} recipient(s)</span>
                          <span>{comm.category}</span>
                          {comm.countryCode && <span>{comm.countryCode}</span>}
                          <span>{comm.createdAt.toLocaleDateString()}</span>
                        </div>
                        
                        <p className="text-gray-700 mb-3">{comm.content}</p>
                        
                        {comm.attachments.length > 0 && (
                          <div className="flex items-center gap-2 mb-3">
                            <Paperclip className="w-4 h-4 text-gray-400" />
                            <span className="text-sm text-gray-600">
                              {comm.attachments.length} attachment(s)
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                        <Reply className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-purple-600 transition-colors">
                        <Forward className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-orange-600 transition-colors">
                        <Archive className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredQualityChecks.map((check, index) => (
                <motion.div
                  key={check.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="bg-gray-50 rounded-lg p-6"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-semibold text-gray-900">{check.targetName}</h4>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(check.status)}`}>
                          {check.status.replace('_', ' ')}
                        </span>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {check.targetType}
                        </span>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {check.checkType.replace('_', ' ')}
                        </span>
                        {check.score && (
                          <span className="text-xs text-green-700 bg-green-100 px-2 py-1 rounded">
                            Score: {check.score}/10
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                        <span className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {check.scheduledDate.toLocaleDateString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <Users className="w-3 h-3" />
                          {check.inspector}
                        </span>
                        <span className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {check.countryCode}
                        </span>
                        {check.followUpRequired && (
                          <span className="text-orange-600 font-medium">Follow-up required</span>
                        )}
                      </div>
                      
                      <p className="text-gray-700 mb-3">{check.findings}</p>
                      
                      {check.recommendations.length > 0 && (
                        <div className="mb-3">
                          <h5 className="text-sm font-medium text-gray-900 mb-2">Recommendations:</h5>
                          <ul className="text-sm text-gray-600 space-y-1">
                            {check.recommendations.map((rec, recIndex) => (
                              <li key={recIndex} className="flex items-start gap-2">
                                <span className="text-blue-500">•</span>
                                {rec}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {check.checklist.map((item, itemIndex) => (
                          <div key={itemIndex} className="flex items-center gap-2 text-sm">
                            <span className={`w-2 h-2 rounded-full ${
                              item.status === 'pass' ? 'bg-green-500' :
                              item.status === 'fail' ? 'bg-red-500' :
                              'bg-gray-300'
                            }`} />
                            <span className="text-gray-700">{item.item}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                        <CheckCircle className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
