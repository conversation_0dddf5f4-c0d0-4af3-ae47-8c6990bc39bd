'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Star,
  MessageSquare,
  ThumbsUp,
  ThumbsDown,
  Flag,
  Eye,
  Edit,
  Trash2,
  Search,
  Filter,
  Calendar,
  User,
  MapPin,
  CheckCircle,
  XCircle,
  AlertTriangle,
  TrendingUp,
  BarChart3,
  Users,
  Award
} from 'lucide-react'
import { useSuperAdmin } from '@/lib/auth/super-admin-auth'

interface Review {
  id: string
  rating: number
  title: string
  content: string
  reviewerName: string
  reviewerEmail: string
  reviewerAvatar: string
  targetType: 'guide' | 'provider' | 'experience' | 'destination'
  targetId: string
  targetName: string
  countryCode: string
  city: string
  isVerified: boolean
  status: 'pending' | 'approved' | 'rejected' | 'flagged'
  moderationNotes?: string
  helpfulVotes: number
  reportCount: number
  createdAt: Date
  updatedAt: Date
  response?: {
    content: string
    responderName: string
    respondedAt: Date
  }
  photos: string[]
  tags: string[]
}

export default function ReviewManagementPage() {
  const { user, hasPermission } = useSuperAdmin()
  const [reviews, setReviews] = useState<Review[]>([])
  const [selectedCountry, setSelectedCountry] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedRating, setSelectedRating] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data
  useEffect(() => {
    const mockReviews: Review[] = [
      {
        id: '1',
        rating: 5,
        title: 'Amazing desert experience!',
        content: 'Ahmed was an incredible guide. His knowledge of the desert and Berber culture was outstanding. The camel trek was perfectly organized and the overnight camping was magical. Highly recommend!',
        reviewerName: 'Sarah Johnson',
        reviewerEmail: '<EMAIL>',
        reviewerAvatar: '/images/avatars/sarah.jpg',
        targetType: 'guide',
        targetId: 'guide-1',
        targetName: 'Ahmed El Mansouri',
        countryCode: 'MAR',
        city: 'Marrakech',
        isVerified: true,
        status: 'approved',
        helpfulVotes: 12,
        reportCount: 0,
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2024-01-20'),
        response: {
          content: 'Thank you so much Sarah! It was a pleasure sharing the beauty of our desert with you. Welcome back anytime!',
          responderName: 'Ahmed El Mansouri',
          respondedAt: new Date('2024-01-21')
        },
        photos: ['/images/reviews/desert1.jpg', '/images/reviews/desert2.jpg'],
        tags: ['desert', 'camel trek', 'cultural', 'camping']
      },
      {
        id: '2',
        rating: 4,
        title: 'Great cultural experience',
        content: 'The tea ceremony was beautiful and Yuki explained everything perfectly. The only downside was that it felt a bit rushed. Would love to have had more time to practice.',
        reviewerName: 'Michael Chen',
        reviewerEmail: '<EMAIL>',
        reviewerAvatar: '/images/avatars/michael.jpg',
        targetType: 'provider',
        targetId: 'provider-2',
        targetName: 'Tokyo Cultural Experiences',
        countryCode: 'JPN',
        city: 'Tokyo',
        isVerified: true,
        status: 'approved',
        helpfulVotes: 8,
        reportCount: 0,
        createdAt: new Date('2024-01-18'),
        updatedAt: new Date('2024-01-18'),
        photos: ['/images/reviews/tea-ceremony.jpg'],
        tags: ['tea ceremony', 'cultural', 'traditional']
      },
      {
        id: '3',
        rating: 2,
        title: 'Disappointing food tour',
        content: 'The tour was overpriced and the guide seemed disinterested. We only visited 3 places in 4 hours and the food was mediocre. Expected much more for the price.',
        reviewerName: 'Emma Rodriguez',
        reviewerEmail: '<EMAIL>',
        reviewerAvatar: '/images/avatars/emma.jpg',
        targetType: 'provider',
        targetId: 'provider-3',
        targetName: 'Barcelona Gourmet Tours',
        countryCode: 'ESP',
        city: 'Barcelona',
        isVerified: false,
        status: 'flagged',
        moderationNotes: 'Provider disputed claims - needs investigation',
        helpfulVotes: 3,
        reportCount: 1,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-16'),
        photos: [],
        tags: ['food tour', 'disappointing', 'overpriced']
      },
      {
        id: '4',
        rating: 5,
        title: 'Perfect Atlas Mountains trek',
        content: 'Absolutely incredible experience! The views were breathtaking and our guide Hassan was knowledgeable and safety-conscious. The accommodation in the mountain lodge was comfortable.',
        reviewerName: 'David Wilson',
        reviewerEmail: '<EMAIL>',
        reviewerAvatar: '/images/avatars/david.jpg',
        targetType: 'experience',
        targetId: 'exp-1',
        targetName: 'Atlas Mountains Hiking',
        countryCode: 'MAR',
        city: 'Marrakech',
        isVerified: true,
        status: 'pending',
        helpfulVotes: 0,
        reportCount: 0,
        createdAt: new Date('2024-01-25'),
        updatedAt: new Date('2024-01-25'),
        photos: ['/images/reviews/atlas1.jpg', '/images/reviews/atlas2.jpg', '/images/reviews/atlas3.jpg'],
        tags: ['hiking', 'mountains', 'adventure', 'nature']
      },
      {
        id: '5',
        rating: 1,
        title: 'Terrible service - avoid!',
        content: 'This is completely inappropriate content that violates our community guidelines and contains offensive language.',
        reviewerName: 'Anonymous User',
        reviewerEmail: '<EMAIL>',
        reviewerAvatar: '/images/avatars/default.jpg',
        targetType: 'guide',
        targetId: 'guide-2',
        targetName: 'Yuki Tanaka',
        countryCode: 'JPN',
        city: 'Tokyo',
        isVerified: false,
        status: 'rejected',
        moderationNotes: 'Contains inappropriate content and appears to be fake review',
        helpfulVotes: 0,
        reportCount: 5,
        createdAt: new Date('2024-01-22'),
        updatedAt: new Date('2024-01-23'),
        photos: [],
        tags: ['inappropriate', 'fake']
      }
    ]
    
    setReviews(mockReviews)
    setIsLoading(false)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-700 bg-green-100'
      case 'pending': return 'text-yellow-700 bg-yellow-100'
      case 'rejected': return 'text-red-700 bg-red-100'
      case 'flagged': return 'text-orange-700 bg-orange-100'
      default: return 'text-gray-700 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle className="w-4 h-4" />
      case 'pending': return <AlertTriangle className="w-4 h-4" />
      case 'rejected': return <XCircle className="w-4 h-4" />
      case 'flagged': return <Flag className="w-4 h-4" />
      default: return <AlertTriangle className="w-4 h-4" />
    }
  }

  const getRatingColor = (rating: number) => {
    if (rating >= 4) return 'text-green-600'
    if (rating >= 3) return 'text-yellow-600'
    return 'text-red-600'
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'text-yellow-500 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  const filteredReviews = reviews.filter(review => {
    const matchesCountry = selectedCountry === 'all' || review.countryCode === selectedCountry
    const matchesType = selectedType === 'all' || review.targetType === selectedType
    const matchesStatus = selectedStatus === 'all' || review.status === selectedStatus
    const matchesRating = selectedRating === 'all' || 
      (selectedRating === '5' && review.rating === 5) ||
      (selectedRating === '4' && review.rating === 4) ||
      (selectedRating === '3' && review.rating === 3) ||
      (selectedRating === '1-2' && review.rating <= 2)
    const matchesSearch = searchQuery === '' || 
      review.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      review.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      review.reviewerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      review.targetName.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesCountry && matchesType && matchesStatus && matchesRating && matchesSearch
  })

  if (!hasPermission('canManageContent')) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold">Access Denied</h3>
          <p className="text-red-600">You don't have permission to manage reviews.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Review Management</h1>
          <p className="text-gray-600">Moderate and manage customer reviews and ratings</p>
        </div>
        
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <BarChart3 className="w-4 h-4" />
            Analytics
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Reviews</p>
              <p className="text-2xl font-bold text-gray-900">{reviews.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <MessageSquare className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Rating</p>
              <p className="text-2xl font-bold text-yellow-600">
                {(reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length).toFixed(1)}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">
                {reviews.filter(r => r.status === 'pending').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Flagged</p>
              <p className="text-2xl font-bold text-red-600">
                {reviews.filter(r => r.status === 'flagged').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <Flag className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Verified</p>
              <p className="text-2xl font-bold text-green-600">
                {reviews.filter(r => r.isVerified).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Award className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search reviews..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <select
            value={selectedCountry}
            onChange={(e) => setSelectedCountry(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Countries</option>
            <option value="MAR">🇲🇦 Morocco</option>
            <option value="JPN">🇯🇵 Japan</option>
            <option value="ESP">🇪🇸 Spain</option>
            <option value="ITA">🇮🇹 Italy</option>
          </select>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Types</option>
            <option value="guide">Guides</option>
            <option value="provider">Providers</option>
            <option value="experience">Experiences</option>
            <option value="destination">Destinations</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="flagged">Flagged</option>
            <option value="rejected">Rejected</option>
          </select>

          <select
            value={selectedRating}
            onChange={(e) => setSelectedRating(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Ratings</option>
            <option value="5">5 Stars</option>
            <option value="4">4 Stars</option>
            <option value="3">3 Stars</option>
            <option value="1-2">1-2 Stars</option>
          </select>

          <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
            <Filter className="w-4 h-4 inline mr-2" />
            More Filters
          </button>
        </div>
      </div>

      {/* Reviews List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Reviews ({filteredReviews.length})</h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredReviews.map((review, index) => (
            <motion.div
              key={review.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="p-6 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gray-100 rounded-full overflow-hidden flex-shrink-0">
                  <img
                    src={review.reviewerAvatar}
                    alt={review.reviewerName}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(review.reviewerName)}&background=random`
                    }}
                  />
                </div>
                
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-semibold text-gray-900">{review.title}</h4>
                        <div className="flex items-center gap-1">
                          {renderStars(review.rating)}
                          <span className={`ml-1 font-medium ${getRatingColor(review.rating)}`}>
                            {review.rating}/5
                          </span>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(review.status)}`}>
                          {getStatusIcon(review.status)}
                          <span className="ml-1">{review.status}</span>
                        </span>
                        {review.isVerified && (
                          <span className="text-xs text-green-700 bg-green-100 px-2 py-1 rounded">
                            Verified
                          </span>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                        <span className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          {review.reviewerName}
                        </span>
                        <span className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {review.targetName}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          {review.createdAt.toLocaleDateString()}
                        </span>
                        <span>{review.countryCode}</span>
                        <span>{review.targetType}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {review.reportCount > 0 && (
                        <span className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded">
                          {review.reportCount} reports
                        </span>
                      )}
                      <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                        <CheckCircle className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                        <XCircle className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-orange-600 transition-colors">
                        <Flag className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  
                  <p className="text-gray-700 mb-3 leading-relaxed">{review.content}</p>
                  
                  {review.photos.length > 0 && (
                    <div className="flex gap-2 mb-3">
                      {review.photos.slice(0, 3).map((photo, photoIndex) => (
                        <div key={photoIndex} className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden">
                          <img
                            src={photo}
                            alt={`Review photo ${photoIndex + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ))}
                      {review.photos.length > 3 && (
                        <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center text-sm text-gray-600">
                          +{review.photos.length - 3}
                        </div>
                      )}
                    </div>
                  )}
                  
                  {review.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-3">
                      {review.tags.map((tag, tagIndex) => (
                        <span key={tagIndex} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <ThumbsUp className="w-3 h-3" />
                        {review.helpfulVotes} helpful
                      </span>
                      {review.response && (
                        <span className="text-blue-600">Response from {review.response.responderName}</span>
                      )}
                    </div>
                    
                    {review.moderationNotes && (
                      <span className="text-orange-600 text-xs">
                        Moderation: {review.moderationNotes}
                      </span>
                    )}
                  </div>
                  
                  {review.response && (
                    <div className="mt-3 p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="text-sm font-medium text-blue-900">Response:</span>
                        <span className="text-xs text-blue-600">
                          {review.response.respondedAt.toLocaleDateString()}
                        </span>
                      </div>
                      <p className="text-sm text-blue-800">{review.response.content}</p>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}
