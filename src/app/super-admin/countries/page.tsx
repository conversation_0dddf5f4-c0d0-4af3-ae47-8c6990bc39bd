'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import {
  Globe,
  Plus,
  Search,
  Filter,
  Edit,
  Eye,
  ToggleLeft,
  ToggleRight,
  Users,
  MapPin,
  Star,
  Settings,
  MoreVertical,
  Trash2
} from 'lucide-react'
import { useSuperAdmin, withSuperAdminAuth } from '@/lib/auth/super-admin-auth'
import { useCountryContext, CountryContext } from '@/lib/country-context'

interface CountryStats {
  users: number
  destinations: number
  experiences: number
  revenue: number
  growth: number
}

interface ExtendedCountryContext extends CountryContext {
  stats: CountryStats
  lastUpdated: Date
  createdBy: string
}

function CountryManagementPage() {
  const { hasPermission } = useSuperAdmin()
  const { availableCountries } = useCountryContext()
  const [countries, setCountries] = useState<ExtendedCountryContext[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadCountries()
  }, [availableCountries])

  const loadCountries = async () => {
    try {
      // Mock data - replace with actual API call
      const mockCountries: ExtendedCountryContext[] = availableCountries.map((country, index) => ({
        ...country,
        stats: {
          users: Math.floor(Math.random() * 10000) + 1000,
          destinations: Math.floor(Math.random() * 50) + 10,
          experiences: Math.floor(Math.random() * 200) + 50,
          revenue: Math.floor(Math.random() * 100000) + 10000,
          growth: (Math.random() * 30) - 10 // -10% to +20%
        },
        lastUpdated: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        createdBy: '<EMAIL>'
      }))

      setCountries(mockCountries)
    } catch (error) {
      console.error('Failed to load countries:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const filteredCountries = countries.filter(country => {
    const matchesSearch = country.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         country.code.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesFilter = filterStatus === 'all' ||
                         (filterStatus === 'active' && country.isActive) ||
                         (filterStatus === 'inactive' && !country.isActive)
    
    return matchesSearch && matchesFilter
  })

  const toggleCountryStatus = async (countryId: string) => {
    setCountries(prev => prev.map(country =>
      country.id === countryId
        ? { ...country, isActive: !country.isActive }
        : country
    ))
    // TODO: Make API call to update country status
  }

  if (!hasPermission('canManageCountries')) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Globe className="w-8 h-8 text-red-600" />
        </div>
        <h2 className="text-xl font-semibold text-slate-900 mb-2">Access Denied</h2>
        <p className="text-slate-600">You don't have permission to manage countries.</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">Country Management</h1>
          <p className="text-slate-600 mt-2">
            Manage countries, their settings, and availability across the platform
          </p>
        </div>
        <Link
          href="/super-admin/countries/add"
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Add Country
        </Link>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
              <input
                type="text"
                placeholder="Search countries..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-slate-400" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-2 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Countries</option>
              <option value="active">Active Only</option>
              <option value="inactive">Inactive Only</option>
            </select>
          </div>
        </div>
      </div>

      {/* Countries Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl shadow-sm border border-slate-200 p-6 animate-pulse">
              <div className="h-4 bg-slate-200 rounded w-3/4 mb-4"></div>
              <div className="h-3 bg-slate-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-slate-200 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCountries.map((country, index) => (
            <motion.div
              key={country.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow"
            >
              {/* Country Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <span className="text-3xl">{country.flag}</span>
                  <div>
                    <h3 className="font-semibold text-slate-900">{country.name}</h3>
                    <p className="text-sm text-slate-600">{country.code}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => toggleCountryStatus(country.id)}
                    className={`p-1 rounded transition-colors ${
                      country.isActive ? 'text-green-600 hover:bg-green-50' : 'text-slate-400 hover:bg-slate-50'
                    }`}
                  >
                    {country.isActive ? <ToggleRight className="w-5 h-5" /> : <ToggleLeft className="w-5 h-5" />}
                  </button>
                  <button className="p-1 text-slate-400 hover:text-slate-600 hover:bg-slate-50 rounded transition-colors">
                    <MoreVertical className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Status Badge */}
              <div className="mb-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  country.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-slate-100 text-slate-800'
                }`}>
                  {country.isActive ? 'Active' : 'Inactive'}
                </span>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <p className="text-xs text-slate-600">Users</p>
                  <p className="text-lg font-semibold text-slate-900">{country.stats.users.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-xs text-slate-600">Revenue</p>
                  <p className="text-lg font-semibold text-slate-900">${country.stats.revenue.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-xs text-slate-600">Destinations</p>
                  <p className="text-lg font-semibold text-slate-900">{country.stats.destinations}</p>
                </div>
                <div>
                  <p className="text-xs text-slate-600">Experiences</p>
                  <p className="text-lg font-semibold text-slate-900">{country.stats.experiences}</p>
                </div>
              </div>

              {/* Growth Indicator */}
              <div className="mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-slate-600">Growth</span>
                  <span className={`font-medium ${
                    country.stats.growth > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {country.stats.growth > 0 ? '+' : ''}{country.stats.growth.toFixed(1)}%
                  </span>
                </div>
                <div className="w-full bg-slate-200 rounded-full h-2 mt-1">
                  <div
                    className={`h-2 rounded-full ${
                      country.stats.growth > 0 ? 'bg-green-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${Math.min(Math.abs(country.stats.growth) * 2, 100)}%` }}
                  ></div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2">
                <Link
                  href={`/super-admin/countries/${country.code.toLowerCase()}`}
                  className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors text-sm font-medium"
                >
                  <Eye className="w-4 h-4" />
                  View
                </Link>
                <Link
                  href={`/super-admin/countries/${country.code.toLowerCase()}/edit`}
                  className="flex-1 flex items-center justify-center gap-2 px-3 py-2 bg-slate-50 text-slate-600 rounded-lg hover:bg-slate-100 transition-colors text-sm font-medium"
                >
                  <Edit className="w-4 h-4" />
                  Edit
                </Link>
              </div>

              {/* Last Updated */}
              <div className="mt-4 pt-4 border-t border-slate-200">
                <p className="text-xs text-slate-500">
                  Updated {country.lastUpdated.toLocaleDateString()}
                </p>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && filteredCountries.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Globe className="w-8 h-8 text-slate-400" />
          </div>
          <h3 className="text-lg font-semibold text-slate-900 mb-2">No countries found</h3>
          <p className="text-slate-600 mb-4">
            {searchQuery ? 'Try adjusting your search criteria' : 'Get started by adding your first country'}
          </p>
          {!searchQuery && (
            <Link
              href="/super-admin/countries/add"
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-4 h-4" />
              Add Country
            </Link>
          )}
        </div>
      )}
    </div>
  )
}

export default withSuperAdminAuth(CountryManagementPage, 'canManageCountries')
