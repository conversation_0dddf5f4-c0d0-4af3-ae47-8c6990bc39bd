'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Building2,
  MapPin,
  Star,
  Calendar,
  Phone,
  Mail,
  Globe,
  Award,
  Clock,
  DollarSign,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Camera,
  Users,
  Shield,
  TrendingUp,
  Package,
  Briefcase,
  FileText,
  Car,
  Utensils
} from 'lucide-react'
import { useSuperAdmin } from '@/lib/auth/super-admin-auth'

interface ExperienceProvider {
  id: string
  businessName: string
  contactPerson: string
  email: string
  phone: string
  website?: string
  logo: string
  coverImage: string
  countryCode: string
  city: string
  address: string
  businessType: 'tour_operator' | 'activity_provider' | 'accommodation' | 'transport' | 'restaurant' | 'shop'
  categories: string[]
  description: string
  services: string[]
  capacity: {
    minGroup: number
    maxGroup: number
    dailyCapacity: number
  }
  pricing: {
    currency: string
    basePrice: number
    priceType: 'per_person' | 'per_group' | 'per_hour' | 'per_day'
    seasonalRates: boolean
  }
  rating: number
  reviewCount: number
  completedBookings: number
  isVerified: boolean
  isActive: boolean
  verificationStatus: 'pending' | 'verified' | 'rejected'
  joinedAt: Date
  lastActive: Date
  documents: {
    id: string
    type: 'business_license' | 'insurance' | 'tax_certificate' | 'safety_certificate'
    url: string
    status: 'pending' | 'approved' | 'rejected'
    uploadedAt: Date
    expiryDate?: Date
  }[]
  bankingInfo: {
    accountHolder: string
    accountNumber: string
    bankName: string
    isVerified: boolean
  }
  socialMedia: {
    instagram?: string
    facebook?: string
    tripadvisor?: string
  }
  operatingHours: {
    [key: string]: {
      open: string
      close: string
      isOpen: boolean
    }
  }
  seasonalAvailability: {
    startMonth: number
    endMonth: number
    notes?: string
  }[]
}

export default function ExperienceProviderPage() {
  const { user, hasPermission } = useSuperAdmin()
  const [providers, setProviders] = useState<ExperienceProvider[]>([])
  const [selectedCountry, setSelectedCountry] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data
  useEffect(() => {
    const mockProviders: ExperienceProvider[] = [
      {
        id: '1',
        businessName: 'Atlas Adventures Morocco',
        contactPerson: 'Hassan Benali',
        email: '<EMAIL>',
        phone: '+212 5 24 12 34 56',
        website: 'www.atlasadventures.ma',
        logo: '/images/providers/atlas-logo.jpg',
        coverImage: '/images/providers/atlas-cover.jpg',
        countryCode: 'MAR',
        city: 'Marrakech',
        address: 'Avenue Mohammed V, Marrakech 40000',
        businessType: 'tour_operator',
        categories: ['Desert Tours', 'Mountain Trekking', 'Cultural Experiences'],
        description: 'Leading tour operator specializing in authentic Moroccan adventures and cultural experiences.',
        services: ['Desert Camping', 'Camel Trekking', 'Mountain Hiking', 'Cultural Tours', 'Photography Tours'],
        capacity: {
          minGroup: 2,
          maxGroup: 20,
          dailyCapacity: 40
        },
        pricing: {
          currency: 'MAD',
          basePrice: 800,
          priceType: 'per_person',
          seasonalRates: true
        },
        rating: 4.8,
        reviewCount: 156,
        completedBookings: 342,
        isVerified: true,
        isActive: true,
        verificationStatus: 'verified',
        joinedAt: new Date('2020-05-15'),
        lastActive: new Date('2024-01-25'),
        documents: [
          {
            id: '1',
            type: 'business_license',
            url: '/documents/atlas-license.pdf',
            status: 'approved',
            uploadedAt: new Date('2020-05-15'),
            expiryDate: new Date('2025-05-15')
          },
          {
            id: '2',
            type: 'insurance',
            url: '/documents/atlas-insurance.pdf',
            status: 'approved',
            uploadedAt: new Date('2020-05-20'),
            expiryDate: new Date('2024-12-31')
          }
        ],
        bankingInfo: {
          accountHolder: 'Atlas Adventures Morocco SARL',
          accountNumber: '****1234',
          bankName: 'BMCE Bank',
          isVerified: true
        },
        socialMedia: {
          instagram: '@atlasadventures',
          facebook: 'AtlasAdventuresMorocco'
        },
        operatingHours: {
          monday: { open: '08:00', close: '18:00', isOpen: true },
          tuesday: { open: '08:00', close: '18:00', isOpen: true },
          wednesday: { open: '08:00', close: '18:00', isOpen: true },
          thursday: { open: '08:00', close: '18:00', isOpen: true },
          friday: { open: '08:00', close: '18:00', isOpen: true },
          saturday: { open: '08:00', close: '18:00', isOpen: true },
          sunday: { open: '09:00', close: '17:00', isOpen: true }
        },
        seasonalAvailability: [
          { startMonth: 1, endMonth: 12, notes: 'Year-round operations' }
        ]
      },
      {
        id: '2',
        businessName: 'Tokyo Cultural Experiences',
        contactPerson: 'Akiko Yamamoto',
        email: '<EMAIL>',
        phone: '+81 3 1234 5678',
        website: 'www.tokyocultural.jp',
        logo: '/images/providers/tokyo-logo.jpg',
        coverImage: '/images/providers/tokyo-cover.jpg',
        countryCode: 'JPN',
        city: 'Tokyo',
        address: 'Shibuya-ku, Tokyo 150-0002',
        businessType: 'activity_provider',
        categories: ['Cultural Workshops', 'Traditional Arts', 'Tea Ceremonies'],
        description: 'Authentic Japanese cultural experiences in the heart of Tokyo.',
        services: ['Tea Ceremony', 'Calligraphy Classes', 'Kimono Rental', 'Cooking Classes', 'Temple Visits'],
        capacity: {
          minGroup: 1,
          maxGroup: 12,
          dailyCapacity: 24
        },
        pricing: {
          currency: 'JPY',
          basePrice: 8000,
          priceType: 'per_person',
          seasonalRates: false
        },
        rating: 4.9,
        reviewCount: 89,
        completedBookings: 167,
        isVerified: true,
        isActive: true,
        verificationStatus: 'verified',
        joinedAt: new Date('2021-03-10'),
        lastActive: new Date('2024-01-24'),
        documents: [
          {
            id: '3',
            type: 'business_license',
            url: '/documents/tokyo-license.pdf',
            status: 'approved',
            uploadedAt: new Date('2021-03-10'),
            expiryDate: new Date('2026-03-10')
          }
        ],
        bankingInfo: {
          accountHolder: 'Tokyo Cultural Experiences KK',
          accountNumber: '****5678',
          bankName: 'Sumitomo Mitsui Banking',
          isVerified: true
        },
        socialMedia: {
          instagram: '@tokyocultural'
        },
        operatingHours: {
          monday: { open: '09:00', close: '17:00', isOpen: true },
          tuesday: { open: '09:00', close: '17:00', isOpen: true },
          wednesday: { open: '09:00', close: '17:00', isOpen: true },
          thursday: { open: '09:00', close: '17:00', isOpen: true },
          friday: { open: '09:00', close: '17:00', isOpen: true },
          saturday: { open: '10:00', close: '16:00', isOpen: true },
          sunday: { open: '10:00', close: '16:00', isOpen: false }
        },
        seasonalAvailability: [
          { startMonth: 1, endMonth: 12, notes: 'Year-round with holiday breaks' }
        ]
      },
      {
        id: '3',
        businessName: 'Barcelona Gourmet Tours',
        contactPerson: 'Carlos Martinez',
        email: '<EMAIL>',
        phone: '+34 93 123 45 67',
        website: 'www.barcelonagourmet.es',
        logo: '/images/providers/barcelona-logo.jpg',
        coverImage: '/images/providers/barcelona-cover.jpg',
        countryCode: 'ESP',
        city: 'Barcelona',
        address: 'Carrer de Pelai, 08001 Barcelona',
        businessType: 'tour_operator',
        categories: ['Food Tours', 'Wine Tasting', 'Market Visits'],
        description: 'Discover Barcelona through its incredible culinary scene with local experts.',
        services: ['Food Tours', 'Wine Tastings', 'Cooking Classes', 'Market Tours', 'Tapas Crawls'],
        capacity: {
          minGroup: 4,
          maxGroup: 16,
          dailyCapacity: 32
        },
        pricing: {
          currency: 'EUR',
          basePrice: 75,
          priceType: 'per_person',
          seasonalRates: true
        },
        rating: 4.7,
        reviewCount: 124,
        completedBookings: 289,
        isVerified: false,
        isActive: true,
        verificationStatus: 'pending',
        joinedAt: new Date('2022-01-20'),
        lastActive: new Date('2024-01-23'),
        documents: [
          {
            id: '4',
            type: 'business_license',
            url: '/documents/barcelona-license.pdf',
            status: 'pending',
            uploadedAt: new Date('2024-01-20')
          }
        ],
        bankingInfo: {
          accountHolder: 'Barcelona Gourmet Tours SL',
          accountNumber: '****9012',
          bankName: 'Banco Santander',
          isVerified: false
        },
        socialMedia: {
          instagram: '@barcelonagourmet',
          facebook: 'BarcelonaGourmetTours'
        },
        operatingHours: {
          monday: { open: '10:00', close: '19:00', isOpen: true },
          tuesday: { open: '10:00', close: '19:00', isOpen: true },
          wednesday: { open: '10:00', close: '19:00', isOpen: true },
          thursday: { open: '10:00', close: '19:00', isOpen: true },
          friday: { open: '10:00', close: '19:00', isOpen: true },
          saturday: { open: '10:00', close: '19:00', isOpen: true },
          sunday: { open: '11:00', close: '18:00', isOpen: true }
        },
        seasonalAvailability: [
          { startMonth: 3, endMonth: 11, notes: 'Seasonal operations' }
        ]
      }
    ]
    
    setProviders(mockProviders)
    setIsLoading(false)
  }, [])

  const getBusinessTypeIcon = (type: string) => {
    switch (type) {
      case 'tour_operator': return <MapPin className="w-4 h-4" />
      case 'activity_provider': return <Star className="w-4 h-4" />
      case 'accommodation': return <Building2 className="w-4 h-4" />
      case 'transport': return <Car className="w-4 h-4" />
      case 'restaurant': return <Utensils className="w-4 h-4" />
      case 'shop': return <Package className="w-4 h-4" />
      default: return <Briefcase className="w-4 h-4" />
    }
  }

  const getBusinessTypeColor = (type: string) => {
    switch (type) {
      case 'tour_operator': return 'bg-blue-100 text-blue-700'
      case 'activity_provider': return 'bg-purple-100 text-purple-700'
      case 'accommodation': return 'bg-green-100 text-green-700'
      case 'transport': return 'bg-orange-100 text-orange-700'
      case 'restaurant': return 'bg-red-100 text-red-700'
      case 'shop': return 'bg-yellow-100 text-yellow-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getVerificationStatus = (provider: ExperienceProvider) => {
    switch (provider.verificationStatus) {
      case 'verified': return { text: 'Verified', color: 'text-green-700 bg-green-100' }
      case 'pending': return { text: 'Pending', color: 'text-yellow-700 bg-yellow-100' }
      case 'rejected': return { text: 'Rejected', color: 'text-red-700 bg-red-100' }
      default: return { text: 'Unverified', color: 'text-gray-700 bg-gray-100' }
    }
  }

  const filteredProviders = providers.filter(provider => {
    const matchesCountry = selectedCountry === 'all' || provider.countryCode === selectedCountry
    const matchesType = selectedType === 'all' || provider.businessType === selectedType
    const matchesStatus = selectedStatus === 'all' || 
      (selectedStatus === 'verified' && provider.isVerified) ||
      (selectedStatus === 'pending' && provider.verificationStatus === 'pending') ||
      (selectedStatus === 'active' && provider.isActive) ||
      (selectedStatus === 'inactive' && !provider.isActive)
    const matchesSearch = searchQuery === '' || 
      provider.businessName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
      provider.categories.some(cat => cat.toLowerCase().includes(searchQuery.toLowerCase()))
    
    return matchesCountry && matchesType && matchesStatus && matchesSearch
  })

  if (!hasPermission('canManageContent')) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold">Access Denied</h3>
          <p className="text-red-600">You don't have permission to manage providers.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Experience Providers</h1>
          <p className="text-gray-600">Manage tour operators, activity providers, and service partners</p>
        </div>
        
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <TrendingUp className="w-4 h-4" />
            Analytics
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Plus className="w-4 h-4" />
            Add Provider
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Providers</p>
              <p className="text-2xl font-bold text-gray-900">{providers.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Building2 className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Verified</p>
              <p className="text-2xl font-bold text-green-600">
                {providers.filter(p => p.isVerified).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Shield className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active</p>
              <p className="text-2xl font-bold text-blue-600">
                {providers.filter(p => p.isActive).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Rating</p>
              <p className="text-2xl font-bold text-yellow-600">
                {(providers.reduce((sum, p) => sum + p.rating, 0) / providers.length).toFixed(1)}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Bookings</p>
              <p className="text-2xl font-bold text-purple-600">
                {providers.reduce((sum, p) => sum + p.completedBookings, 0)}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search providers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <select
            value={selectedCountry}
            onChange={(e) => setSelectedCountry(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Countries</option>
            <option value="MAR">🇲🇦 Morocco</option>
            <option value="JPN">🇯🇵 Japan</option>
            <option value="ESP">🇪🇸 Spain</option>
            <option value="ITA">🇮🇹 Italy</option>
          </select>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Types</option>
            <option value="tour_operator">Tour Operators</option>
            <option value="activity_provider">Activity Providers</option>
            <option value="accommodation">Accommodation</option>
            <option value="transport">Transport</option>
            <option value="restaurant">Restaurants</option>
            <option value="shop">Shops</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="verified">Verified</option>
            <option value="pending">Pending</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <Building2 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <FileText className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Providers Grid/List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Experience Providers ({filteredProviders.length})</h3>
        </div>
        
        {viewMode === 'grid' ? (
          <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProviders.map((provider, index) => (
              <motion.div
                key={provider.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                {/* Cover Image */}
                <div className="h-32 bg-gradient-to-r from-blue-500 to-purple-600 relative">
                  <div className="absolute top-3 right-3 flex gap-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${getBusinessTypeColor(provider.businessType)}`}>
                      {getBusinessTypeIcon(provider.businessType)}
                      <span className="ml-1">{provider.businessType.replace('_', ' ')}</span>
                    </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${getVerificationStatus(provider).color}`}>
                      {getVerificationStatus(provider).text}
                    </span>
                  </div>
                </div>

                {/* Profile */}
                <div className="p-6 -mt-8 relative">
                  <div className="w-16 h-16 bg-white rounded-lg border-4 border-white shadow-lg mb-4">
                    <img
                      src={provider.logo}
                      alt={provider.businessName}
                      className="w-full h-full rounded-lg object-cover"
                      onError={(e) => {
                        e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(provider.businessName)}&background=random`
                      }}
                    />
                  </div>

                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{provider.businessName}</h3>
                    <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                      <MapPin className="w-3 h-3" />
                      <span>{provider.city}, {provider.countryCode}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-sm font-medium">{provider.rating}</span>
                        <span className="text-sm text-gray-500">({provider.reviewCount})</span>
                      </div>
                      <span className="text-sm text-gray-500">•</span>
                      <span className="text-sm text-gray-600">{provider.completedBookings} bookings</span>
                    </div>

                    <p className="text-sm text-gray-600 line-clamp-2 mb-3">{provider.description}</p>

                    <div className="flex flex-wrap gap-1 mb-3">
                      {provider.categories.slice(0, 2).map((category, catIndex) => (
                        <span key={catIndex} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                          {category}
                        </span>
                      ))}
                      {provider.categories.length > 2 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          +{provider.categories.length - 2} more
                        </span>
                      )}
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span className="font-semibold text-gray-900">
                        {provider.pricing.currency} {provider.pricing.basePrice}/{provider.pricing.priceType.replace('_', ' ')}
                      </span>
                      <span className="text-gray-600">Max {provider.capacity.maxGroup}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <button className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors">
                      View Details
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredProviders.map((provider, index) => (
              <motion.div
                key={provider.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="p-6 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                    <img
                      src={provider.logo}
                      alt={provider.businessName}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(provider.businessName)}&background=random`
                      }}
                    />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{provider.businessName}</h3>
                      <span className={`px-2 py-1 text-xs rounded-full ${getBusinessTypeColor(provider.businessType)}`}>
                        {provider.businessType.replace('_', ' ')}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getVerificationStatus(provider).color}`}>
                        {getVerificationStatus(provider).text}
                      </span>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {provider.countryCode}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-6 text-sm text-gray-600 mb-2">
                      <span className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        {provider.city}
                      </span>
                      <span className="flex items-center gap-1">
                        <Star className="w-3 h-3" />
                        {provider.rating} ({provider.reviewCount} reviews)
                      </span>
                      <span className="flex items-center gap-1">
                        <Users className="w-3 h-3" />
                        Max {provider.capacity.maxGroup} people
                      </span>
                      <span className="flex items-center gap-1">
                        <DollarSign className="w-3 h-3" />
                        {provider.pricing.currency} {provider.pricing.basePrice}
                      </span>
                      <span>{provider.completedBookings} bookings</span>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mb-2">
                      {provider.categories.map((category, catIndex) => (
                        <span key={catIndex} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          {category}
                        </span>
                      ))}
                    </div>
                    
                    <p className="text-sm text-gray-600 line-clamp-1">{provider.description}</p>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
