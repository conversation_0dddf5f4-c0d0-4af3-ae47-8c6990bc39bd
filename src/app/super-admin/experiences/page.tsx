'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Star,
  MapPin,
  Clock,
  Users,
  DollarSign,
  Calendar,
  Camera,
  Heart,
  Share2,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  Globe,
  Award,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  FileText,
  Image,
  Video
} from 'lucide-react'
import { useSuperAdmin } from '@/lib/auth/super-admin-auth'

interface Experience {
  id: string
  title: string
  description: string
  shortDescription: string
  countryCode: string
  city: string
  region: string
  category: string
  subcategories: string[]
  type: 'tour' | 'activity' | 'workshop' | 'adventure' | 'cultural' | 'culinary' | 'wellness'
  duration: {
    value: number
    unit: 'hours' | 'days'
  }
  groupSize: {
    min: number
    max: number
  }
  pricing: {
    basePrice: number
    currency: string
    priceType: 'per_person' | 'per_group'
    seasonalRates: boolean
  }
  difficulty: 'easy' | 'moderate' | 'challenging' | 'expert'
  languages: string[]
  includes: string[]
  excludes: string[]
  requirements: string[]
  highlights: string[]
  itinerary: {
    step: number
    title: string
    description: string
    duration: number
    location?: string
  }[]
  providerId: string
  providerName: string
  guideIds: string[]
  rating: number
  reviewCount: number
  bookingCount: number
  status: 'draft' | 'pending_review' | 'approved' | 'published' | 'suspended'
  isAuthentic: boolean
  isSustainable: boolean
  isFeatured: boolean
  tags: string[]
  media: {
    id: string
    type: 'image' | 'video' | '360_photo'
    url: string
    caption: string
    isPrimary: boolean
  }[]
  availability: {
    startDate: Date
    endDate: Date
    daysOfWeek: number[]
    timeSlots: string[]
  }
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
  lastBooking?: Date
}

export default function ExperiencesPage() {
  const { user, hasPermission } = useSuperAdmin()
  const [experiences, setExperiences] = useState<Experience[]>([])
  const [selectedCountry, setSelectedCountry] = useState<string>('all')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data
  useEffect(() => {
    const mockExperiences: Experience[] = [
      {
        id: '1',
        title: 'Sahara Desert Camel Trek & Overnight Camping',
        description: 'Experience the magic of the Sahara Desert with a traditional camel trek followed by an authentic overnight camping experience under the stars. Learn about Berber culture, enjoy traditional music, and witness breathtaking sunrises and sunsets over the golden dunes.',
        shortDescription: 'Traditional camel trek with overnight desert camping and Berber cultural immersion.',
        countryCode: 'MAR',
        city: 'Merzouga',
        region: 'Sahara Desert',
        category: 'Adventure & Nature',
        subcategories: ['Desert Adventures', 'Cultural Immersion', 'Camping'],
        type: 'adventure',
        duration: { value: 2, unit: 'days' },
        groupSize: { min: 2, max: 12 },
        pricing: {
          basePrice: 180,
          currency: 'USD',
          priceType: 'per_person',
          seasonalRates: true
        },
        difficulty: 'moderate',
        languages: ['English', 'French', 'Arabic', 'Berber'],
        includes: [
          'Professional guide',
          'Camel trekking',
          'Desert camp accommodation',
          'All meals (dinner & breakfast)',
          'Traditional music performance',
          'Sandboarding',
          'Transportation from Merzouga'
        ],
        excludes: ['Personal expenses', 'Tips', 'Travel insurance'],
        requirements: ['Moderate fitness level', 'Sun protection', 'Comfortable walking shoes'],
        highlights: [
          'Authentic Berber desert camp',
          'Spectacular sunrise & sunset views',
          'Traditional camel caravan experience',
          'Stargazing in pristine dark skies',
          'Local Berber guide stories and culture'
        ],
        itinerary: [
          {
            step: 1,
            title: 'Departure & Camel Trek',
            description: 'Meet your guide and begin the camel trek into the desert',
            duration: 90,
            location: 'Merzouga Village'
          },
          {
            step: 2,
            title: 'Desert Camp & Sunset',
            description: 'Arrive at camp, enjoy tea and watch the sunset',
            duration: 120,
            location: 'Desert Camp'
          },
          {
            step: 3,
            title: 'Traditional Dinner & Music',
            description: 'Authentic Berber dinner with traditional music around the fire',
            duration: 180,
            location: 'Desert Camp'
          },
          {
            step: 4,
            title: 'Sunrise & Return Trek',
            description: 'Watch sunrise and trek back to Merzouga',
            duration: 120,
            location: 'Desert Camp to Merzouga'
          }
        ],
        providerId: 'provider-1',
        providerName: 'Atlas Adventures Morocco',
        guideIds: ['guide-1', 'guide-2'],
        rating: 4.9,
        reviewCount: 127,
        bookingCount: 342,
        status: 'published',
        isAuthentic: true,
        isSustainable: true,
        isFeatured: true,
        tags: ['desert', 'camel', 'camping', 'berber', 'adventure', 'authentic', 'sustainable'],
        media: [
          {
            id: '1',
            type: 'image',
            url: '/images/experiences/sahara-camel-trek.jpg',
            caption: 'Camel caravan in the Sahara Desert',
            isPrimary: true
          },
          {
            id: '2',
            type: 'image',
            url: '/images/experiences/desert-camp.jpg',
            caption: 'Traditional Berber desert camp',
            isPrimary: false
          },
          {
            id: '3',
            type: 'video',
            url: '/videos/experiences/sahara-sunset.mp4',
            caption: 'Spectacular Sahara sunset',
            isPrimary: false
          }
        ],
        availability: {
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
          daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
          timeSlots: ['14:00']
        },
        createdAt: new Date('2023-12-01'),
        updatedAt: new Date('2024-01-20'),
        publishedAt: new Date('2023-12-15'),
        lastBooking: new Date('2024-01-24')
      },
      {
        id: '2',
        title: 'Traditional Japanese Tea Ceremony Experience',
        description: 'Immerse yourself in the ancient art of Japanese tea ceremony (chanoyu) in a traditional tatami room. Learn the precise movements, philosophy, and cultural significance of this meditative practice while enjoying authentic matcha and traditional sweets.',
        shortDescription: 'Authentic tea ceremony experience in traditional setting with cultural insights.',
        countryCode: 'JPN',
        city: 'Tokyo',
        region: 'Shibuya',
        category: 'Cultural & Traditional',
        subcategories: ['Traditional Arts', 'Cultural Workshops', 'Meditation'],
        type: 'cultural',
        duration: { value: 2, unit: 'hours' },
        groupSize: { min: 1, max: 8 },
        pricing: {
          basePrice: 65,
          currency: 'USD',
          priceType: 'per_person',
          seasonalRates: false
        },
        difficulty: 'easy',
        languages: ['English', 'Japanese'],
        includes: [
          'Tea ceremony instruction',
          'Traditional matcha tea',
          'Japanese sweets (wagashi)',
          'Cultural explanation',
          'Take-home tea ceremony guide'
        ],
        excludes: ['Transportation', 'Additional refreshments'],
        requirements: ['Ability to sit on tatami mats', 'Respectful attitude'],
        highlights: [
          'Authentic tea ceremony in traditional setting',
          'Learn proper tea ceremony etiquette',
          'Understand the philosophy of "wa-kei-sei-jaku"',
          'Taste premium matcha and seasonal sweets',
          'Small group for personalized attention'
        ],
        itinerary: [
          {
            step: 1,
            title: 'Welcome & Introduction',
            description: 'Introduction to tea ceremony history and philosophy',
            duration: 20,
            location: 'Traditional Tea Room'
          },
          {
            step: 2,
            title: 'Demonstration',
            description: 'Watch master perform complete tea ceremony',
            duration: 30,
            location: 'Traditional Tea Room'
          },
          {
            step: 3,
            title: 'Hands-on Practice',
            description: 'Practice the movements and prepare your own tea',
            duration: 60,
            location: 'Traditional Tea Room'
          },
          {
            step: 4,
            title: 'Reflection & Q&A',
            description: 'Discuss the experience and cultural significance',
            duration: 10,
            location: 'Traditional Tea Room'
          }
        ],
        providerId: 'provider-2',
        providerName: 'Tokyo Cultural Experiences',
        guideIds: ['guide-3'],
        rating: 4.8,
        reviewCount: 89,
        bookingCount: 167,
        status: 'published',
        isAuthentic: true,
        isSustainable: false,
        isFeatured: false,
        tags: ['tea ceremony', 'traditional', 'cultural', 'meditation', 'authentic', 'tokyo'],
        media: [
          {
            id: '4',
            type: 'image',
            url: '/images/experiences/tea-ceremony.jpg',
            caption: 'Traditional Japanese tea ceremony',
            isPrimary: true
          },
          {
            id: '5',
            type: 'image',
            url: '/images/experiences/matcha-preparation.jpg',
            caption: 'Matcha tea preparation',
            isPrimary: false
          }
        ],
        availability: {
          startDate: new Date('2024-01-01'),
          endDate: new Date('2024-12-31'),
          daysOfWeek: [1, 2, 3, 4, 5, 6],
          timeSlots: ['10:00', '14:00', '16:00']
        },
        createdAt: new Date('2023-11-15'),
        updatedAt: new Date('2024-01-18'),
        publishedAt: new Date('2023-12-01'),
        lastBooking: new Date('2024-01-23')
      },
      {
        id: '3',
        title: 'Barcelona Tapas & Market Food Tour',
        description: 'Discover Barcelona\'s vibrant food scene with a local foodie guide. Visit traditional markets, family-run tapas bars, and hidden gems known only to locals. Taste authentic Catalan specialties and learn about the city\'s culinary traditions.',
        shortDescription: 'Authentic food tour through Barcelona\'s markets and traditional tapas bars.',
        countryCode: 'ESP',
        city: 'Barcelona',
        region: 'Gothic Quarter',
        category: 'Food & Culinary',
        subcategories: ['Food Tours', 'Market Visits', 'Local Cuisine'],
        type: 'culinary',
        duration: { value: 4, unit: 'hours' },
        groupSize: { min: 4, max: 12 },
        pricing: {
          basePrice: 85,
          currency: 'EUR',
          priceType: 'per_person',
          seasonalRates: true
        },
        difficulty: 'easy',
        languages: ['English', 'Spanish', 'Catalan'],
        includes: [
          'Local food guide',
          'Market visit',
          '6-8 food tastings',
          'Traditional tapas',
          'Local wine/beer tasting',
          'Cultural insights'
        ],
        excludes: ['Full meals', 'Additional drinks', 'Transportation'],
        requirements: ['Comfortable walking shoes', 'Appetite for adventure'],
        highlights: [
          'Visit authentic local markets',
          'Taste traditional Catalan specialties',
          'Learn about Barcelona\'s food culture',
          'Discover hidden local gems',
          'Small group experience'
        ],
        itinerary: [
          {
            step: 1,
            title: 'Market Exploration',
            description: 'Explore Boqueria Market and taste fresh products',
            duration: 60,
            location: 'La Boqueria Market'
          },
          {
            step: 2,
            title: 'Traditional Tapas Bar',
            description: 'Visit family-run tapas bar for authentic dishes',
            duration: 45,
            location: 'Gothic Quarter'
          },
          {
            step: 3,
            title: 'Local Specialties',
            description: 'Taste Catalan specialties and local wines',
            duration: 60,
            location: 'El Born District'
          },
          {
            step: 4,
            title: 'Sweet Ending',
            description: 'Traditional desserts and coffee',
            duration: 35,
            location: 'Local Pastry Shop'
          }
        ],
        providerId: 'provider-3',
        providerName: 'Barcelona Gourmet Tours',
        guideIds: ['guide-4'],
        rating: 4.6,
        reviewCount: 124,
        bookingCount: 289,
        status: 'pending_review',
        isAuthentic: true,
        isSustainable: true,
        isFeatured: false,
        tags: ['food tour', 'tapas', 'market', 'catalan', 'local', 'authentic'],
        media: [
          {
            id: '6',
            type: 'image',
            url: '/images/experiences/barcelona-tapas.jpg',
            caption: 'Traditional Catalan tapas',
            isPrimary: true
          },
          {
            id: '7',
            type: 'image',
            url: '/images/experiences/boqueria-market.jpg',
            caption: 'La Boqueria Market',
            isPrimary: false
          }
        ],
        availability: {
          startDate: new Date('2024-03-01'),
          endDate: new Date('2024-11-30'),
          daysOfWeek: [1, 2, 3, 4, 5, 6],
          timeSlots: ['10:30', '15:30']
        },
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-22'),
        lastBooking: new Date('2024-01-20')
      }
    ]
    
    setExperiences(mockExperiences)
    setIsLoading(false)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'text-green-700 bg-green-100'
      case 'approved': return 'text-blue-700 bg-blue-100'
      case 'pending_review': return 'text-yellow-700 bg-yellow-100'
      case 'draft': return 'text-gray-700 bg-gray-100'
      case 'suspended': return 'text-red-700 bg-red-100'
      default: return 'text-gray-700 bg-gray-100'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-700 bg-green-100'
      case 'moderate': return 'text-yellow-700 bg-yellow-100'
      case 'challenging': return 'text-orange-700 bg-orange-100'
      case 'expert': return 'text-red-700 bg-red-100'
      default: return 'text-gray-700 bg-gray-100'
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating) ? 'text-yellow-500 fill-current' : 'text-gray-300'
        }`}
      />
    ))
  }

  const filteredExperiences = experiences.filter(exp => {
    const matchesCountry = selectedCountry === 'all' || exp.countryCode === selectedCountry
    const matchesCategory = selectedCategory === 'all' || exp.category === selectedCategory
    const matchesStatus = selectedStatus === 'all' || exp.status === selectedStatus
    const matchesType = selectedType === 'all' || exp.type === selectedType
    const matchesSearch = searchQuery === '' || 
      exp.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      exp.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      exp.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
      exp.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    return matchesCountry && matchesCategory && matchesStatus && matchesType && matchesSearch
  })

  if (!hasPermission('canManageContent')) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold">Access Denied</h3>
          <p className="text-red-600">You don't have permission to manage experiences.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Experience Management</h1>
          <p className="text-gray-600">Curate and manage authentic travel experiences across all destinations</p>
        </div>
        
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <TrendingUp className="w-4 h-4" />
            Analytics
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Plus className="w-4 h-4" />
            Add Experience
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Experiences</p>
              <p className="text-2xl font-bold text-gray-900">{experiences.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Published</p>
              <p className="text-2xl font-bold text-green-600">
                {experiences.filter(e => e.status === 'published').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Authentic</p>
              <p className="text-2xl font-bold text-purple-600">
                {experiences.filter(e => e.isAuthentic).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Award className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Rating</p>
              <p className="text-2xl font-bold text-yellow-600">
                {(experiences.reduce((sum, e) => sum + e.rating, 0) / experiences.length).toFixed(1)}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Bookings</p>
              <p className="text-2xl font-bold text-indigo-600">
                {experiences.reduce((sum, e) => sum + e.bookingCount, 0)}
              </p>
            </div>
            <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-6 h-6 text-indigo-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search experiences..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <select
            value={selectedCountry}
            onChange={(e) => setSelectedCountry(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Countries</option>
            <option value="MAR">🇲🇦 Morocco</option>
            <option value="JPN">🇯🇵 Japan</option>
            <option value="ESP">🇪🇸 Spain</option>
            <option value="ITA">🇮🇹 Italy</option>
          </select>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Categories</option>
            <option value="Adventure & Nature">Adventure & Nature</option>
            <option value="Cultural & Traditional">Cultural & Traditional</option>
            <option value="Food & Culinary">Food & Culinary</option>
            <option value="Arts & Crafts">Arts & Crafts</option>
            <option value="Wellness & Spirituality">Wellness & Spirituality</option>
          </select>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Types</option>
            <option value="tour">Tours</option>
            <option value="activity">Activities</option>
            <option value="workshop">Workshops</option>
            <option value="adventure">Adventures</option>
            <option value="cultural">Cultural</option>
            <option value="culinary">Culinary</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="draft">Draft</option>
            <option value="pending_review">Pending Review</option>
            <option value="approved">Approved</option>
            <option value="published">Published</option>
            <option value="suspended">Suspended</option>
          </select>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <Star className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <FileText className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Experiences Grid/List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Experiences ({filteredExperiences.length})</h3>
        </div>
        
        {viewMode === 'grid' ? (
          <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredExperiences.map((experience, index) => (
              <motion.div
                key={experience.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                {/* Image */}
                <div className="h-48 bg-gray-100 relative">
                  <img
                    src={experience.media.find(m => m.isPrimary)?.url || '/images/placeholder-experience.jpg'}
                    alt={experience.title}
                    className="w-full h-full object-cover"
                  />
                  
                  <div className="absolute top-3 left-3 flex gap-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(experience.status)}`}>
                      {experience.status.replace('_', ' ')}
                    </span>
                    {experience.isFeatured && (
                      <span className="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-700">
                        Featured
                      </span>
                    )}
                  </div>
                  
                  <div className="absolute top-3 right-3 flex gap-2">
                    {experience.isAuthentic && (
                      <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-700">
                        <Award className="w-3 h-3 inline mr-1" />
                        Authentic
                      </span>
                    )}
                    {experience.isSustainable && (
                      <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-700">
                        Sustainable
                      </span>
                    )}
                  </div>
                  
                  <div className="absolute bottom-3 right-3">
                    <span className="px-2 py-1 text-xs rounded bg-black bg-opacity-75 text-white">
                      {experience.media.length} media
                    </span>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <div className="mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{experience.title}</h3>
                    
                    <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                      <MapPin className="w-3 h-3" />
                      <span>{experience.city}, {experience.countryCode}</span>
                      <span>•</span>
                      <span>{experience.category}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center gap-1">
                        {renderStars(experience.rating)}
                        <span className="text-sm font-medium ml-1">{experience.rating}</span>
                        <span className="text-sm text-gray-500">({experience.reviewCount})</span>
                      </div>
                    </div>

                    <p className="text-sm text-gray-600 line-clamp-3 mb-3">{experience.shortDescription}</p>

                    <div className="flex items-center justify-between text-sm mb-3">
                      <div className="flex items-center gap-4">
                        <span className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {experience.duration.value} {experience.duration.unit}
                        </span>
                        <span className="flex items-center gap-1">
                          <Users className="w-3 h-3" />
                          {experience.groupSize.min}-{experience.groupSize.max}
                        </span>
                      </div>
                      <span className={`px-2 py-1 text-xs rounded ${getDifficultyColor(experience.difficulty)}`}>
                        {experience.difficulty}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-lg font-bold text-gray-900">
                        {experience.pricing.currency} {experience.pricing.basePrice}
                        <span className="text-sm font-normal text-gray-600">
                          /{experience.pricing.priceType.replace('_', ' ')}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        {experience.bookingCount} bookings
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <button className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors">
                      View Details
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredExperiences.map((experience, index) => (
              <motion.div
                key={experience.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="p-6 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start gap-4">
                  <div className="w-24 h-24 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                    <img
                      src={experience.media.find(m => m.isPrimary)?.url || '/images/placeholder-experience.jpg'}
                      alt={experience.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">{experience.title}</h3>
                        <div className="flex items-center gap-3 mb-2">
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(experience.status)}`}>
                            {experience.status.replace('_', ' ')}
                          </span>
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            {experience.countryCode}
                          </span>
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            {experience.type}
                          </span>
                          {experience.isAuthentic && (
                            <span className="text-xs text-green-700 bg-green-100 px-2 py-1 rounded">
                              Authentic
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="text-lg font-bold text-gray-900">
                          {experience.pricing.currency} {experience.pricing.basePrice}
                        </div>
                        <div className="text-sm text-gray-600">
                          {experience.pricing.priceType.replace('_', ' ')}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-6 text-sm text-gray-600 mb-2">
                      <span className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        {experience.city}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {experience.duration.value} {experience.duration.unit}
                      </span>
                      <span className="flex items-center gap-1">
                        <Users className="w-3 h-3" />
                        {experience.groupSize.min}-{experience.groupSize.max} people
                      </span>
                      <span className="flex items-center gap-1">
                        <Star className="w-3 h-3" />
                        {experience.rating} ({experience.reviewCount} reviews)
                      </span>
                      <span>{experience.bookingCount} bookings</span>
                    </div>
                    
                    <p className="text-sm text-gray-600 line-clamp-2 mb-2">{experience.shortDescription}</p>
                    
                    <div className="flex flex-wrap gap-1">
                      {experience.tags.slice(0, 5).map((tag, tagIndex) => (
                        <span key={tagIndex} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          {tag}
                        </span>
                      ))}
                      {experience.tags.length > 5 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          +{experience.tags.length - 5} more
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
