'use client'

import { SuperAdminProvider } from '@/lib/auth/super-admin-auth'
import { SuperAdminSidebar } from '@/components/super-admin/super-admin-sidebar'
import { SuperAdminHeader } from '@/components/super-admin/super-admin-header'

export default function SuperAdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <SuperAdminProvider>
      <div className="min-h-screen bg-slate-50">
        <SuperAdminSidebar />
        <div className="lg:pl-72">
          <SuperAdminHeader />
          <main className="py-8">
            <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </main>
        </div>
      </div>
    </SuperAdminProvider>
  )
}
