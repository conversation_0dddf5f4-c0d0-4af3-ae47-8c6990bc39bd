'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  UserPlus,
  Building2,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  Award,
  FileText,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Users,
  Star,
  Upload,
  Download,
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Send,
  Globe,
  Shield,
  TrendingUp
} from 'lucide-react'
import { useSuperAdmin } from '@/lib/auth/super-admin-auth'

interface OnboardingApplication {
  id: string
  type: 'guide' | 'provider'
  applicantName: string
  businessName?: string
  email: string
  phone: string
  countryCode: string
  city: string
  specializations: string[]
  experience: number
  languages: string[]
  status: 'submitted' | 'under_review' | 'interview_scheduled' | 'approved' | 'rejected' | 'onboarded'
  submittedAt: Date
  reviewedAt?: Date
  reviewedBy?: string
  interviewDate?: Date
  onboardedAt?: Date
  documents: {
    id: string
    type: string
    name: string
    status: 'pending' | 'approved' | 'rejected'
    url: string
    uploadedAt: Date
  }[]
  notes: string
  score?: number
  certificationLevel?: 'bronze' | 'silver' | 'gold' | 'platinum'
  trainingCompleted: string[]
  backgroundCheck: {
    status: 'pending' | 'passed' | 'failed'
    completedAt?: Date
    notes?: string
  }
}

interface CertificationProgram {
  id: string
  name: string
  level: 'bronze' | 'silver' | 'gold' | 'platinum'
  description: string
  requirements: string[]
  modules: {
    id: string
    name: string
    duration: number // hours
    type: 'online' | 'practical' | 'assessment'
    status: 'available' | 'coming_soon'
  }[]
  benefits: string[]
  validityPeriod: number // months
  cost: number
  currency: string
  enrolledCount: number
  completedCount: number
  passRate: number
}

export default function OnboardingPage() {
  const { user, hasPermission } = useSuperAdmin()
  const [activeTab, setActiveTab] = useState<'applications' | 'certifications'>('applications')
  const [applications, setApplications] = useState<OnboardingApplication[]>([])
  const [certifications, setCertifications] = useState<CertificationProgram[]>([])
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedCountry, setSelectedCountry] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data
  useEffect(() => {
    const mockApplications: OnboardingApplication[] = [
      {
        id: '1',
        type: 'guide',
        applicantName: 'Fatima Zahra',
        email: '<EMAIL>',
        phone: '+212 6 12 34 56 78',
        countryCode: 'MAR',
        city: 'Fes',
        specializations: ['Cultural Tours', 'Historical Sites', 'Traditional Crafts'],
        experience: 6,
        languages: ['Arabic', 'French', 'English'],
        status: 'under_review',
        submittedAt: new Date('2024-01-20'),
        reviewedAt: new Date('2024-01-22'),
        reviewedBy: 'Review Team Morocco',
        documents: [
          {
            id: '1',
            type: 'ID',
            name: 'National ID Card',
            status: 'approved',
            url: '/documents/fatima-id.pdf',
            uploadedAt: new Date('2024-01-20')
          },
          {
            id: '2',
            type: 'certification',
            name: 'Tourism Guide License',
            status: 'approved',
            url: '/documents/fatima-license.pdf',
            uploadedAt: new Date('2024-01-20')
          }
        ],
        notes: 'Excellent background in Moroccan history and traditional crafts. Strong language skills.',
        score: 8.5,
        trainingCompleted: ['Cultural Sensitivity', 'First Aid Basics'],
        backgroundCheck: {
          status: 'passed',
          completedAt: new Date('2024-01-21'),
          notes: 'Clean background, excellent references'
        }
      },
      {
        id: '2',
        type: 'provider',
        applicantName: 'Hiroshi Sato',
        businessName: 'Kyoto Traditional Arts',
        email: '<EMAIL>',
        phone: '+81 75 123 4567',
        countryCode: 'JPN',
        city: 'Kyoto',
        specializations: ['Tea Ceremony', 'Calligraphy', 'Kimono Experience'],
        experience: 12,
        languages: ['Japanese', 'English'],
        status: 'interview_scheduled',
        submittedAt: new Date('2024-01-18'),
        reviewedAt: new Date('2024-01-19'),
        reviewedBy: 'Review Team Japan',
        interviewDate: new Date('2024-01-26'),
        documents: [
          {
            id: '3',
            type: 'business_license',
            name: 'Business Registration',
            status: 'approved',
            url: '/documents/kyoto-business.pdf',
            uploadedAt: new Date('2024-01-18')
          },
          {
            id: '4',
            type: 'insurance',
            name: 'Liability Insurance',
            status: 'pending',
            url: '/documents/kyoto-insurance.pdf',
            uploadedAt: new Date('2024-01-18')
          }
        ],
        notes: 'Established business with excellent reputation. Needs insurance documentation update.',
        score: 9.2,
        trainingCompleted: ['Business Operations', 'Customer Service'],
        backgroundCheck: {
          status: 'passed',
          completedAt: new Date('2024-01-19')
        }
      },
      {
        id: '3',
        type: 'guide',
        applicantName: 'Carlos Mendez',
        email: '<EMAIL>',
        phone: '+34 6 12 34 56 78',
        countryCode: 'ESP',
        city: 'Seville',
        specializations: ['Flamenco Culture', 'Architecture', 'Gastronomy'],
        experience: 4,
        languages: ['Spanish', 'English', 'Portuguese'],
        status: 'approved',
        submittedAt: new Date('2024-01-15'),
        reviewedAt: new Date('2024-01-17'),
        reviewedBy: 'Review Team Spain',
        onboardedAt: new Date('2024-01-24'),
        documents: [
          {
            id: '5',
            type: 'ID',
            name: 'Spanish ID',
            status: 'approved',
            url: '/documents/carlos-id.pdf',
            uploadedAt: new Date('2024-01-15')
          }
        ],
        notes: 'Passionate about Andalusian culture. Completed all training modules.',
        score: 8.8,
        certificationLevel: 'silver',
        trainingCompleted: ['Cultural Sensitivity', 'First Aid Basics', 'Customer Service Excellence'],
        backgroundCheck: {
          status: 'passed',
          completedAt: new Date('2024-01-16')
        }
      }
    ]

    const mockCertifications: CertificationProgram[] = [
      {
        id: '1',
        name: 'Certified Local Guide - Bronze',
        level: 'bronze',
        description: 'Entry-level certification for new local guides covering basic tourism principles and customer service.',
        requirements: [
          'Minimum 1 year local experience',
          'Basic language proficiency',
          'Clean background check',
          'Complete all training modules'
        ],
        modules: [
          {
            id: '1',
            name: 'Tourism Fundamentals',
            duration: 4,
            type: 'online',
            status: 'available'
          },
          {
            id: '2',
            name: 'Customer Service Excellence',
            duration: 3,
            type: 'online',
            status: 'available'
          },
          {
            id: '3',
            name: 'Safety and First Aid',
            duration: 6,
            type: 'practical',
            status: 'available'
          },
          {
            id: '4',
            name: 'Final Assessment',
            duration: 2,
            type: 'assessment',
            status: 'available'
          }
        ],
        benefits: [
          'Platform listing eligibility',
          'Basic marketing support',
          'Access to booking system',
          'Community forum access'
        ],
        validityPeriod: 24,
        cost: 150,
        currency: 'USD',
        enrolledCount: 45,
        completedCount: 32,
        passRate: 89
      },
      {
        id: '2',
        name: 'Certified Local Guide - Silver',
        level: 'silver',
        description: 'Intermediate certification for experienced guides with specialized knowledge and advanced customer service skills.',
        requirements: [
          'Bronze certification',
          'Minimum 3 years experience',
          'Advanced language skills',
          '50+ completed tours',
          '4.5+ average rating'
        ],
        modules: [
          {
            id: '5',
            name: 'Advanced Cultural Knowledge',
            duration: 8,
            type: 'online',
            status: 'available'
          },
          {
            id: '6',
            name: 'Specialized Tour Design',
            duration: 6,
            type: 'practical',
            status: 'available'
          },
          {
            id: '7',
            name: 'Crisis Management',
            duration: 4,
            type: 'practical',
            status: 'available'
          },
          {
            id: '8',
            name: 'Advanced Assessment',
            duration: 3,
            type: 'assessment',
            status: 'available'
          }
        ],
        benefits: [
          'Premium listing placement',
          'Higher commission rates',
          'Marketing co-op programs',
          'Priority customer support',
          'Advanced analytics access'
        ],
        validityPeriod: 36,
        cost: 300,
        currency: 'USD',
        enrolledCount: 28,
        completedCount: 19,
        passRate: 82
      },
      {
        id: '3',
        name: 'Experience Provider Certification',
        level: 'gold',
        description: 'Comprehensive certification for experience providers covering business operations, quality standards, and customer excellence.',
        requirements: [
          'Valid business license',
          'Minimum 2 years operation',
          'Insurance coverage',
          'Quality management system',
          'Sustainability practices'
        ],
        modules: [
          {
            id: '9',
            name: 'Business Operations Excellence',
            duration: 10,
            type: 'online',
            status: 'available'
          },
          {
            id: '10',
            name: 'Quality Management Systems',
            duration: 8,
            type: 'practical',
            status: 'available'
          },
          {
            id: '11',
            name: 'Sustainable Tourism Practices',
            duration: 6,
            type: 'online',
            status: 'available'
          },
          {
            id: '12',
            name: 'Digital Marketing for Tourism',
            duration: 4,
            type: 'online',
            status: 'coming_soon'
          }
        ],
        benefits: [
          'Featured provider status',
          'Reduced platform fees',
          'Marketing partnership opportunities',
          'Priority booking placement',
          'Dedicated account management'
        ],
        validityPeriod: 24,
        cost: 500,
        currency: 'USD',
        enrolledCount: 15,
        completedCount: 8,
        passRate: 94
      }
    ]
    
    setApplications(mockApplications)
    setCertifications(mockCertifications)
    setIsLoading(false)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': case 'onboarded': case 'passed': return 'text-green-700 bg-green-100'
      case 'under_review': case 'interview_scheduled': case 'pending': return 'text-blue-700 bg-blue-100'
      case 'submitted': return 'text-yellow-700 bg-yellow-100'
      case 'rejected': case 'failed': return 'text-red-700 bg-red-100'
      default: return 'text-gray-700 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': case 'onboarded': case 'passed': return <CheckCircle className="w-4 h-4" />
      case 'under_review': case 'interview_scheduled': case 'pending': return <Clock className="w-4 h-4" />
      case 'submitted': return <AlertTriangle className="w-4 h-4" />
      case 'rejected': case 'failed': return <XCircle className="w-4 h-4" />
      default: return <Clock className="w-4 h-4" />
    }
  }

  const getCertificationLevelColor = (level: string) => {
    switch (level) {
      case 'bronze': return 'text-orange-700 bg-orange-100'
      case 'silver': return 'text-gray-700 bg-gray-100'
      case 'gold': return 'text-yellow-700 bg-yellow-100'
      case 'platinum': return 'text-purple-700 bg-purple-100'
      default: return 'text-gray-700 bg-gray-100'
    }
  }

  const filteredApplications = applications.filter(app => {
    const matchesType = selectedType === 'all' || app.type === selectedType
    const matchesStatus = selectedStatus === 'all' || app.status === selectedStatus
    const matchesCountry = selectedCountry === 'all' || app.countryCode === selectedCountry
    const matchesSearch = searchQuery === '' || 
      app.applicantName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (app.businessName && app.businessName.toLowerCase().includes(searchQuery.toLowerCase())) ||
      app.city.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesType && matchesStatus && matchesCountry && matchesSearch
  })

  if (!hasPermission('canManageContent')) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold">Access Denied</h3>
          <p className="text-red-600">You don't have permission to manage onboarding.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Partner Onboarding & Certification</h1>
          <p className="text-gray-600">Manage partner applications and certification programs</p>
        </div>
        
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <TrendingUp className="w-4 h-4" />
            Analytics
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Plus className="w-4 h-4" />
            Invite Partner
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Applications</p>
              <p className="text-2xl font-bold text-gray-900">{applications.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <UserPlus className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-green-600">
                {applications.filter(a => a.status === 'approved' || a.status === 'onboarded').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Under Review</p>
              <p className="text-2xl font-bold text-blue-600">
                {applications.filter(a => a.status === 'under_review' || a.status === 'interview_scheduled').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Certifications</p>
              <p className="text-2xl font-bold text-purple-600">{certifications.length}</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Award className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Score</p>
              <p className="text-2xl font-bold text-yellow-600">
                {applications.filter(a => a.score).length > 0 
                  ? (applications.filter(a => a.score).reduce((sum, a) => sum + (a.score || 0), 0) / applications.filter(a => a.score).length).toFixed(1)
                  : 'N/A'
                }
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('applications')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'applications'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <UserPlus className="w-4 h-4 inline mr-2" />
              Applications ({applications.length})
            </button>
            <button
              onClick={() => setActiveTab('certifications')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'certifications'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Award className="w-4 h-4 inline mr-2" />
              Certifications ({certifications.length})
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'applications' ? (
            <>
              {/* Filters */}
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search applications..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Types</option>
                  <option value="guide">Guides</option>
                  <option value="provider">Providers</option>
                </select>

                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="submitted">Submitted</option>
                  <option value="under_review">Under Review</option>
                  <option value="interview_scheduled">Interview Scheduled</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                  <option value="onboarded">Onboarded</option>
                </select>

                <select
                  value={selectedCountry}
                  onChange={(e) => setSelectedCountry(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Countries</option>
                  <option value="MAR">🇲🇦 Morocco</option>
                  <option value="JPN">🇯🇵 Japan</option>
                  <option value="ESP">🇪🇸 Spain</option>
                  <option value="ITA">🇮🇹 Italy</option>
                </select>

                <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                  <Filter className="w-4 h-4 inline mr-2" />
                  More Filters
                </button>
              </div>

              {/* Applications List */}
              <div className="space-y-4">
                {filteredApplications.map((application, index) => (
                  <motion.div
                    key={application.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="bg-gray-50 rounded-lg p-6"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-3">
                          <h4 className="text-lg font-semibold text-gray-900">
                            {application.applicantName}
                            {application.businessName && (
                              <span className="text-gray-600 font-normal"> - {application.businessName}</span>
                            )}
                          </h4>
                          <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(application.status)}`}>
                            {getStatusIcon(application.status)}
                            <span className="ml-1">{application.status.replace('_', ' ')}</span>
                          </span>
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            {application.type}
                          </span>
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            {application.countryCode}
                          </span>
                          {application.certificationLevel && (
                            <span className={`px-2 py-1 text-xs rounded-full ${getCertificationLevelColor(application.certificationLevel)}`}>
                              {application.certificationLevel}
                            </span>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div>
                            <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                              <Mail className="w-3 h-3" />
                              <span>{application.email}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
                              <Phone className="w-3 h-3" />
                              <span>{application.phone}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <MapPin className="w-3 h-3" />
                              <span>{application.city}, {application.countryCode}</span>
                            </div>
                          </div>
                          
                          <div>
                            <div className="text-sm text-gray-600 mb-1">
                              <strong>Experience:</strong> {application.experience} years
                            </div>
                            <div className="text-sm text-gray-600 mb-1">
                              <strong>Languages:</strong> {application.languages.join(', ')}
                            </div>
                            {application.score && (
                              <div className="text-sm text-gray-600">
                                <strong>Score:</strong> <span className="text-green-600 font-medium">{application.score}/10</span>
                              </div>
                            )}
                          </div>
                          
                          <div>
                            <div className="text-sm text-gray-600 mb-1">
                              <strong>Submitted:</strong> {application.submittedAt.toLocaleDateString()}
                            </div>
                            {application.interviewDate && (
                              <div className="text-sm text-gray-600 mb-1">
                                <strong>Interview:</strong> {application.interviewDate.toLocaleDateString()}
                              </div>
                            )}
                            <div className="text-sm text-gray-600">
                              <strong>Documents:</strong> {application.documents.length}
                            </div>
                          </div>
                        </div>
                        
                        <div className="mb-4">
                          <div className="text-sm text-gray-600 mb-2">
                            <strong>Specializations:</strong>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {application.specializations.map((spec, specIndex) => (
                              <span key={specIndex} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                                {spec}
                              </span>
                            ))}
                          </div>
                        </div>
                        
                        {application.notes && (
                          <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                            <div className="text-sm font-medium text-blue-900 mb-1">Review Notes:</div>
                            <div className="text-sm text-blue-800">{application.notes}</div>
                          </div>
                        )}
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span className={`flex items-center gap-1 ${getStatusColor(application.backgroundCheck.status)}`}>
                            <Shield className="w-3 h-3" />
                            Background: {application.backgroundCheck.status}
                          </span>
                          <span>Training: {application.trainingCompleted.length} modules</span>
                          {application.reviewedBy && (
                            <span>Reviewed by: {application.reviewedBy}</span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                          <Eye className="w-4 h-4" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                          <CheckCircle className="w-4 h-4" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                          <XCircle className="w-4 h-4" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-purple-600 transition-colors">
                          <Send className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </>
          ) : (
            <div className="space-y-6">
              {certifications.map((cert, index) => (
                <motion.div
                  key={cert.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-gray-50 rounded-lg p-6"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <h4 className="text-xl font-semibold text-gray-900">{cert.name}</h4>
                        <span className={`px-3 py-1 text-sm rounded-full ${getCertificationLevelColor(cert.level)}`}>
                          <Award className="w-4 h-4 inline mr-1" />
                          {cert.level.toUpperCase()}
                        </span>
                      </div>
                      
                      <p className="text-gray-700 mb-4">{cert.description}</p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <div>
                          <h5 className="font-medium text-gray-900 mb-2">Requirements:</h5>
                          <ul className="text-sm text-gray-600 space-y-1">
                            {cert.requirements.map((req, reqIndex) => (
                              <li key={reqIndex} className="flex items-start gap-2">
                                <span className="text-blue-500 mt-1">•</span>
                                {req}
                              </li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h5 className="font-medium text-gray-900 mb-2">Training Modules:</h5>
                          <div className="space-y-2">
                            {cert.modules.map((module, moduleIndex) => (
                              <div key={moduleIndex} className="flex items-center justify-between text-sm">
                                <span className="text-gray-700">{module.name}</span>
                                <span className="text-gray-500">{module.duration}h</span>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <div>
                          <h5 className="font-medium text-gray-900 mb-2">Benefits:</h5>
                          <ul className="text-sm text-gray-600 space-y-1">
                            {cert.benefits.map((benefit, benefitIndex) => (
                              <li key={benefitIndex} className="flex items-start gap-2">
                                <span className="text-green-500 mt-1">•</span>
                                {benefit}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-center">
                        <div className="bg-white rounded-lg p-3">
                          <div className="text-lg font-bold text-blue-600">{cert.enrolledCount}</div>
                          <div className="text-xs text-gray-600">Enrolled</div>
                        </div>
                        <div className="bg-white rounded-lg p-3">
                          <div className="text-lg font-bold text-green-600">{cert.completedCount}</div>
                          <div className="text-xs text-gray-600">Completed</div>
                        </div>
                        <div className="bg-white rounded-lg p-3">
                          <div className="text-lg font-bold text-purple-600">{cert.passRate}%</div>
                          <div className="text-xs text-gray-600">Pass Rate</div>
                        </div>
                        <div className="bg-white rounded-lg p-3">
                          <div className="text-lg font-bold text-orange-600">{cert.cost} {cert.currency}</div>
                          <div className="text-xs text-gray-600">Cost</div>
                        </div>
                        <div className="bg-white rounded-lg p-3">
                          <div className="text-lg font-bold text-gray-600">{cert.validityPeriod}m</div>
                          <div className="text-xs text-gray-600">Validity</div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                        <Edit className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
