'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Users,
  MapPin,
  Star,
  Calendar,
  Phone,
  Mail,
  Globe,
  Award,
  Clock,
  DollarSign,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Camera,
  Languages,
  Shield,
  TrendingUp
} from 'lucide-react'
import { useSuperAdmin } from '@/lib/auth/super-admin-auth'

interface Guide {
  id: string
  name: string
  email: string
  phone: string
  profileImage: string
  coverImage: string
  countryCode: string
  city: string
  region: string
  languages: string[]
  specializations: string[]
  certifications: string[]
  experience: number // years
  rating: number
  reviewCount: number
  completedTours: number
  pricePerDay: number
  pricePerHour: number
  availability: 'available' | 'busy' | 'unavailable'
  isVerified: boolean
  isActive: boolean
  joinedAt: Date
  lastActive: Date
  bio: string
  highlights: string[]
  socialMedia: {
    instagram?: string
    facebook?: string
    website?: string
  }
  bankingInfo: {
    accountHolder: string
    accountNumber: string
    bankName: string
    isVerified: boolean
  }
  documents: {
    id: string
    type: 'license' | 'certification' | 'insurance' | 'passport'
    url: string
    status: 'pending' | 'approved' | 'rejected'
    uploadedAt: Date
  }[]
}

export default function GuideManagementPage() {
  const { user, hasPermission } = useSuperAdmin()
  const [guides, setGuides] = useState<Guide[]>([])
  const [selectedCountry, setSelectedCountry] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [selectedAvailability, setSelectedAvailability] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data
  useEffect(() => {
    const mockGuides: Guide[] = [
      {
        id: '1',
        name: 'Ahmed El Mansouri',
        email: '<EMAIL>',
        phone: '+212 6 12 34 56 78',
        profileImage: '/images/guides/ahmed.jpg',
        coverImage: '/images/guides/ahmed-cover.jpg',
        countryCode: 'MAR',
        city: 'Marrakech',
        region: 'Marrakech-Safi',
        languages: ['Arabic', 'French', 'English', 'Berber'],
        specializations: ['Cultural Tours', 'Desert Expeditions', 'Photography', 'History'],
        certifications: ['Licensed Tour Guide', 'First Aid Certified', 'Desert Safety'],
        experience: 8,
        rating: 4.9,
        reviewCount: 127,
        completedTours: 340,
        pricePerDay: 80,
        pricePerHour: 15,
        availability: 'available',
        isVerified: true,
        isActive: true,
        joinedAt: new Date('2020-03-15'),
        lastActive: new Date('2024-01-25'),
        bio: 'Passionate about sharing the rich culture and history of Morocco. Specialized in desert expeditions and cultural immersion experiences.',
        highlights: ['8+ years experience', '340+ tours completed', 'Desert specialist', 'Multilingual'],
        socialMedia: {
          instagram: '@ahmed_morocco_guide',
          website: 'www.ahmedguide.com'
        },
        bankingInfo: {
          accountHolder: 'Ahmed El Mansouri',
          accountNumber: '****1234',
          bankName: 'Attijariwafa Bank',
          isVerified: true
        },
        documents: [
          {
            id: '1',
            type: 'license',
            url: '/documents/ahmed-license.pdf',
            status: 'approved',
            uploadedAt: new Date('2020-03-15')
          },
          {
            id: '2',
            type: 'certification',
            url: '/documents/ahmed-cert.pdf',
            status: 'approved',
            uploadedAt: new Date('2020-03-20')
          }
        ]
      },
      {
        id: '2',
        name: 'Yuki Tanaka',
        email: '<EMAIL>',
        phone: '+81 90 1234 5678',
        profileImage: '/images/guides/yuki.jpg',
        coverImage: '/images/guides/yuki-cover.jpg',
        countryCode: 'JPN',
        city: 'Tokyo',
        region: 'Kanto',
        languages: ['Japanese', 'English', 'Mandarin'],
        specializations: ['Temple Tours', 'Traditional Culture', 'Food Tours', 'Art & Museums'],
        certifications: ['National Guide License', 'Cultural Heritage Specialist'],
        experience: 5,
        rating: 4.8,
        reviewCount: 89,
        completedTours: 156,
        pricePerDay: 120,
        pricePerHour: 25,
        availability: 'busy',
        isVerified: true,
        isActive: true,
        joinedAt: new Date('2021-06-10'),
        lastActive: new Date('2024-01-24'),
        bio: 'Tokyo native with deep knowledge of traditional Japanese culture and modern city life. Specializing in temple visits and cultural experiences.',
        highlights: ['5+ years experience', '156+ tours completed', 'Temple specialist', 'Cultural expert'],
        socialMedia: {
          instagram: '@yuki_tokyo_guide'
        },
        bankingInfo: {
          accountHolder: 'Yuki Tanaka',
          accountNumber: '****5678',
          bankName: 'Mizuho Bank',
          isVerified: true
        },
        documents: [
          {
            id: '3',
            type: 'license',
            url: '/documents/yuki-license.pdf',
            status: 'approved',
            uploadedAt: new Date('2021-06-10')
          }
        ]
      },
      {
        id: '3',
        name: 'Maria Rodriguez',
        email: '<EMAIL>',
        phone: '+34 6 12 34 56 78',
        profileImage: '/images/guides/maria.jpg',
        coverImage: '/images/guides/maria-cover.jpg',
        countryCode: 'ESP',
        city: 'Barcelona',
        region: 'Catalonia',
        languages: ['Spanish', 'Catalan', 'English', 'French'],
        specializations: ['Architecture Tours', 'Art History', 'Gastronomy', 'Modernism'],
        certifications: ['Official Tour Guide', 'Art History Degree'],
        experience: 6,
        rating: 4.7,
        reviewCount: 94,
        completedTours: 203,
        pricePerDay: 100,
        pricePerHour: 20,
        availability: 'available',
        isVerified: false,
        isActive: true,
        joinedAt: new Date('2021-09-20'),
        lastActive: new Date('2024-01-23'),
        bio: 'Art historian and licensed guide specializing in Barcelona\'s unique architecture and cultural heritage.',
        highlights: ['6+ years experience', '203+ tours completed', 'Architecture specialist', 'Art historian'],
        socialMedia: {
          instagram: '@maria_bcn_guide',
          website: 'www.barcelonawithmaria.com'
        },
        bankingInfo: {
          accountHolder: 'Maria Rodriguez',
          accountNumber: '****9012',
          bankName: 'CaixaBank',
          isVerified: false
        },
        documents: [
          {
            id: '4',
            type: 'license',
            url: '/documents/maria-license.pdf',
            status: 'pending',
            uploadedAt: new Date('2024-01-20')
          }
        ]
      }
    ]
    
    setGuides(mockGuides)
    setIsLoading(false)
  }, [])

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'available': return 'text-green-700 bg-green-100'
      case 'busy': return 'text-yellow-700 bg-yellow-100'
      case 'unavailable': return 'text-red-700 bg-red-100'
      default: return 'text-gray-700 bg-gray-100'
    }
  }

  const getVerificationStatus = (guide: Guide) => {
    if (guide.isVerified) return { text: 'Verified', color: 'text-green-700 bg-green-100' }
    const pendingDocs = guide.documents.filter(doc => doc.status === 'pending').length
    if (pendingDocs > 0) return { text: 'Pending', color: 'text-yellow-700 bg-yellow-100' }
    return { text: 'Unverified', color: 'text-red-700 bg-red-100' }
  }

  const filteredGuides = guides.filter(guide => {
    const matchesCountry = selectedCountry === 'all' || guide.countryCode === selectedCountry
    const matchesStatus = selectedStatus === 'all' || 
      (selectedStatus === 'verified' && guide.isVerified) ||
      (selectedStatus === 'unverified' && !guide.isVerified) ||
      (selectedStatus === 'active' && guide.isActive) ||
      (selectedStatus === 'inactive' && !guide.isActive)
    const matchesAvailability = selectedAvailability === 'all' || guide.availability === selectedAvailability
    const matchesSearch = searchQuery === '' || 
      guide.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      guide.city.toLowerCase().includes(searchQuery.toLowerCase()) ||
      guide.specializations.some(spec => spec.toLowerCase().includes(searchQuery.toLowerCase()))
    
    return matchesCountry && matchesStatus && matchesAvailability && matchesSearch
  })

  if (!hasPermission('canManageContent')) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold">Access Denied</h3>
          <p className="text-red-600">You don't have permission to manage guides.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Guide Management</h1>
          <p className="text-gray-600">Manage local guides and their profiles across all countries</p>
        </div>
        
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <TrendingUp className="w-4 h-4" />
            Analytics
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Plus className="w-4 h-4" />
            Add Guide
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Guides</p>
              <p className="text-2xl font-bold text-gray-900">{guides.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Verified</p>
              <p className="text-2xl font-bold text-green-600">
                {guides.filter(g => g.isVerified).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Shield className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Available</p>
              <p className="text-2xl font-bold text-blue-600">
                {guides.filter(g => g.availability === 'available').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Avg Rating</p>
              <p className="text-2xl font-bold text-yellow-600">
                {(guides.reduce((sum, g) => sum + g.rating, 0) / guides.length).toFixed(1)}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Tours</p>
              <p className="text-2xl font-bold text-purple-600">
                {guides.reduce((sum, g) => sum + g.completedTours, 0)}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <MapPin className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search guides..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <select
            value={selectedCountry}
            onChange={(e) => setSelectedCountry(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Countries</option>
            <option value="MAR">🇲🇦 Morocco</option>
            <option value="JPN">🇯🇵 Japan</option>
            <option value="ESP">🇪🇸 Spain</option>
            <option value="ITA">🇮🇹 Italy</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="verified">Verified</option>
            <option value="unverified">Unverified</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>

          <select
            value={selectedAvailability}
            onChange={(e) => setSelectedAvailability(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Availability</option>
            <option value="available">Available</option>
            <option value="busy">Busy</option>
            <option value="unavailable">Unavailable</option>
          </select>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <Users className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <Users className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Guides Grid/List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Local Guides ({filteredGuides.length})</h3>
        </div>
        
        {viewMode === 'grid' ? (
          <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredGuides.map((guide, index) => (
              <motion.div
                key={guide.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                {/* Cover Image */}
                <div className="h-32 bg-gradient-to-r from-blue-500 to-purple-600 relative">
                  <div className="absolute top-3 right-3 flex gap-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${getAvailabilityColor(guide.availability)}`}>
                      {guide.availability}
                    </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${getVerificationStatus(guide).color}`}>
                      {getVerificationStatus(guide).text}
                    </span>
                  </div>
                </div>

                {/* Profile */}
                <div className="p-6 -mt-8 relative">
                  <div className="w-16 h-16 bg-white rounded-full border-4 border-white shadow-lg mb-4">
                    <img
                      src={guide.profileImage}
                      alt={guide.name}
                      className="w-full h-full rounded-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(guide.name)}&background=random`
                      }}
                    />
                  </div>

                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{guide.name}</h3>
                    <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                      <MapPin className="w-3 h-3" />
                      <span>{guide.city}, {guide.countryCode}</span>
                    </div>
                    
                    <div className="flex items-center gap-2 mb-3">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        <span className="text-sm font-medium">{guide.rating}</span>
                        <span className="text-sm text-gray-500">({guide.reviewCount})</span>
                      </div>
                      <span className="text-sm text-gray-500">•</span>
                      <span className="text-sm text-gray-600">{guide.completedTours} tours</span>
                    </div>

                    <p className="text-sm text-gray-600 line-clamp-2 mb-3">{guide.bio}</p>

                    <div className="flex flex-wrap gap-1 mb-3">
                      {guide.specializations.slice(0, 2).map((spec, specIndex) => (
                        <span key={specIndex} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                          {spec}
                        </span>
                      ))}
                      {guide.specializations.length > 2 && (
                        <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          +{guide.specializations.length - 2} more
                        </span>
                      )}
                    </div>

                    <div className="flex items-center justify-between text-sm">
                      <span className="font-semibold text-gray-900">${guide.pricePerDay}/day</span>
                      <span className="text-gray-600">{guide.experience} years exp.</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <button className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors">
                      View Profile
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredGuides.map((guide, index) => (
              <motion.div
                key={guide.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                className="p-6 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-full overflow-hidden flex-shrink-0">
                    <img
                      src={guide.profileImage}
                      alt={guide.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(guide.name)}&background=random`
                      }}
                    />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{guide.name}</h3>
                      <span className={`px-2 py-1 text-xs rounded-full ${getAvailabilityColor(guide.availability)}`}>
                        {guide.availability}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${getVerificationStatus(guide).color}`}>
                        {getVerificationStatus(guide).text}
                      </span>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {guide.countryCode}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-6 text-sm text-gray-600 mb-2">
                      <span className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        {guide.city}
                      </span>
                      <span className="flex items-center gap-1">
                        <Star className="w-3 h-3" />
                        {guide.rating} ({guide.reviewCount} reviews)
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {guide.experience} years
                      </span>
                      <span className="flex items-center gap-1">
                        <DollarSign className="w-3 h-3" />
                        ${guide.pricePerDay}/day
                      </span>
                      <span>{guide.completedTours} tours</span>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mb-2">
                      {guide.specializations.map((spec, specIndex) => (
                        <span key={specIndex} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          {spec}
                        </span>
                      ))}
                    </div>
                    
                    <p className="text-sm text-gray-600 line-clamp-1">{guide.bio}</p>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                      <Edit className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
