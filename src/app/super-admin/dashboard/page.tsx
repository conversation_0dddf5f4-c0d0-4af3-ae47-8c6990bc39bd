'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Globe,
  Users,
  FileText,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  CheckCircle,
  Clock,
  AlertTriangle,
  MapPin,
  Star,
  Eye
} from 'lucide-react'
import { useSuperAdmin, withSuperAdminAuth } from '@/lib/auth/super-admin-auth'
import { useCountryContext } from '@/lib/country-context'

interface DashboardStats {
  totalCountries: number
  activeCountries: number
  totalUsers: number
  activeUsers: number
  totalContent: number
  pendingValidation: number
  monthlyGrowth: number
  revenueGrowth: number
}

interface CountryMetric {
  country: string
  flag: string
  users: number
  content: number
  revenue: number
  growth: number
}

function SuperAdminDashboard() {
  const { user, hasPermission } = useSuperAdmin()
  const { availableCountries } = useCountryContext()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [countryMetrics, setCountryMetrics] = useState<CountryMetric[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      // Mock data - replace with actual API calls
      const mockStats: DashboardStats = {
        totalCountries: availableCountries.length,
        activeCountries: availableCountries.filter(c => c.isActive).length,
        totalUsers: 12847,
        activeUsers: 8934,
        totalContent: 2456,
        pendingValidation: 23,
        monthlyGrowth: 12.5,
        revenueGrowth: 8.3
      }

      const mockCountryMetrics: CountryMetric[] = [
        { country: 'Morocco', flag: '🇲🇦', users: 5234, content: 1456, revenue: 45600, growth: 15.2 },
        { country: 'Japan', flag: '🇯🇵', users: 3421, content: 892, revenue: 32100, growth: 8.7 },
        { country: 'Italy', flag: '🇮🇹', users: 2156, content: 567, revenue: 21400, growth: 12.1 },
        { country: 'Spain', flag: '🇪🇸', users: 1876, content: 423, revenue: 18900, growth: 6.8 }
      ]

      setStats(mockStats)
      setCountryMetrics(mockCountryMetrics)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div>
        <h1 className="text-3xl font-bold text-slate-900">
          Welcome back, {user?.name}
        </h1>
        <p className="text-slate-600 mt-2">
          Here's what's happening across your international platform
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Total Countries</p>
              <p className="text-3xl font-bold text-slate-900">{stats?.totalCountries}</p>
              <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                <TrendingUp className="w-4 h-4" />
                {stats?.activeCountries} active
              </p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Globe className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Total Users</p>
              <p className="text-3xl font-bold text-slate-900">{stats?.totalUsers.toLocaleString()}</p>
              <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                <TrendingUp className="w-4 h-4" />
                +{stats?.monthlyGrowth}% this month
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Content Items</p>
              <p className="text-3xl font-bold text-slate-900">{stats?.totalContent.toLocaleString()}</p>
              <p className="text-sm text-orange-600 flex items-center gap-1 mt-1">
                <Clock className="w-4 h-4" />
                {stats?.pendingValidation} pending review
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600">Revenue Growth</p>
              <p className="text-3xl font-bold text-slate-900">+{stats?.revenueGrowth}%</p>
              <p className="text-sm text-green-600 flex items-center gap-1 mt-1">
                <TrendingUp className="w-4 h-4" />
                vs last month
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Country Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
        >
          <h3 className="text-lg font-semibold text-slate-900 mb-6">Country Performance</h3>
          <div className="space-y-4">
            {countryMetrics.map((metric, index) => (
              <div key={metric.country} className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{metric.flag}</span>
                  <div>
                    <p className="font-medium text-slate-900">{metric.country}</p>
                    <p className="text-sm text-slate-600">{metric.users.toLocaleString()} users</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium text-slate-900">${metric.revenue.toLocaleString()}</p>
                  <p className={`text-sm flex items-center gap-1 ${
                    metric.growth > 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {metric.growth > 0 ? <TrendingUp className="w-3 h-3" /> : <TrendingDown className="w-3 h-3" />}
                    {metric.growth > 0 ? '+' : ''}{metric.growth}%
                  </p>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
        >
          <h3 className="text-lg font-semibold text-slate-900 mb-6">Quick Actions</h3>
          <div className="space-y-3">
            {hasPermission('canManageCountries') && (
              <button className="w-full flex items-center gap-3 p-4 text-left bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors">
                <Globe className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="font-medium text-blue-900">Add New Country</p>
                  <p className="text-sm text-blue-700">Expand to new markets</p>
                </div>
              </button>
            )}
            
            {hasPermission('canValidateCulturalContent') && (
              <button className="w-full flex items-center gap-3 p-4 text-left bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors">
                <Eye className="w-5 h-5 text-orange-600" />
                <div>
                  <p className="font-medium text-orange-900">Review Pending Content</p>
                  <p className="text-sm text-orange-700">{stats?.pendingValidation} items waiting</p>
                </div>
              </button>
            )}
            
            {hasPermission('canManageUsers') && (
              <button className="w-full flex items-center gap-3 p-4 text-left bg-green-50 hover:bg-green-100 rounded-lg transition-colors">
                <Users className="w-5 h-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-900">Manage Admin Users</p>
                  <p className="text-sm text-green-700">Roles and permissions</p>
                </div>
              </button>
            )}
            
            {hasPermission('canViewAnalytics') && (
              <button className="w-full flex items-center gap-3 p-4 text-left bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors">
                <BarChart3 className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="font-medium text-purple-900">View Analytics</p>
                  <p className="text-sm text-purple-700">Detailed insights and reports</p>
                </div>
              </button>
            )}
          </div>
        </motion.div>
      </div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
      >
        <h3 className="text-lg font-semibold text-slate-900 mb-6">Recent Activity</h3>
        <div className="space-y-4">
          <div className="flex items-center gap-4 p-4 bg-slate-50 rounded-lg">
            <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="w-4 h-4 text-green-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-slate-900">Cultural content approved</p>
              <p className="text-xs text-slate-600">Morocco traditional cooking experience - 2 hours ago</p>
            </div>
          </div>
          
          <div className="flex items-center gap-4 p-4 bg-slate-50 rounded-lg">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <Globe className="w-4 h-4 text-blue-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-slate-900">New country configuration</p>
              <p className="text-xs text-slate-600">Spain country settings updated - 4 hours ago</p>
            </div>
          </div>
          
          <div className="flex items-center gap-4 p-4 bg-slate-50 rounded-lg">
            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-4 h-4 text-orange-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-slate-900">Content flagged for review</p>
              <p className="text-xs text-slate-600">Japan temple visit experience - 6 hours ago</p>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default withSuperAdminAuth(SuperAdminDashboard)
