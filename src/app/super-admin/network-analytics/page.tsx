'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  TrendingUp,
  Users,
  Star,
  MapPin,
  Calendar,
  DollarSign,
  Award,
  Globe,
  BarChart3,
  PieChart,
  Activity,
  Target,
  CheckCircle,
  AlertTriangle,
  ArrowUp,
  ArrowDown,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react'
import { useSuperAdmin } from '@/lib/auth/super-admin-auth'

interface NetworkMetrics {
  totalPartners: number
  activePartners: number
  verifiedPartners: number
  newPartnersThisMonth: number
  totalExperiences: number
  publishedExperiences: number
  totalBookings: number
  totalRevenue: number
  averageRating: number
  customerSatisfaction: number
  partnerRetentionRate: number
  growthRate: number
}

interface CountryMetrics {
  countryCode: string
  countryName: string
  flag: string
  partners: number
  experiences: number
  bookings: number
  revenue: number
  averageRating: number
  growthRate: number
  topCategories: string[]
}

interface PerformanceData {
  period: string
  bookings: number
  revenue: number
  newPartners: number
  customerRating: number
}

interface TopPerformer {
  id: string
  name: string
  type: 'guide' | 'provider'
  countryCode: string
  rating: number
  bookings: number
  revenue: number
  growthRate: number
}

export default function NetworkAnalyticsPage() {
  const { user, hasPermission } = useSuperAdmin()
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d' | '1y'>('30d')
  const [selectedCountry, setSelectedCountry] = useState<string>('all')
  const [metrics, setMetrics] = useState<NetworkMetrics | null>(null)
  const [countryMetrics, setCountryMetrics] = useState<CountryMetrics[]>([])
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([])
  const [topPerformers, setTopPerformers] = useState<TopPerformer[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Mock data
  useEffect(() => {
    const mockMetrics: NetworkMetrics = {
      totalPartners: 247,
      activePartners: 198,
      verifiedPartners: 156,
      newPartnersThisMonth: 23,
      totalExperiences: 342,
      publishedExperiences: 289,
      totalBookings: 1847,
      totalRevenue: 284750,
      averageRating: 4.7,
      customerSatisfaction: 92,
      partnerRetentionRate: 87,
      growthRate: 15.3
    }

    const mockCountryMetrics: CountryMetrics[] = [
      {
        countryCode: 'MAR',
        countryName: 'Morocco',
        flag: '🇲🇦',
        partners: 89,
        experiences: 127,
        bookings: 743,
        revenue: 118400,
        averageRating: 4.8,
        growthRate: 18.5,
        topCategories: ['Desert Adventures', 'Cultural Tours', 'Atlas Mountains']
      },
      {
        countryCode: 'JPN',
        countryName: 'Japan',
        flag: '🇯🇵',
        partners: 67,
        experiences: 94,
        bookings: 521,
        revenue: 89300,
        averageRating: 4.9,
        growthRate: 12.8,
        topCategories: ['Cultural Experiences', 'Traditional Arts', 'Temple Tours']
      },
      {
        countryCode: 'ESP',
        countryName: 'Spain',
        flag: '🇪🇸',
        partners: 54,
        experiences: 78,
        bookings: 398,
        revenue: 67200,
        averageRating: 4.6,
        growthRate: 14.2,
        topCategories: ['Food Tours', 'Architecture', 'Flamenco Culture']
      },
      {
        countryCode: 'ITA',
        countryName: 'Italy',
        flag: '🇮🇹',
        partners: 37,
        experiences: 43,
        bookings: 185,
        revenue: 29850,
        averageRating: 4.5,
        growthRate: 8.7,
        topCategories: ['Art & History', 'Culinary', 'Wine Tours']
      }
    ]

    const mockPerformanceData: PerformanceData[] = [
      { period: 'Week 1', bookings: 127, revenue: 18450, newPartners: 3, customerRating: 4.6 },
      { period: 'Week 2', bookings: 143, revenue: 21300, newPartners: 5, customerRating: 4.7 },
      { period: 'Week 3', bookings: 156, revenue: 23800, newPartners: 7, customerRating: 4.8 },
      { period: 'Week 4', bookings: 189, revenue: 28900, newPartners: 8, customerRating: 4.7 }
    ]

    const mockTopPerformers: TopPerformer[] = [
      {
        id: '1',
        name: 'Ahmed El Mansouri',
        type: 'guide',
        countryCode: 'MAR',
        rating: 4.9,
        bookings: 127,
        revenue: 22860,
        growthRate: 23.5
      },
      {
        id: '2',
        name: 'Atlas Adventures Morocco',
        type: 'provider',
        countryCode: 'MAR',
        rating: 4.8,
        bookings: 156,
        revenue: 28080,
        growthRate: 19.2
      },
      {
        id: '3',
        name: 'Yuki Tanaka',
        type: 'guide',
        countryCode: 'JPN',
        rating: 4.8,
        bookings: 89,
        revenue: 17800,
        growthRate: 16.8
      },
      {
        id: '4',
        name: 'Tokyo Cultural Experiences',
        type: 'provider',
        countryCode: 'JPN',
        rating: 4.9,
        bookings: 94,
        revenue: 18800,
        growthRate: 15.3
      },
      {
        id: '5',
        name: 'Maria Rodriguez',
        type: 'guide',
        countryCode: 'ESP',
        rating: 4.7,
        bookings: 67,
        revenue: 13400,
        growthRate: 12.1
      }
    ]
    
    setMetrics(mockMetrics)
    setCountryMetrics(mockCountryMetrics)
    setPerformanceData(mockPerformanceData)
    setTopPerformers(mockTopPerformers)
    setIsLoading(false)
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const getGrowthColor = (rate: number) => {
    if (rate > 0) return 'text-green-600'
    if (rate < 0) return 'text-red-600'
    return 'text-gray-600'
  }

  const getGrowthIcon = (rate: number) => {
    if (rate > 0) return <ArrowUp className="w-3 h-3" />
    if (rate < 0) return <ArrowDown className="w-3 h-3" />
    return null
  }

  if (!hasPermission('canManageContent')) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold">Access Denied</h3>
          <p className="text-red-600">You don't have permission to view analytics.</p>
        </div>
      </div>
    )
  }

  if (isLoading || !metrics) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Partnership Network Analytics</h1>
          <p className="text-gray-600">Monitor performance and growth across all partner networks</p>
        </div>
        
        <div className="flex items-center gap-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as any)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>
          
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Download className="w-4 h-4" />
            Export Report
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div className={`flex items-center gap-1 text-sm ${getGrowthColor(metrics.growthRate)}`}>
              {getGrowthIcon(metrics.growthRate)}
              <span>{metrics.growthRate}%</span>
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900">{metrics.totalPartners}</p>
            <p className="text-sm text-gray-600">Total Partners</p>
            <p className="text-xs text-gray-500 mt-1">{metrics.activePartners} active • {metrics.verifiedPartners} verified</p>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-green-600" />
            </div>
            <div className="flex items-center gap-1 text-sm text-green-600">
              <ArrowUp className="w-3 h-3" />
              <span>2.3%</span>
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900">{metrics.totalExperiences}</p>
            <p className="text-sm text-gray-600">Total Experiences</p>
            <p className="text-xs text-gray-500 mt-1">{metrics.publishedExperiences} published</p>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-6 h-6 text-purple-600" />
            </div>
            <div className="flex items-center gap-1 text-sm text-green-600">
              <ArrowUp className="w-3 h-3" />
              <span>12.8%</span>
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900">{metrics.totalBookings.toLocaleString()}</p>
            <p className="text-sm text-gray-600">Total Bookings</p>
            <p className="text-xs text-gray-500 mt-1">This period</p>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="flex items-center gap-1 text-sm text-green-600">
              <ArrowUp className="w-3 h-3" />
              <span>18.5%</span>
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.totalRevenue)}</p>
            <p className="text-sm text-gray-600">Total Revenue</p>
            <p className="text-xs text-gray-500 mt-1">This period</p>
          </div>
        </motion.div>
      </div>

      {/* Quality Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Award className="w-6 h-6 text-orange-600" />
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900">{metrics.averageRating}</p>
            <p className="text-sm text-gray-600">Average Rating</p>
            <div className="flex items-center gap-1 mt-1">
              {Array.from({ length: 5 }, (_, i) => (
                <Star
                  key={i}
                  className={`w-3 h-3 ${
                    i < Math.floor(metrics.averageRating) ? 'text-yellow-500 fill-current' : 'text-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-indigo-600" />
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900">{metrics.customerSatisfaction}%</p>
            <p className="text-sm text-gray-600">Customer Satisfaction</p>
            <p className="text-xs text-gray-500 mt-1">Based on reviews</p>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
              <Target className="w-6 h-6 text-teal-600" />
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900">{metrics.partnerRetentionRate}%</p>
            <p className="text-sm text-gray-600">Partner Retention</p>
            <p className="text-xs text-gray-500 mt-1">12-month rate</p>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-pink-600" />
            </div>
          </div>
          <div>
            <p className="text-2xl font-bold text-gray-900">{metrics.newPartnersThisMonth}</p>
            <p className="text-sm text-gray-600">New Partners</p>
            <p className="text-xs text-gray-500 mt-1">This month</p>
          </div>
        </motion.div>
      </div>

      {/* Country Performance */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Country Performance</h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {countryMetrics.map((country, index) => (
              <motion.div
                key={country.countryCode}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-50 rounded-lg p-6"
              >
                <div className="flex items-center gap-3 mb-4">
                  <span className="text-2xl">{country.flag}</span>
                  <div>
                    <h4 className="font-semibold text-gray-900">{country.countryName}</h4>
                    <div className={`flex items-center gap-1 text-sm ${getGrowthColor(country.growthRate)}`}>
                      {getGrowthIcon(country.growthRate)}
                      <span>{country.growthRate}% growth</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Partners</span>
                    <span className="font-medium">{country.partners}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Experiences</span>
                    <span className="font-medium">{country.experiences}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Bookings</span>
                    <span className="font-medium">{country.bookings}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Revenue</span>
                    <span className="font-medium">{formatCurrency(country.revenue)}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Rating</span>
                    <div className="flex items-center gap-1">
                      <Star className="w-3 h-3 text-yellow-500 fill-current" />
                      <span className="font-medium">{country.averageRating}</span>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <p className="text-xs text-gray-600 mb-2">Top Categories:</p>
                  <div className="flex flex-wrap gap-1">
                    {country.topCategories.map((category, catIndex) => (
                      <span key={catIndex} className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded">
                        {category}
                      </span>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Performers */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Top Performers</h3>
        </div>
        
        <div className="p-6">
          <div className="space-y-4">
            {topPerformers.map((performer, index) => (
              <motion.div
                key={performer.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-bold">#{index + 1}</span>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900">{performer.name}</h4>
                    <div className="flex items-center gap-3 text-sm text-gray-600">
                      <span className="capitalize">{performer.type}</span>
                      <span>•</span>
                      <span>{performer.countryCode}</span>
                      <span>•</span>
                      <div className="flex items-center gap-1">
                        <Star className="w-3 h-3 text-yellow-500 fill-current" />
                        <span>{performer.rating}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-6 text-sm">
                  <div className="text-center">
                    <div className="font-semibold text-gray-900">{performer.bookings}</div>
                    <div className="text-gray-600">Bookings</div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold text-gray-900">{formatCurrency(performer.revenue)}</div>
                    <div className="text-gray-600">Revenue</div>
                  </div>
                  <div className="text-center">
                    <div className={`font-semibold flex items-center gap-1 ${getGrowthColor(performer.growthRate)}`}>
                      {getGrowthIcon(performer.growthRate)}
                      <span>{performer.growthRate}%</span>
                    </div>
                    <div className="text-gray-600">Growth</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Trends */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Performance Trends</h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {performanceData.map((data, index) => (
              <motion.div
                key={data.period}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="text-center"
              >
                <h4 className="font-medium text-gray-900 mb-3">{data.period}</h4>
                <div className="space-y-2">
                  <div>
                    <div className="text-lg font-bold text-blue-600">{data.bookings}</div>
                    <div className="text-xs text-gray-600">Bookings</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-green-600">{formatCurrency(data.revenue)}</div>
                    <div className="text-xs text-gray-600">Revenue</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-purple-600">{data.newPartners}</div>
                    <div className="text-xs text-gray-600">New Partners</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-yellow-600">{data.customerRating}</div>
                    <div className="text-xs text-gray-600">Rating</div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
