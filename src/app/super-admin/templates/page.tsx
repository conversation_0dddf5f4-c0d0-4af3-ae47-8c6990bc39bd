'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Layers,
  Globe,
  Copy,
  Plus,
  Edit,
  Trash2,
  Eye,
  Download,
  Upload,
  CheckCircle,
  Clock,
  Star,
  MapPin,
  Users,
  Camera,
  Settings,
  Palette,
  Languages,
  Calendar
} from 'lucide-react'
import { useSuperAdmin } from '@/lib/auth/super-admin-auth'

interface CountryTemplate {
  id: string
  name: string
  description: string
  baseCountry: string
  category: 'cultural' | 'adventure' | 'beach' | 'urban' | 'nature' | 'mixed'
  regions: string[]
  features: {
    destinations: number
    experiences: number
    guides: number
    mediaItems: number
  }
  configuration: {
    currency: string
    languages: string[]
    timeZone: string
    theme: {
      primaryColor: string
      secondaryColor: string
      accentColor: string
    }
  }
  contentStructure: {
    destinationCategories: string[]
    experienceTypes: string[]
    guideSpecializations: string[]
    mediaTypes: string[]
  }
  isPublic: boolean
  usageCount: number
  createdBy: string
  createdAt: Date
  updatedAt: Date
}

export default function CountryTemplatesPage() {
  const { user, hasPermission } = useSuperAdmin()
  const [templates, setTemplates] = useState<CountryTemplate[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Mock data
  useEffect(() => {
    const mockTemplates: CountryTemplate[] = [
      {
        id: '1',
        name: 'Mediterranean Coastal Template',
        description: 'Perfect for Mediterranean countries with coastal attractions, historic cities, and cultural experiences',
        baseCountry: 'ITA',
        category: 'mixed',
        regions: ['Coastal', 'Historic Cities', 'Islands', 'Mountains'],
        features: {
          destinations: 15,
          experiences: 25,
          guides: 12,
          mediaItems: 80
        },
        configuration: {
          currency: 'EUR',
          languages: ['en', 'local'],
          timeZone: 'Europe/Rome',
          theme: {
            primaryColor: '#2563eb',
            secondaryColor: '#0ea5e9',
            accentColor: '#f59e0b'
          }
        },
        contentStructure: {
          destinationCategories: ['Historic Cities', 'Coastal Towns', 'Islands', 'Mountains', 'Archaeological Sites'],
          experienceTypes: ['Cultural Tours', 'Food & Wine', 'Beach Activities', 'Hiking', 'Art & Museums'],
          guideSpecializations: ['History', 'Culinary', 'Art', 'Nature', 'Photography'],
          mediaTypes: ['Photos', 'Videos', '360° Tours', 'Audio Guides']
        },
        isPublic: true,
        usageCount: 8,
        createdBy: 'template-admin',
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: '2',
        name: 'Desert & Mountains Adventure',
        description: 'Ideal for countries with desert landscapes, mountain ranges, and adventure tourism',
        baseCountry: 'MAR',
        category: 'adventure',
        regions: ['Desert', 'Mountains', 'Oases', 'Traditional Villages'],
        features: {
          destinations: 12,
          experiences: 20,
          guides: 15,
          mediaItems: 60
        },
        configuration: {
          currency: 'MAD',
          languages: ['en', 'ar', 'fr'],
          timeZone: 'Africa/Casablanca',
          theme: {
            primaryColor: '#dc2626',
            secondaryColor: '#ea580c',
            accentColor: '#fbbf24'
          }
        },
        contentStructure: {
          destinationCategories: ['Desert', 'Mountains', 'Oases', 'Traditional Villages', 'Kasbahs'],
          experienceTypes: ['Desert Camping', 'Trekking', 'Camel Rides', 'Cultural Immersion', 'Photography'],
          guideSpecializations: ['Desert Navigation', 'Mountain Trekking', 'Cultural Heritage', 'Photography'],
          mediaTypes: ['Landscape Photos', 'Adventure Videos', 'Cultural Documentation']
        },
        isPublic: true,
        usageCount: 5,
        createdBy: 'template-admin',
        createdAt: new Date('2024-01-12'),
        updatedAt: new Date('2024-01-18')
      },
      {
        id: '3',
        name: 'Asian Cultural Heritage',
        description: 'Designed for Asian countries with rich cultural heritage, temples, and traditional experiences',
        baseCountry: 'JPN',
        category: 'cultural',
        regions: ['Historic Districts', 'Temples', 'Traditional Villages', 'Gardens'],
        features: {
          destinations: 18,
          experiences: 30,
          guides: 20,
          mediaItems: 100
        },
        configuration: {
          currency: 'JPY',
          languages: ['en', 'ja'],
          timeZone: 'Asia/Tokyo',
          theme: {
            primaryColor: '#7c3aed',
            secondaryColor: '#a855f7',
            accentColor: '#ec4899'
          }
        },
        contentStructure: {
          destinationCategories: ['Temples', 'Historic Districts', 'Gardens', 'Traditional Villages', 'Museums'],
          experienceTypes: ['Temple Visits', 'Tea Ceremonies', 'Traditional Crafts', 'Festivals', 'Meditation'],
          guideSpecializations: ['Religious Heritage', 'Traditional Arts', 'History', 'Philosophy', 'Language'],
          mediaTypes: ['Cultural Photos', 'Ceremony Videos', 'Audio Guides', '360° Temple Tours']
        },
        isPublic: true,
        usageCount: 12,
        createdBy: 'template-admin',
        createdAt: new Date('2024-01-08'),
        updatedAt: new Date('2024-01-20')
      }
    ]
    
    setTemplates(mockTemplates)
    setIsLoading(false)
  }, [])

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'cultural': return 'bg-purple-100 text-purple-700'
      case 'adventure': return 'bg-red-100 text-red-700'
      case 'beach': return 'bg-blue-100 text-blue-700'
      case 'urban': return 'bg-gray-100 text-gray-700'
      case 'nature': return 'bg-green-100 text-green-700'
      case 'mixed': return 'bg-orange-100 text-orange-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const filteredTemplates = templates.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory
    const matchesSearch = searchQuery === '' || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.regions.some(region => region.toLowerCase().includes(searchQuery.toLowerCase()))
    
    return matchesCategory && matchesSearch
  })

  if (!hasPermission('canManageContent')) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold">Access Denied</h3>
          <p className="text-red-600">You don't have permission to manage templates.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Country Templates</h1>
          <p className="text-gray-600">Pre-built templates for rapid country onboarding</p>
        </div>
        
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
            <Upload className="w-4 h-4" />
            Import Template
          </button>
          <button 
            onClick={() => setShowCreateModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Create Template
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Templates</p>
              <p className="text-2xl font-bold text-gray-900">{templates.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Layers className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Public Templates</p>
              <p className="text-2xl font-bold text-green-600">
                {templates.filter(t => t.isPublic).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <Globe className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Usage</p>
              <p className="text-2xl font-bold text-purple-600">
                {templates.reduce((sum, t) => sum + t.usageCount, 0)}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Copy className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Categories</p>
              <p className="text-2xl font-bold text-orange-600">
                {new Set(templates.map(t => t.category)).size}
              </p>
            </div>
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center gap-4">
          <div className="relative flex-1">
            <input
              type="text"
              placeholder="Search templates..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-4 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Categories</option>
            <option value="cultural">Cultural</option>
            <option value="adventure">Adventure</option>
            <option value="beach">Beach</option>
            <option value="urban">Urban</option>
            <option value="nature">Nature</option>
            <option value="mixed">Mixed</option>
          </select>
        </div>
      </div>

      {/* Templates Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {filteredTemplates.map((template, index) => (
          <motion.div
            key={template.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow"
          >
            {/* Template Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{template.name}</h3>
                  <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                  
                  <div className="flex items-center gap-2 mb-3">
                    <span className={`px-2 py-1 text-xs rounded-full ${getCategoryColor(template.category)}`}>
                      {template.category}
                    </span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                      Based on {template.baseCountry}
                    </span>
                    {template.isPublic && (
                      <span className="text-xs text-green-700 bg-green-100 px-2 py-1 rounded">
                        Public
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Theme Preview */}
              <div className="flex items-center gap-2 mb-3">
                <span className="text-xs text-gray-600">Theme:</span>
                <div className="flex gap-1">
                  <div 
                    className="w-4 h-4 rounded-full border border-gray-200"
                    style={{ backgroundColor: template.configuration.theme.primaryColor }}
                  />
                  <div 
                    className="w-4 h-4 rounded-full border border-gray-200"
                    style={{ backgroundColor: template.configuration.theme.secondaryColor }}
                  />
                  <div 
                    className="w-4 h-4 rounded-full border border-gray-200"
                    style={{ backgroundColor: template.configuration.theme.accentColor }}
                  />
                </div>
              </div>

              {/* Features */}
              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <MapPin className="w-3 h-3" />
                  <span>{template.features.destinations} destinations</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Star className="w-3 h-3" />
                  <span>{template.features.experiences} experiences</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Users className="w-3 h-3" />
                  <span>{template.features.guides} guides</span>
                </div>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Camera className="w-3 h-3" />
                  <span>{template.features.mediaItems} media</span>
                </div>
              </div>

              {/* Regions */}
              <div className="mb-4">
                <span className="text-xs text-gray-600 mb-2 block">Regions:</span>
                <div className="flex flex-wrap gap-1">
                  {template.regions.map((region, regionIndex) => (
                    <span key={regionIndex} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                      {region}
                    </span>
                  ))}
                </div>
              </div>

              {/* Usage Stats */}
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>Used {template.usageCount} times</span>
                <span>{template.updatedAt.toLocaleDateString()}</span>
              </div>
            </div>

            {/* Actions */}
            <div className="p-4 bg-gray-50 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                  <Eye className="w-4 h-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                  <Edit className="w-4 h-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-purple-600 transition-colors">
                  <Download className="w-4 h-4" />
                </button>
                <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
              
              <button className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors">
                Use Template
              </button>
            </div>
          </motion.div>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <Layers className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No templates found</h3>
          <p className="text-gray-600 mb-4">Try adjusting your search or create a new template</p>
          <button 
            onClick={() => setShowCreateModal(true)}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Create Template
          </button>
        </div>
      )}
    </div>
  )
}
