'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  FileText,
  Image,
  Video,
  Globe,
  Upload,
  Download,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Users,
  MapPin,
  Star,
  Languages,
  Camera
} from 'lucide-react'
import { useSuperAdmin } from '@/lib/auth/super-admin-auth'

interface ContentItem {
  id: string
  title: string
  type: 'destination' | 'experience' | 'guide' | 'media' | 'article'
  countryCode: string
  status: 'draft' | 'pending_review' | 'cultural_review' | 'approved' | 'rejected'
  language: string
  createdAt: Date
  updatedAt: Date
  createdBy: string
  reviewedBy?: string
  culturalValidation?: {
    status: 'pending' | 'approved' | 'rejected'
    reviewer: string
    score: number
    feedback: string
  }
  mediaCount: number
  tags: string[]
}

export default function ContentManagementPage() {
  const { user, hasPermission } = useSuperAdmin()
  const [contentItems, setContentItems] = useState<ContentItem[]>([])
  const [selectedCountry, setSelectedCountry] = useState<string>('all')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)

  // Mock data for demonstration
  useEffect(() => {
    const mockContent: ContentItem[] = [
      {
        id: '1',
        title: 'Marrakech Medina Walking Tour',
        type: 'experience',
        countryCode: 'MAR',
        status: 'approved',
        language: 'en',
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-20'),
        createdBy: 'content-manager-1',
        reviewedBy: 'cultural-expert-mar',
        culturalValidation: {
          status: 'approved',
          reviewer: 'Dr. Amina Hassan',
          score: 9.2,
          feedback: 'Excellent cultural accuracy and sensitivity'
        },
        mediaCount: 12,
        tags: ['cultural', 'walking', 'medina', 'traditional']
      },
      {
        id: '2',
        title: 'Atlas Mountains Hiking Guide',
        type: 'destination',
        countryCode: 'MAR',
        status: 'cultural_review',
        language: 'en',
        createdAt: new Date('2024-01-18'),
        updatedAt: new Date('2024-01-22'),
        createdBy: 'content-manager-2',
        culturalValidation: {
          status: 'pending',
          reviewer: 'Local Expert Network',
          score: 0,
          feedback: 'Awaiting review from Berber community representatives'
        },
        mediaCount: 8,
        tags: ['hiking', 'mountains', 'berber', 'adventure']
      },
      {
        id: '3',
        title: 'Traditional Moroccan Cooking Class',
        type: 'experience',
        countryCode: 'MAR',
        status: 'pending_review',
        language: 'fr',
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2024-01-20'),
        createdBy: 'content-manager-1',
        mediaCount: 15,
        tags: ['cooking', 'traditional', 'culinary', 'hands-on']
      },
      {
        id: '4',
        title: 'Tokyo Temple District Guide',
        type: 'destination',
        countryCode: 'JPN',
        status: 'approved',
        language: 'en',
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-25'),
        createdBy: 'content-manager-3',
        reviewedBy: 'cultural-expert-jpn',
        culturalValidation: {
          status: 'approved',
          reviewer: 'Tanaka Hiroshi',
          score: 9.5,
          feedback: 'Perfect respect for temple etiquette and traditions'
        },
        mediaCount: 20,
        tags: ['temples', 'spiritual', 'traditional', 'tokyo']
      }
    ]
    
    setContentItems(mockContent)
    setIsLoading(false)
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-700 bg-green-100'
      case 'pending_review': return 'text-yellow-700 bg-yellow-100'
      case 'cultural_review': return 'text-blue-700 bg-blue-100'
      case 'rejected': return 'text-red-700 bg-red-100'
      default: return 'text-gray-700 bg-gray-100'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'destination': return <MapPin className="w-4 h-4" />
      case 'experience': return <Star className="w-4 h-4" />
      case 'guide': return <Users className="w-4 h-4" />
      case 'media': return <Camera className="w-4 h-4" />
      case 'article': return <FileText className="w-4 h-4" />
      default: return <FileText className="w-4 h-4" />
    }
  }

  const filteredContent = contentItems.filter(item => {
    const matchesCountry = selectedCountry === 'all' || item.countryCode === selectedCountry
    const matchesType = selectedType === 'all' || item.type === selectedType
    const matchesStatus = selectedStatus === 'all' || item.status === selectedStatus
    const matchesSearch = searchQuery === '' || 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    
    return matchesCountry && matchesType && matchesStatus && matchesSearch
  })

  if (!hasPermission('canManageContent')) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-semibold">Access Denied</h3>
          <p className="text-red-600">You don't have permission to manage content.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Content Management</h1>
          <p className="text-gray-600">Manage destinations, experiences, and cultural content across all countries</p>
        </div>
        
        <div className="flex items-center gap-3">
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Upload className="w-4 h-4" />
            Bulk Import
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Plus className="w-4 h-4" />
            Add Content
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Content</p>
              <p className="text-2xl font-bold text-gray-900">{contentItems.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <FileText className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pending Review</p>
              <p className="text-2xl font-bold text-yellow-600">
                {contentItems.filter(item => item.status === 'pending_review' || item.status === 'cultural_review').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Approved</p>
              <p className="text-2xl font-bold text-green-600">
                {contentItems.filter(item => item.status === 'approved').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Countries</p>
              <p className="text-2xl font-bold text-purple-600">
                {new Set(contentItems.map(item => item.countryCode)).size}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Globe className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <select
            value={selectedCountry}
            onChange={(e) => setSelectedCountry(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Countries</option>
            <option value="MAR">🇲🇦 Morocco</option>
            <option value="JPN">🇯🇵 Japan</option>
            <option value="ITA">🇮🇹 Italy</option>
            <option value="ESP">🇪🇸 Spain</option>
          </select>

          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Types</option>
            <option value="destination">Destinations</option>
            <option value="experience">Experiences</option>
            <option value="guide">Guides</option>
            <option value="media">Media</option>
            <option value="article">Articles</option>
          </select>

          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Status</option>
            <option value="draft">Draft</option>
            <option value="pending_review">Pending Review</option>
            <option value="cultural_review">Cultural Review</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
        </div>
      </div>

      {/* Content List */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Content Items ({filteredContent.length})</h3>
        </div>
        
        <div className="divide-y divide-gray-200">
          {filteredContent.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              className="p-6 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                    {getTypeIcon(item.type)}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-semibold text-gray-900">{item.title}</h4>
                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(item.status)}`}>
                        {item.status.replace('_', ' ')}
                      </span>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {item.countryCode}
                      </span>
                      <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {item.language}
                      </span>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span className="flex items-center gap-1">
                        <Camera className="w-3 h-3" />
                        {item.mediaCount} media
                      </span>
                      <span>Created {item.createdAt.toLocaleDateString()}</span>
                      <span>By {item.createdBy}</span>
                    </div>
                    
                    {item.culturalValidation && (
                      <div className="mt-2 p-2 bg-blue-50 rounded text-sm">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">Cultural Review:</span>
                          <span className={`px-2 py-1 text-xs rounded ${
                            item.culturalValidation.status === 'approved' ? 'bg-green-100 text-green-700' :
                            item.culturalValidation.status === 'rejected' ? 'bg-red-100 text-red-700' :
                            'bg-yellow-100 text-yellow-700'
                          }`}>
                            {item.culturalValidation.status}
                          </span>
                          {item.culturalValidation.score > 0 && (
                            <span className="text-blue-600 font-medium">
                              Score: {item.culturalValidation.score}/10
                            </span>
                          )}
                        </div>
                        <p className="text-gray-600 mt-1">{item.culturalValidation.feedback}</p>
                      </div>
                    )}
                    
                    <div className="flex flex-wrap gap-1 mt-2">
                      {item.tags.map((tag, tagIndex) => (
                        <span key={tagIndex} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                    <Eye className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                    <Edit className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}
