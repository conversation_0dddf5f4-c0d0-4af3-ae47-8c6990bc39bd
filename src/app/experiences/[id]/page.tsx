'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { 
  MapPin,
  Clock,
  Users,
  Star,
  Heart,
  Share2,
  Calendar,
  ArrowLeft,
  CheckCircle,
  Info,
  Camera,
  ChevronLeft,
  ChevronRight,
  Play,
  MessageCircle,
  Shield,
  Award,
  X
} from 'lucide-react'

interface ExperienceDetailProps {
  params: { id: string }
}

export default function ExperienceDetailPage({ params }: ExperienceDetailProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [showBookingFlow, setShowBookingFlow] = useState(false)
  const [activeTab, setActiveTab] = useState<'overview' | 'itinerary' | 'reviews'>('overview')

  // Mock experience data - in real app, fetch based on params.id
  const experience = {
    id: 'medina-storytelling',
    title: 'Medina Storytelling Walk',
    description: 'Journey through 1000 years of history with captivating stories that bring the ancient walls of Marrakech to life. Walk through hidden passages, discover secret courtyards, and hear tales passed down through generations.',
    location: 'Marrakech Medina',
    duration: '3 hours',
    price: 45,
    rating: 4.9,
    reviewCount: 127,
    category: 'Cultural',
    difficulty: 'Easy',
    groupSize: '2-8 people',
    languages: ['English', 'French', 'Arabic'],
    images: [
      'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop'
    ],
    highlights: [
      'Hidden courtyards and secret passages',
      'Stories passed down through generations',
      'Traditional mint tea ceremony',
      'Small group intimate experience',
      'Local legends and folklore',
      'Historical insights from expert guide'
    ],
    includes: [
      'Expert storyteller guide',
      'Traditional mint tea',
      'Access to hidden areas',
      'Historical insights',
      'Small group experience',
      'Photo opportunities'
    ],
    notIncluded: [
      'Hotel pickup/drop-off',
      'Meals (except tea)',
      'Personal expenses',
      'Tips for guide'
    ],
    agent: {
      name: 'Youssef Alami',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      rating: 4.9,
      experience: '8 years',
      specializations: ['Cultural Tours', 'Historical Sites', 'Storytelling'],
      bio: 'Born and raised in Marrakech, Youssef has been sharing the magic of Morocco with travelers for over 8 years.'
    },
    itinerary: [
      {
        time: '9:00 AM',
        title: 'Meeting Point',
        description: 'Meet at Jemaa el-Fnaa square near the main fountain'
      },
      {
        time: '9:15 AM',
        title: 'Enter the Medina',
        description: 'Begin our journey through the ancient gates with historical context'
      },
      {
        time: '10:00 AM',
        title: 'Hidden Courtyards',
        description: 'Discover secret passages and hear stories of ancient merchants'
      },
      {
        time: '10:45 AM',
        title: 'Traditional Tea Break',
        description: 'Enjoy mint tea while learning about Berber traditions'
      },
      {
        time: '11:30 AM',
        title: 'Artisan Quarter',
        description: 'Visit workshops and meet local craftsmen'
      },
      {
        time: '12:00 PM',
        title: 'Experience Ends',
        description: 'Return to starting point with new perspectives on Marrakech'
      }
    ],
    reviews: [
      {
        id: '1',
        author: 'Sarah Johnson',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100&h=100&fit=crop&crop=face',
        rating: 5,
        date: '2024-02-15',
        title: 'Absolutely magical experience!',
        content: 'Youssef brought the medina to life with his incredible storytelling. We discovered places we never would have found on our own and learned so much about Moroccan culture and history.'
      },
      {
        id: '2',
        author: 'Michael Chen',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
        rating: 5,
        date: '2024-02-10',
        title: 'Best tour in Marrakech',
        content: 'This was the highlight of our Morocco trip. The stories were captivating and Youssef\'s knowledge is incredible. Highly recommend for anyone wanting to understand the real Marrakech.'
      },
      {
        id: '3',
        author: 'Emma Rodriguez',
        avatar: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=100&h=100&fit=crop&crop=face',
        rating: 5,
        date: '2024-02-05',
        title: 'Unforgettable cultural immersion',
        content: 'Perfect blend of history, culture, and personal stories. Youssef made us feel like we were part of the medina\'s living history. The tea ceremony was a beautiful touch.'
      }
    ],
    cancellationPolicy: 'Free cancellation up to 48 hours before the experience starts. Cancellations within 48 hours are subject to a 50% fee.',
    meetingPoint: 'Jemaa el-Fnaa square, near the main fountain. Look for your guide holding a ComeToMorocco sign.',
    whatToBring: ['Comfortable walking shoes', 'Sun hat', 'Camera', 'Water bottle', 'Modest clothing'],
    accessibility: 'This experience involves walking on uneven surfaces and narrow passages. Not suitable for wheelchairs.',
    weatherPolicy: 'Experience runs in all weather conditions. In case of extreme weather, we will contact you to reschedule.'
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % experience.images.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + experience.images.length) % experience.images.length)
  }

  const handleBookingComplete = (booking: any) => {
    console.log('Booking completed:', booking)
    setShowBookingFlow(false)
    // Handle successful booking
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Breadcrumb */}
      <section className="pt-24 pb-4 bg-neutral-50">
        <div className="container-custom">
          <div className="flex items-center gap-2 text-sm text-neutral-600">
            <Link href="/" className="hover:text-primary-600">Home</Link>
            <span>/</span>
            <Link href="/experiences" className="hover:text-primary-600">Experiences</Link>
            <span>/</span>
            <span className="text-neutral-900">{experience.title}</span>
          </div>
        </div>
      </section>

      {/* Hero Section */}
      <section className="pb-8 bg-neutral-50">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Image Gallery */}
            <div className="space-y-4">
              <div className="relative aspect-[4/3] rounded-2xl overflow-hidden">
                <img
                  src={experience.images[currentImageIndex]}
                  alt={experience.title}
                  className="w-full h-full object-cover"
                />
                
                {experience.images.length > 1 && (
                  <>
                    <button
                      onClick={prevImage}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white"
                    >
                      <ChevronLeft className="w-5 h-5" />
                    </button>
                    <button
                      onClick={nextImage}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white"
                    >
                      <ChevronRight className="w-5 h-5" />
                    </button>
                    
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                      {experience.images.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentImageIndex(index)}
                          className={`w-2 h-2 rounded-full ${
                            index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                          }`}
                        />
                      ))}
                    </div>
                  </>
                )}
              </div>
              
              {/* Thumbnail Gallery */}
              {experience.images.length > 1 && (
                <div className="grid grid-cols-4 gap-2">
                  {experience.images.slice(0, 4).map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`aspect-square rounded-lg overflow-hidden ${
                        index === currentImageIndex ? 'ring-2 ring-primary-500' : ''
                      }`}
                    >
                      <img
                        src={image}
                        alt={`${experience.title} ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              )}
            </div>

            {/* Experience Info */}
            <div className="space-y-6">
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <span className="bg-primary-100 text-primary-700 px-3 py-1 rounded-full text-sm font-medium">
                    {experience.category}
                  </span>
                  <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium">
                    {experience.difficulty}
                  </span>
                </div>
                
                <h1 className="heading-xl text-neutral-900 mb-4">{experience.title}</h1>
                
                <div className="flex items-center gap-6 mb-4">
                  <div className="flex items-center gap-1">
                    <Star className="w-5 h-5 text-yellow-500 fill-current" />
                    <span className="font-semibold text-neutral-900">{experience.rating}</span>
                    <span className="text-neutral-500">({experience.reviewCount} reviews)</span>
                  </div>
                  
                  <div className="flex items-center gap-1 text-neutral-600">
                    <MapPin className="w-4 h-4" />
                    <span>{experience.location}</span>
                  </div>
                </div>
                
                <p className="text-body text-neutral-600 mb-6">{experience.description}</p>
              </div>

              {/* Quick Info */}
              <div className="grid grid-cols-2 gap-4">
                <div className="flex items-center gap-3 p-4 bg-white rounded-lg border border-neutral-200">
                  <Clock className="w-5 h-5 text-primary-500" />
                  <div>
                    <p className="font-medium text-neutral-900">Duration</p>
                    <p className="text-sm text-neutral-600">{experience.duration}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3 p-4 bg-white rounded-lg border border-neutral-200">
                  <Users className="w-5 h-5 text-primary-500" />
                  <div>
                    <p className="font-medium text-neutral-900">Group Size</p>
                    <p className="text-sm text-neutral-600">{experience.groupSize}</p>
                  </div>
                </div>
              </div>

              {/* Agent Info */}
              <div className="p-4 bg-white rounded-lg border border-neutral-200">
                <h3 className="font-semibold text-neutral-900 mb-3">Your Guide</h3>
                <div className="flex items-center gap-4">
                  <img
                    src={experience.agent.avatar}
                    alt={experience.agent.name}
                    className="w-12 h-12 rounded-full"
                  />
                  <div className="flex-1">
                    <h4 className="font-medium text-neutral-900">{experience.agent.name}</h4>
                    <div className="flex items-center gap-2 text-sm text-neutral-600">
                      <Star className="w-3 h-3 text-yellow-500 fill-current" />
                      <span>{experience.agent.rating}</span>
                      <span>•</span>
                      <span>{experience.agent.experience} experience</span>
                    </div>
                  </div>
                  <Link href={`/agents/${experience.agent.name.toLowerCase().replace(' ', '-')}`} className="btn-ghost btn-sm">
                    View Profile
                  </Link>
                </div>
              </div>

              {/* Booking Section */}
              <div className="p-6 bg-white rounded-2xl border border-neutral-200 shadow-sm">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <div className="text-3xl font-bold text-primary-600">€{experience.price}</div>
                    <div className="text-sm text-neutral-500">per person</div>
                  </div>
                  <div className="flex gap-2">
                    <button className="btn-ghost p-3">
                      <Heart className="w-5 h-5" />
                    </button>
                    <button className="btn-ghost p-3">
                      <Share2 className="w-5 h-5" />
                    </button>
                  </div>
                </div>
                
                <button 
                  onClick={() => setShowBookingFlow(true)}
                  className="btn-primary w-full mb-4"
                >
                  <Calendar className="w-4 h-4 mr-2" />
                  Book This Experience
                </button>
                
                <div className="flex items-center gap-2 text-sm text-neutral-600">
                  <Shield className="w-4 h-4 text-green-500" />
                  <span>Free cancellation up to 48 hours</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Tabs Section */}
      <section className="section-padding">
        <div className="container-custom">
          {/* Tab Navigation */}
          <div className="border-b border-neutral-200 mb-8">
            <div className="flex gap-8">
              {[
                { id: 'overview', label: 'Overview' },
                { id: 'itinerary', label: 'Itinerary' },
                { id: 'reviews', label: 'Reviews' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`pb-4 border-b-2 transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-neutral-600 hover:text-neutral-900'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="grid lg:grid-cols-3 gap-12">
            <div className="lg:col-span-2">
              {activeTab === 'overview' && (
                <div className="space-y-8">
                  {/* Highlights */}
                  <div>
                    <h3 className="heading-md text-neutral-900 mb-4">Experience Highlights</h3>
                    <div className="grid md:grid-cols-2 gap-3">
                      {experience.highlights.map((highlight, index) => (
                        <div key={index} className="flex items-start gap-3">
                          <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-neutral-700">{highlight}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* What's Included */}
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="heading-md text-neutral-900 mb-4">What's Included</h3>
                      <div className="space-y-2">
                        {experience.includes.map((item, index) => (
                          <div key={index} className="flex items-start gap-2">
                            <CheckCircle className="w-4 h-4 text-green-500 mt-1 flex-shrink-0" />
                            <span className="text-neutral-700 text-sm">{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h3 className="heading-md text-neutral-900 mb-4">Not Included</h3>
                      <div className="space-y-2">
                        {experience.notIncluded.map((item, index) => (
                          <div key={index} className="flex items-start gap-2">
                            <X className="w-4 h-4 text-red-500 mt-1 flex-shrink-0" />
                            <span className="text-neutral-700 text-sm">{item}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'itinerary' && (
                <div className="space-y-6">
                  <h3 className="heading-md text-neutral-900 mb-6">Detailed Itinerary</h3>
                  <div className="space-y-6">
                    {experience.itinerary.map((item, index) => (
                      <div key={index} className="flex gap-4">
                        <div className="w-20 text-sm font-medium text-primary-600 flex-shrink-0">
                          {item.time}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-neutral-900 mb-2">{item.title}</h4>
                          <p className="text-neutral-600">{item.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'reviews' && (
                <div className="space-y-8">
                  <div className="flex items-center justify-between">
                    <h3 className="heading-md text-neutral-900">Reviews ({experience.reviewCount})</h3>
                    <div className="flex items-center gap-2">
                      <Star className="w-5 h-5 text-yellow-500 fill-current" />
                      <span className="font-semibold text-neutral-900">{experience.rating}</span>
                      <span className="text-neutral-500">average</span>
                    </div>
                  </div>

                  <div className="space-y-6">
                    {experience.reviews.map((review) => (
                      <div key={review.id} className="p-6 bg-neutral-50 rounded-lg">
                        <div className="flex items-start gap-4">
                          <img
                            src={review.avatar}
                            alt={review.author}
                            className="w-12 h-12 rounded-full"
                          />
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold text-neutral-900">{review.author}</h4>
                              <span className="text-sm text-neutral-500">
                                {new Date(review.date).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="flex items-center gap-1 mb-3">
                              {[...Array(review.rating)].map((_, i) => (
                                <Star key={i} className="w-4 h-4 text-yellow-500 fill-current" />
                              ))}
                            </div>
                            <h5 className="font-medium text-neutral-900 mb-2">{review.title}</h5>
                            <p className="text-neutral-700">{review.content}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-24 space-y-6">
                {/* Important Info */}
                <div className="card">
                  <h3 className="font-semibold text-neutral-900 mb-4">Important Information</h3>
                  <div className="space-y-4 text-sm">
                    <div>
                      <h4 className="font-medium text-neutral-900 mb-2">Meeting Point</h4>
                      <p className="text-neutral-600">{experience.meetingPoint}</p>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-neutral-900 mb-2">What to Bring</h4>
                      <ul className="space-y-1 text-neutral-600">
                        {experience.whatToBring.map((item, index) => (
                          <li key={index}>• {item}</li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-medium text-neutral-900 mb-2">Accessibility</h4>
                      <p className="text-neutral-600">{experience.accessibility}</p>
                    </div>
                  </div>
                </div>

                {/* Contact Agent */}
                <div className="card">
                  <h3 className="font-semibold text-neutral-900 mb-4">Questions?</h3>
                  <p className="text-neutral-600 text-sm mb-4">
                    Get in touch with {experience.agent.name} for personalized advice.
                  </p>
                  <button className="btn-secondary w-full">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Message Guide
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Booking Flow Modal */}
      {showBookingFlow && (
        <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl p-8 max-w-md w-full">
            <h3 className="heading-md text-neutral-900 mb-4">Booking Coming Soon</h3>
            <p className="text-neutral-600 mb-6">
              Our booking system is being finalized. Contact the agent directly for now.
            </p>
            <button 
              onClick={() => setShowBookingFlow(false)}
              className="btn-primary w-full"
            >
              Close
            </button>
          </div>
        </div>
      )}

      <Footer />
    </div>
  )
}