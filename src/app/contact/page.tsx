'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Head<PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  Send,
  MessageCircle,
  User,
  Calendar,
  CheckCircle
} from 'lucide-react'

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    travelDates: '',
    travelers: ''
  })
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    setIsSubmitted(true)
  }

  const contactInfo = [
    {
      icon: Mail,
      title: 'Email Us',
      details: '<EMAIL>',
      description: 'Get a response within 2 hours',
      action: 'mailto:<EMAIL>'
    },
    {
      icon: Phone,
      title: 'Call Us',
      details: '+212 522 000 000',
      description: 'Mon-Fri 9AM-6PM (GMT+1)',
      action: 'tel:+212522000000'
    },
    {
      icon: MessageCircle,
      title: 'Live Chat',
      details: 'Chat with our team',
      description: 'Available 24/7 for urgent inquiries',
      action: '#'
    },
    {
      icon: MapPin,
      title: 'Visit Us',
      details: 'Marrakech, Morocco',
      description: 'Schedule an in-person meeting',
      action: '#'
    }
  ]

  const offices = [
    {
      city: 'Marrakech',
      address: '123 Medina Quarter, Marrakech 40000, Morocco',
      phone: '+212 524 000 001',
      hours: 'Mon-Fri: 9AM-6PM, Sat: 10AM-4PM'
    },
    {
      city: 'Fes',
      address: '456 Imperial City, Fes 30000, Morocco',
      phone: '+212 535 000 002',
      hours: 'Mon-Fri: 9AM-6PM, Sat: 10AM-4PM'
    },
    {
      city: 'Casablanca',
      address: '789 Hassan II Boulevard, Casablanca 20000, Morocco',
      phone: '+212 522 000 003',
      hours: 'Mon-Fri: 9AM-6PM'
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-primary-50 to-sand-50">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="heading-xl text-neutral-900 mb-6">
              Get in Touch
            </h1>
            <p className="text-body-lg text-neutral-600 mb-8">
              Ready to start planning your Morocco adventure? Our travel experts are here to help 
              you create the perfect personalized experience.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {contactInfo.map((contact, index) => (
              <motion.a
                key={index}
                href={contact.action}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                viewport={{ once: true }}
                className="card-feature text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <contact.icon className="w-8 h-8 text-primary-600" />
                </div>
                <h3 className="heading-md text-neutral-900 mb-2">{contact.title}</h3>
                <p className="font-semibold text-primary-600 mb-2">{contact.details}</p>
                <p className="text-sm text-neutral-500">{contact.description}</p>
              </motion.a>
            ))}
          </div>

          {/* Contact Form & Info */}
          <div className="grid lg:grid-cols-2 gap-16">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="heading-lg text-neutral-900 mb-6">Send us a Message</h2>
              
              {isSubmitted ? (
                <div className="text-center py-12">
                  <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
                  <h3 className="heading-md text-neutral-900 mb-2">Message Sent!</h3>
                  <p className="text-neutral-600">
                    Thank you for contacting us. We'll get back to you within 2 hours.
                  </p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        required
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        className="input-field"
                        placeholder="Your full name"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        required
                        value={formData.email}
                        onChange={(e) => setFormData({...formData, email: e.target.value})}
                        className="input-field"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => setFormData({...formData, phone: e.target.value})}
                        className="input-field"
                        placeholder="+****************"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">
                        Travel Dates
                      </label>
                      <input
                        type="text"
                        value={formData.travelDates}
                        onChange={(e) => setFormData({...formData, travelDates: e.target.value})}
                        className="input-field"
                        placeholder="When are you planning to visit?"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Subject *
                    </label>
                    <select
                      required
                      value={formData.subject}
                      onChange={(e) => setFormData({...formData, subject: e.target.value})}
                      className="input-field"
                    >
                      <option value="">Select a subject</option>
                      <option value="trip-planning">Trip Planning</option>
                      <option value="booking-inquiry">Booking Inquiry</option>
                      <option value="agent-matching">Agent Matching</option>
                      <option value="custom-experience">Custom Experience</option>
                      <option value="group-travel">Group Travel</option>
                      <option value="support">Support</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-2">
                      Message *
                    </label>
                    <textarea
                      required
                      rows={6}
                      value={formData.message}
                      onChange={(e) => setFormData({...formData, message: e.target.value})}
                      className="input-field resize-none"
                      placeholder="Tell us about your dream Morocco experience..."
                    />
                  </div>

                  <button type="submit" className="btn-primary w-full">
                    <Send className="w-4 h-4 mr-2" />
                    Send Message
                  </button>
                </form>
              )}
            </motion.div>

            {/* Office Locations */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="heading-lg text-neutral-900 mb-6">Our Offices</h2>
              
              <div className="space-y-6">
                {offices.map((office, index) => (
                  <div key={index} className="card">
                    <h3 className="font-semibold text-neutral-900 mb-3">{office.city}</h3>
                    
                    <div className="space-y-3 text-sm">
                      <div className="flex items-start gap-3">
                        <MapPin className="w-4 h-4 text-primary-500 mt-0.5 flex-shrink-0" />
                        <span className="text-neutral-600">{office.address}</span>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <Phone className="w-4 h-4 text-primary-500 flex-shrink-0" />
                        <span className="text-neutral-600">{office.phone}</span>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <Clock className="w-4 h-4 text-primary-500 flex-shrink-0" />
                        <span className="text-neutral-600">{office.hours}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* FAQ Quick Links */}
              <div className="mt-8">
                <h3 className="font-semibold text-neutral-900 mb-4">Quick Help</h3>
                <div className="space-y-2">
                  <a href="/help" className="block text-primary-600 hover:text-primary-700 text-sm">
                    → Frequently Asked Questions
                  </a>
                  <a href="/help" className="block text-primary-600 hover:text-primary-700 text-sm">
                    → Booking & Cancellation Policy
                  </a>
                  <a href="/help" className="block text-primary-600 hover:text-primary-700 text-sm">
                    → Travel Requirements & Visa Info
                  </a>
                  <a href="/help" className="block text-primary-600 hover:text-primary-700 text-sm">
                    → Payment & Pricing Information
                  </a>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Emergency Contact */}
      <section className="py-16 bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div className="container-custom text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="heading-md text-white mb-4">24/7 Emergency Support</h2>
            <p className="text-primary-100 mb-6 max-w-2xl mx-auto">
              Traveling with us? Our emergency support team is available around the clock 
              to assist you during your Morocco journey.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="tel:+212522000000" className="btn-secondary btn-lg">
                <Phone className="mr-3 h-5 w-5" />
                Emergency Hotline
              </a>
              <a href="https://wa.me/212522000000" className="btn-primary btn-lg bg-white text-primary-600 hover:bg-neutral-100">
                <MessageCircle className="mr-3 h-5 w-5" />
                WhatsApp Support
              </a>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  )
}