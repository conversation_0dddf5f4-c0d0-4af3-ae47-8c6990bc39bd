'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Head<PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { TripSummaryWidget } from '@/components/navigation/trip-summary-widget'
import { OptimizedDestinationMap } from '@/components/discovery/optimized-destination-map'
import { EnhancedSearchSystem } from '@/components/discovery/enhanced-search-system'
import { EnhancedTabNavigation } from '@/components/ui/enhanced-tab-navigation'
import { TouchOptimizedContainer, CollapsibleSection, SwipeableCards } from '@/components/discovery/mobile-touch-optimized'
import { EnhancedButton } from '@/components/ui/enhanced-button'
import { LoadingSpinner, DestinationCardSkeleton } from '@/components/ui/loading-states'
import { SwipeableCard, TouchFriendlyButton, HorizontalScroll } from '@/components/ui/mobile-optimized'
import { Breadcrumb } from '@/components/navigation/breadcrumb'
import { DestinationSelector } from '@/components/navigation/destination-selector'
import { useTripIntegration, TripUtils } from '@/lib/trip-integration'
import { useCountryContext } from '@/lib/country-context'
import { useRealTimeData, PricingIndicator, AvailabilityIndicator } from '@/lib/real-time-data'
import { useUserPreferences } from '@/lib/user-preferences'
import { CulturalIntegration } from '@/components/cultural-immersion/cultural-integration'
import {
  MapPin,
  Compass,
  Route,
  Calendar,
  Users,
  Star,
  ArrowRight,
  Play,
  Info,
  Search,
  Filter,
  Grid,
  List,
  Heart,
  Camera,
  Mountain,
  Building,
  CheckCircle
} from 'lucide-react'

export default function ExplorePage() {
  const { currentCountry } = useCountryContext()
  const { addDestination, removeDestination, isDestinationAdded, getTripSummary } = useTripIntegration()
  const { subscribeToDestination, liveAlerts } = useRealTimeData()
  const { addToViewHistory, preferences } = useUserPreferences()
  const [selectedDestinations, setSelectedDestinations] = useState<string[]>([])
  const [viewMode, setViewMode] = useState<'map' | 'grid' | 'cards'>(
    preferences.personalization.interactionPatterns.preferredViewMode === 'list' ? 'cards' :
      preferences.personalization.interactionPatterns.preferredViewMode as 'map' | 'grid'
  )
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [showMobileFilters, setShowMobileFilters] = useState(false)
  const [showTripSuccess, setShowTripSuccess] = useState(false)

  const handleDestinationToggle = (destinationId: string) => {
    const isCurrentlySelected = selectedDestinations?.includes(destinationId) || false

    // Track user interaction
    addToViewHistory(destinationId)

    if (isCurrentlySelected) {
      // Remove from local state and trip
      setSelectedDestinations(prev => prev.filter(id => id !== destinationId))
      // Note: We'd need to track destination IDs to remove from trip properly
    } else {
      // Add to local state and trip
      setSelectedDestinations(prev => [...prev, destinationId])

      // Subscribe to real-time data for this destination
      subscribeToDestination(destinationId)

      // Convert to trip destination and add to trip
      const destinations = TripUtils.convertExplorationsToDestinations([destinationId], currentCountry.code)
      if (destinations.length > 0) {
        addDestination(destinations[0])
        setShowTripSuccess(true)
        setTimeout(() => setShowTripSuccess(false), 3000)
      }
    }
  }

  const handleSearchResult = (result: any) => {
    // Handle search result selection
    if (result.type === 'destination') {
      handleDestinationToggle(result.id)
    }
    // Navigate to result page
    window.location.href = result.href
  }

  const saveToTrip = () => {
    // Convert all selected destinations to trip destinations
    const destinations = TripUtils.convertExplorationsToDestinations(selectedDestinations, currentCountry.code)

    destinations.forEach(destination => {
      if (!isDestinationAdded(destination.name)) {
        addDestination(destination)
      }
    })

    setShowTripSuccess(true)
    setTimeout(() => setShowTripSuccess(false), 3000)
  }

  // Load existing trip destinations on component mount
  useEffect(() => {
    const tripSummary = getTripSummary()
    // You could sync selectedDestinations with trip data here if needed
  }, [])

  const mapRegions = [
    {
      id: 'north',
      name: 'Northern Morocco',
      cities: ['Tangier', 'Chefchaouen', 'Tetouan'],
      description: 'Mediterranean coast and Rif Mountains',
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'imperial',
      name: 'Imperial Cities',
      cities: ['Fes', 'Meknes', 'Rabat'],
      description: 'Historic capitals and cultural heritage',
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: 'central',
      name: 'Central Morocco',
      cities: ['Marrakech', 'Atlas Mountains'],
      description: 'Red city and mountain adventures',
      color: 'from-red-500 to-red-600'
    },
    {
      id: 'south',
      name: 'Southern Morocco',
      cities: ['Sahara Desert', 'Ouarzazate', 'Zagora'],
      description: 'Desert landscapes and oases',
      color: 'from-yellow-500 to-orange-600'
    },
    {
      id: 'coast',
      name: 'Atlantic Coast',
      cities: ['Casablanca', 'Essaouira', 'Agadir'],
      description: 'Coastal cities and ocean adventures',
      color: 'from-teal-500 to-teal-600'
    }
  ]

  const travelTips = [
    {
      icon: Calendar,
      title: 'Best Time to Visit',
      description: 'Spring (March-May) and Fall (September-November) offer perfect weather'
    },
    {
      icon: Route,
      title: 'Recommended Duration',
      description: '7-14 days allows you to experience 3-4 destinations comfortably'
    },
    {
      icon: Users,
      title: 'Group Size',
      description: 'Small groups (2-8 people) provide more personalized experiences'
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      <Header />

      {/* Live Alerts */}
      {liveAlerts.length > 0 && (
        <div className="fixed top-20 left-4 space-y-2 z-50 max-w-sm">
          {liveAlerts.slice(0, 3).map((alert) => (
            <motion.div
              key={alert.id}
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -50 }}
              className={`p-4 rounded-lg shadow-lg border-l-4 ${alert.severity === 'urgent' ? 'bg-red-50 border-red-500 text-red-800' :
                alert.severity === 'warning' ? 'bg-yellow-50 border-yellow-500 text-yellow-800' :
                  'bg-blue-50 border-blue-500 text-blue-800'
                }`}
            >
              <h4 className="font-semibold text-sm">{alert.title}</h4>
              <p className="text-xs mt-1">{alert.message}</p>
            </motion.div>
          ))}
        </div>
      )}

      {/* Trip Success Notification */}
      {showTripSuccess && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          className="fixed top-20 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 flex items-center gap-2"
        >
          <CheckCircle className="w-5 h-5" />
          <span>Added to your trip!</span>
        </motion.div>
      )}

      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-primary-50 to-sand-50">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="heading-xl text-neutral-900 mb-6">
              Explore Morocco
              <span className="block bg-gradient-to-r from-primary-500 to-morocco-500 bg-clip-text text-transparent">
                Your Way
              </span>
            </h1>
            <p className="text-body-lg text-neutral-600 mb-8">
              Discover Morocco's diverse regions and build your perfect itinerary.
              Select destinations that interest you and we'll help you create an unforgettable journey.
            </p>

            {/* Enhanced Search System */}
            <div className="mb-8">
              <EnhancedSearchSystem
                placeholder="Search destinations, experiences, guides..."
                onResultSelect={handleSearchResult}
                className="max-w-2xl mx-auto"
              />
            </div>

            <div className="flex justify-center">
              <EnhancedTabNavigation
                tabs={[
                  { id: 'map', label: 'Interactive Map', icon: Compass },
                  { id: 'grid', label: 'Grid View', icon: Grid },
                  { id: 'cards', label: 'Cards', icon: List, className: 'md:hidden' }
                ]}
                activeTab={viewMode}
                onTabChange={(tab) => setViewMode(tab as 'map' | 'grid' | 'cards')}
                variant="pills"
                className="bg-neutral-100 rounded-xl p-1"
              />
            </div>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <section className="section-padding">
        <div className="container-custom">
          {viewMode === 'map' ? (
            /* Enhanced Interactive Map View */
            <div className="space-y-12">
              <div className="relative">
                <h2 className="heading-lg text-neutral-900 mb-8 text-center">Interactive {currentCountry.name} Map</h2>
                <OptimizedDestinationMap
                  selectedDestinations={selectedDestinations}
                  onDestinationSelect={handleDestinationToggle}
                  className="mb-8"
                />
              </div>

              {/* Smart Content Sections */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <CollapsibleSection
                  title="Travel Tips"
                  icon={Info}
                  defaultExpanded={true}
                  className="lg:col-span-1"
                >
                  <div className="space-y-4">
                    {travelTips.map((tip, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <tip.icon className="w-5 h-5 text-primary-500 mt-1" />
                        <div>
                          <h4 className="font-medium text-neutral-900 mb-1">{tip.title}</h4>
                          <p className="text-sm text-neutral-600">{tip.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CollapsibleSection>

                <CollapsibleSection
                  title="Selected Destinations"
                  icon={Heart}
                  defaultExpanded={true}
                  className="lg:col-span-2"
                >
                  {selectedDestinations.length > 0 ? (
                    <div className="space-y-3">
                      {selectedDestinations.map((destId, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-primary-50 rounded-lg">
                          <span className="font-medium text-primary-900 capitalize">{destId}</span>
                          <button
                            onClick={() => handleDestinationToggle(destId)}
                            className="text-primary-600 hover:text-primary-800"
                          >
                            Remove
                          </button>
                        </div>
                      ))}
                      <button
                        onClick={saveToTrip}
                        className="w-full btn-primary mt-4"
                      >
                        Save to Trip Builder
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </button>
                    </div>
                  ) : (
                    <p className="text-neutral-600 text-center py-8">
                      Click on destinations in the map to add them to your trip
                    </p>
                  )}
                </CollapsibleSection>
              </div>
            </div>
          ) : viewMode === 'cards' ? (
            /* Mobile Cards View */
            <div className="md:hidden">
              <SwipeableCards
                items={mapRegions}
                renderCard={(region, index) => (
                  <div className="bg-white rounded-2xl shadow-lg border border-neutral-200 overflow-hidden">
                    <div className={`h-32 bg-gradient-to-r ${region.color} flex items-center justify-center`}>
                      <h3 className="text-xl font-bold text-white text-center">{region.name}</h3>
                    </div>
                    <div className="p-6">
                      <p className="text-neutral-600 mb-4">{region.description}</p>
                      <div className="space-y-2 mb-4">
                        {region.cities.map((city: string, cityIndex: number) => (
                          <div key={cityIndex} className="flex items-center gap-2">
                            <MapPin className="w-4 h-4 text-primary-500" />
                            <span className="text-sm text-neutral-700">{city}</span>
                          </div>
                        ))}
                      </div>
                      <button className="w-full btn-primary">
                        Explore Region
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </button>
                    </div>
                  </div>
                )}
                onSwipe={(direction, region) => {
                  console.log(`Swiped ${direction} on ${region.name}`)
                }}
              />
            </div>
          ) : (
            /* Grid View */
            <div className="space-y-8">
              <h2 className="heading-lg text-neutral-900 text-center">Explore by Region</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {mapRegions.map((region, index) => (
                  <motion.div
                    key={region.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-white rounded-xl shadow-sm border border-neutral-200 overflow-hidden hover:shadow-lg transition-shadow"
                  >
                    <div className={`h-32 bg-gradient-to-r ${region.color} flex items-center justify-center`}>
                      <h3 className="text-xl font-bold text-white text-center">{region.name}</h3>
                    </div>
                    <div className="p-6">
                      <p className="text-neutral-600 mb-4">{region.description}</p>
                      <div className="space-y-2 mb-6">
                        {region.cities.map((city, cityIndex) => (
                          <div key={cityIndex} className="flex items-center gap-2">
                            <MapPin className="w-4 h-4 text-primary-500" />
                            <span className="text-sm text-neutral-700">{city}</span>
                          </div>
                        ))}
                      </div>
                      <button
                        onClick={() => {
                          // Add region cities to selected destinations
                          region.cities.forEach(city => {
                            const cityId = city.toLowerCase().replace(/\s+/g, '-')
                            if (!selectedDestinations?.includes(cityId)) {
                              handleDestinationToggle(cityId)
                            }
                          })
                        }}
                        className="w-full btn-primary"
                      >
                        Explore Region
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Selected Destinations Summary */}
              {selectedDestinations.length > 0 && (
                <div className="bg-primary-50 rounded-xl p-6 border border-primary-200">
                  <h3 className="font-semibold text-primary-900 mb-4">Your Selected Destinations</h3>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {selectedDestinations.map((destId, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-sm font-medium"
                      >
                        {destId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                    ))}
                  </div>
                  <button
                    onClick={saveToTrip}
                    className="btn-primary"
                  >
                    Save to Trip Builder
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </section>

      {/* Trip Summary Widget */}
      <TripSummaryWidget />

      <Footer />
    </div>
  )
}