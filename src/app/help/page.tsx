'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { 
  Search,
  ChevronDown,
  ChevronUp,
  HelpCircle,
  Book,
  CreditCard,
  MapPin,
  Users,
  Shield,
  Phone,
  MessageCircle,
  Mail,
  ExternalLink
} from 'lucide-react'

interface FAQ {
  id: string
  question: string
  answer: string
  category: string
}

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('')
  const [activeCategory, setActiveCategory] = useState('all')
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null)

  const categories = [
    { id: 'all', name: 'All Topics', icon: HelpCircle },
    { id: 'booking', name: 'Booking & Payment', icon: CreditCard },
    { id: 'travel', name: 'Travel Planning', icon: MapPin },
    { id: 'experiences', name: 'Experiences', icon: Book },
    { id: 'agents', name: 'Agents & Guides', icon: Users },
    { id: 'safety', name: 'Safety & Support', icon: Shield }
  ]

  const faqs: FAQ[] = [
    {
      id: '1',
      question: 'How do I book an experience with ComeToMorocco?',
      answer: 'Booking is simple! Browse our destinations or experiences, select what interests you, choose your dates, and follow our step-by-step booking process. You can also use our Journey Builder to create a custom itinerary, and we\'ll match you with the perfect local agent.',
      category: 'booking'
    },
    {
      id: '2',
      question: 'What payment methods do you accept?',
      answer: 'We accept all major credit cards (Visa, Mastercard, American Express), PayPal, and bank transfers. All payments are processed securely through our encrypted payment system. You can pay in full or choose our flexible payment plan options.',
      category: 'booking'
    },
    {
      id: '3',
      question: 'Can I cancel or modify my booking?',
      answer: 'Yes, you can cancel or modify your booking depending on our cancellation policy. Most experiences can be cancelled up to 48 hours before the start time for a full refund. Some specialized experiences may have different terms. Check your booking confirmation for specific details.',
      category: 'booking'
    },
    {
      id: '4',
      question: 'How are your local agents selected?',
      answer: 'All our agents go through a rigorous vetting process. They must be licensed tour guides with proven experience, excellent customer reviews, and deep local knowledge. We also verify their certifications, conduct background checks, and regularly monitor their performance through customer feedback.',
      category: 'agents'
    },
    {
      id: '5',
      question: 'Do I need a visa to visit Morocco?',
      answer: 'Visa requirements depend on your nationality. Citizens of many countries including the US, EU, UK, Canada, and Australia can enter Morocco visa-free for up to 90 days. However, requirements can change, so we recommend checking with the Moroccan consulate in your country before traveling.',
      category: 'travel'
    },
    {
      id: '6',
      question: 'What should I pack for Morocco?',
      answer: 'Pack comfortable walking shoes, modest clothing that covers shoulders and knees, sunscreen, a hat, and layers for temperature changes. In winter, bring warm clothes for mountain areas. For desert trips, include long sleeves, pants, and a scarf for sandstorms. We provide a detailed packing list after booking.',
      category: 'travel'
    },
    {
      id: '7',
      question: 'Is Morocco safe for tourists?',
      answer: 'Morocco is generally very safe for tourists. Our local agents are trained in safety protocols and know the safest routes and areas. We provide 24/7 emergency support and all our experiences include safety briefings. We also offer travel insurance recommendations for additional peace of mind.',
      category: 'safety'
    },
    {
      id: '8',
      question: 'What makes your experiences different from regular tours?',
      answer: 'Our experiences focus on authentic cultural immersion rather than typical tourist activities. You\'ll stay with local families, learn traditional crafts from master artisans, and discover hidden gems that only locals know. Each experience is personalized based on your interests and cultural comfort level.',
      category: 'experiences'
    },
    {
      id: '9',
      question: 'Can you accommodate dietary restrictions?',
      answer: 'Absolutely! We can accommodate most dietary restrictions including vegetarian, vegan, gluten-free, halal, and kosher requirements. Just let us know during booking, and we\'ll ensure all meals and cooking experiences are tailored to your needs.',
      category: 'experiences'
    },
    {
      id: '10',
      question: 'What if I need help during my trip?',
      answer: 'We provide 24/7 support during your trip. You\'ll have direct contact with your local agent, plus our emergency hotline and WhatsApp support. Our team can help with anything from restaurant recommendations to emergency assistance.',
      category: 'safety'
    },
    {
      id: '11',
      question: 'How far in advance should I book?',
      answer: 'We recommend booking at least 2-4 weeks in advance, especially during peak seasons (March-May and September-November). However, we can often accommodate last-minute bookings depending on availability. Popular experiences and specific agents may require more advance notice.',
      category: 'booking'
    },
    {
      id: '12',
      question: 'Do you offer group discounts?',
      answer: 'Yes! We offer special rates for groups of 6 or more people. Group experiences can also be customized for corporate retreats, family reunions, or special celebrations. Contact us for a personalized group quote.',
      category: 'booking'
    }
  ]

  const filteredFAQs = faqs.filter(faq => {
    const matchesCategory = activeCategory === 'all' || faq.category === activeCategory
    const matchesSearch = searchQuery === '' || 
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesCategory && matchesSearch
  })

  const toggleFAQ = (id: string) => {
    setExpandedFAQ(expandedFAQ === id ? null : id)
  }

  const contactOptions = [
    {
      icon: MessageCircle,
      title: 'Live Chat',
      description: 'Get instant answers from our support team',
      action: 'Start Chat',
      available: '24/7'
    },
    {
      icon: Mail,
      title: 'Email Support',
      description: 'Send us a detailed message',
      action: 'Send Email',
      available: 'Response within 2 hours'
    },
    {
      icon: Phone,
      title: 'Phone Support',
      description: 'Speak directly with our team',
      action: 'Call Now',
      available: 'Mon-Fri 9AM-6PM GMT+1'
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-primary-50 to-sand-50">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-3xl mx-auto"
          >
            <h1 className="heading-xl text-neutral-900 mb-6">
              How Can We Help?
            </h1>
            <p className="text-body-lg text-neutral-600 mb-8">
              Find answers to common questions about booking, travel planning, and experiencing Morocco with us.
            </p>
            
            {/* Search Bar */}
            <div className="relative max-w-xl mx-auto">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-neutral-400" />
              <input
                type="text"
                placeholder="Search for answers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-field pl-12 text-lg"
              />
            </div>
          </motion.div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-8 border-b border-neutral-200">
        <div className="container-custom">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`flex items-center gap-2 px-6 py-3 rounded-full transition-all duration-200 ${
                  activeCategory === category.id
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-600 hover:bg-neutral-200'
                }`}
              >
                <category.icon className="w-4 h-4" />
                <span className="font-medium">{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid lg:grid-cols-3 gap-12">
            {/* FAQ List */}
            <div className="lg:col-span-2">
              <h2 className="heading-lg text-neutral-900 mb-8">
                {activeCategory === 'all' ? 'Frequently Asked Questions' : 
                 categories.find(c => c.id === activeCategory)?.name + ' Questions'}
              </h2>
              
              {filteredFAQs.length === 0 ? (
                <div className="text-center py-12">
                  <HelpCircle className="w-16 h-16 text-neutral-300 mx-auto mb-4" />
                  <h3 className="heading-md text-neutral-900 mb-2">No results found</h3>
                  <p className="text-neutral-600">
                    Try adjusting your search or browse different categories.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredFAQs.map((faq, index) => (
                    <motion.div
                      key={faq.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="card border border-neutral-200 hover:border-primary-300 transition-colors duration-200"
                    >
                      <button
                        onClick={() => toggleFAQ(faq.id)}
                        className="w-full flex items-center justify-between text-left"
                      >
                        <h3 className="font-semibold text-neutral-900 pr-4">{faq.question}</h3>
                        {expandedFAQ === faq.id ? (
                          <ChevronUp className="w-5 h-5 text-neutral-500 flex-shrink-0" />
                        ) : (
                          <ChevronDown className="w-5 h-5 text-neutral-500 flex-shrink-0" />
                        )}
                      </button>
                      
                      <AnimatePresence>
                        {expandedFAQ === faq.id && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                            className="overflow-hidden"
                          >
                            <div className="pt-4 border-t border-neutral-100 mt-4">
                              <p className="text-neutral-600 leading-relaxed">{faq.answer}</p>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))}
                </div>
              )}
            </div>

            {/* Contact Support */}
            <div className="lg:col-span-1">
              <div className="sticky top-24 space-y-8">
                <div>
                  <h3 className="heading-md text-neutral-900 mb-6">Still Need Help?</h3>
                  <div className="space-y-4">
                    {contactOptions.map((option, index) => (
                      <div key={index} className="card">
                        <div className="flex items-start gap-4">
                          <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <option.icon className="w-6 h-6 text-primary-600" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-neutral-900 mb-1">{option.title}</h4>
                            <p className="text-sm text-neutral-600 mb-2">{option.description}</p>
                            <p className="text-xs text-neutral-500 mb-3">{option.available}</p>
                            <button className="btn-primary btn-sm w-full">
                              {option.action}
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Quick Links */}
                <div>
                  <h3 className="heading-md text-neutral-900 mb-6">Quick Links</h3>
                  <div className="space-y-3">
                    <a href="/contact" className="flex items-center gap-2 text-primary-600 hover:text-primary-700">
                      <ExternalLink className="w-4 h-4" />
                      Contact Us
                    </a>
                    <a href="/about" className="flex items-center gap-2 text-primary-600 hover:text-primary-700">
                      <ExternalLink className="w-4 h-4" />
                      About ComeToMorocco
                    </a>
                    <a href="/agents" className="flex items-center gap-2 text-primary-600 hover:text-primary-700">
                      <ExternalLink className="w-4 h-4" />
                      Meet Our Agents
                    </a>
                    <a href="/destinations/marrakech" className="flex items-center gap-2 text-primary-600 hover:text-primary-700">
                      <ExternalLink className="w-4 h-4" />
                      Explore Destinations
                    </a>
                  </div>
                </div>

                {/* Emergency Contact */}
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <h4 className="font-semibold text-red-900 mb-2">Emergency Support</h4>
                  <p className="text-sm text-red-700 mb-3">
                    For urgent assistance during your trip
                  </p>
                  <a href="tel:+212522000000" className="btn-accent btn-sm w-full">
                    <Phone className="w-4 h-4 mr-2" />
                    Emergency Hotline
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}