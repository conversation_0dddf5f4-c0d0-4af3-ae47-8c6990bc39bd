'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { useTripIntegration } from '@/lib/trip-integration'
import { 
  MapPin, 
  Clock, 
  Users, 
  Star, 
  Play,
  Pause,
  Volume2,
  VolumeX,
  ChevronLeft,
  ChevronRight,
  Compass,
  Camera,
  BookOpen,
  Palette,
  Hammer,
  Eye,
  Navigation,
  Timer,
  Award,
  Plus
} from 'lucide-react'
import { EnhancedActivityCard } from '@/components/ui/enhanced-activity-card'

interface StoryPoint {
  id: string
  title: string
  description: string
  longDescription: string
  coordinates: { x: number; y: number }
  category: 'history' | 'craft' | 'culture' | 'hidden'
  audioUrl?: string
  images: string[]
  facts: string[]
  experiences: {
    title: string
    duration: string
    price: string
    description: string
  }[]
}

interface TimelineEvent {
  year: string
  title: string
  description: string
  significance: string
}

interface Experience {
  id: string
  title: string
  description: string
  duration: string
  price: number
  rating: number
  category: string
  image: string
  highlights: string[]
  difficulty: 'Easy' | 'Moderate' | 'Challenging'
  timeOfDay: 'Morning' | 'Afternoon' | 'Evening' | 'Full Day'
}

export default function FesPage() {
  const { addDestination, addExperience } = useTripIntegration()
  const [activeStory, setActiveStory] = useState<string | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [viewMode, setViewMode] = useState<'explore' | 'timeline' | 'experiences'>('explore')
  const [discoveredPoints, setDiscoveredPoints] = useState<string[]>([])
  const [selectedExperiences, setSelectedExperiences] = useState<Experience[]>([])
  const [showAddedFeedback, setShowAddedFeedback] = useState<string | null>(null)
  const [activeFilter, setActiveFilter] = useState('all')
  const audioRef = useRef<HTMLAudioElement>(null)

  const storyPoints: StoryPoint[] = [
    {
      id: 'medina-entrance',
      title: 'The Ancient Gates',
      description: 'Where time stands still at the entrance to the world\'s largest car-free urban area',
      longDescription: 'Step through the Bab Boujloud, the iconic blue gate that has welcomed travelers for centuries. This isn\'t just an entrance—it\'s a portal between worlds. The intricate zellige tilework tells stories of master craftsmen who spent years perfecting their art. As you pass through, you\'re entering a UNESCO World Heritage site that has remained virtually unchanged since the Middle Ages.',
      coordinates: { x: 20, y: 30 },
      category: 'history',
      images: [
        'https://images.unsplash.com/photo-1570026517541-7c6c7c6c6c6c?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&h=600&fit=crop'
      ],
      facts: [
        'Built in 1913, replacing the original 12th-century gate',
        'The blue represents the city of Fes, green represents Islam',
        'Over 1 million visitors pass through annually'
      ],
      experiences: [
        {
          title: 'Gate Guardian Stories',
          duration: '45 min',
          price: '€25',
          description: 'Meet the families who have guarded these gates for generations'
        },
        {
          title: 'Architectural Deep Dive',
          duration: '1.5 hours',
          price: '€45',
          description: 'Understand the mathematical precision behind Islamic architecture'
        }
      ]
    },
    {
      id: 'tanneries',
      title: 'The Ancient Tanneries',
      description: 'Where leather has been crafted using 1000-year-old techniques',
      longDescription: 'The Chouara Tannery is a sensory explosion that connects you directly to medieval Morocco. The process hasn\'t changed in a millennium—animal hides are still treated with natural ingredients like pigeon droppings, cow urine, and plant extracts. The rainbow of dye vats creates one of the world\'s most photographed industrial sites, but beyond the Instagram moment lies a complex ecosystem of family businesses and ancient knowledge.',
      coordinates: { x: 60, y: 40 },
      category: 'craft',
      images: [
        'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop'
      ],
      facts: [
        'Operating continuously for over 1000 years',
        'Uses only natural dyes and materials',
        'Employs over 500 families in the medina'
      ],
      experiences: [
        {
          title: 'Master Tanner Experience',
          duration: '2 hours',
          price: '€65',
          description: 'Work alongside master tanners and create your own leather piece'
        },
        {
          title: 'Rooftop Photography Session',
          duration: '1 hour',
          price: '€35',
          description: 'Capture the perfect shot with professional guidance'
        }
      ]
    },
    {
      id: 'al-qarawiyyin',
      title: 'The World\'s First University',
      description: 'Founded by a woman in 859 AD, predating Oxford by 200 years',
      longDescription: 'Al-Qarawiyyin University represents one of humanity\'s greatest intellectual achievements. Founded by Fatima al-Fihri, a visionary woman who used her inheritance to create a center of learning that would influence the world. This is where algebra was refined, where European scholars came to learn, and where the foundations of the modern university system were laid. The library contains manuscripts that predate the printing press by centuries.',
      coordinates: { x: 45, y: 25 },
      category: 'history',
      images: [
        'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&h=600&fit=crop'
      ],
      facts: [
        'Founded in 859 AD by Fatima al-Fihri',
        'Oldest continuously operating university in the world',
        'Library contains 4,000 rare manuscripts'
      ],
      experiences: [
        {
          title: 'Scholar\'s Journey',
          duration: '3 hours',
          price: '€85',
          description: 'Explore the university with an Islamic studies professor'
        },
        {
          title: 'Manuscript Workshop',
          duration: '2 hours',
          price: '€55',
          description: 'Learn traditional calligraphy and bookbinding techniques'
        }
      ]
    },
    {
      id: 'hidden-fondouk',
      title: 'Secret Caravanserai',
      description: 'Hidden courtyards where Saharan traders once rested',
      longDescription: 'Tucked away in the labyrinthine streets lies a perfectly preserved fondouk—a medieval inn where caravans from across Africa would rest. These hidden courtyards tell the story of Fes as the crossroads of Africa, where gold, salt, and slaves were traded. Today, only a few locals know how to find these spaces, making them some of the most exclusive experiences in Morocco.',
      coordinates: { x: 75, y: 60 },
      category: 'hidden',
      images: [
        'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1570026517541-7c6c7c6c6c6c?w=800&h=600&fit=crop'
      ],
      facts: [
        'Over 200 fondouks once operated in Fes',
        'Served as hotels, warehouses, and trading posts',
        'Some date back to the 13th century'
      ],
      experiences: [
        {
          title: 'Trader\'s Tale',
          duration: '2.5 hours',
          price: '€75',
          description: 'Follow ancient trade routes with a local historian'
        },
        {
          title: 'Hidden Courtyard Tea',
          duration: '1 hour',
          price: '€40',
          description: 'Private tea ceremony in a secret fondouk'
        }
      ]
    },
    {
      id: 'pottery-quarter',
      title: 'The Potter\'s Quarter',
      description: 'Where clay becomes art through fire and tradition',
      longDescription: 'In the pottery quarter, families have been shaping clay for over 600 years. The distinctive blue and white Fes pottery isn\'t just decorative—it\'s a complex art form that requires years to master. Watch as potters use techniques passed down through generations, creating pieces that will grace tables around the world. The kilns here have been burning continuously for centuries.',
      coordinates: { x: 30, y: 70 },
      category: 'craft',
      images: [
        'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop',
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop'
      ],
      facts: [
        'Fes blue pottery is famous worldwide',
        'Techniques unchanged for 600 years',
        'Clay comes from nearby mountains'
      ],
      experiences: [
        {
          title: 'Master Potter Workshop',
          duration: '3 hours',
          price: '€70',
          description: 'Create your own pottery piece with a master craftsman'
        },
        {
          title: 'Kiln Fire Ceremony',
          duration: '2 hours',
          price: '€50',
          description: 'Witness the ancient firing process'
        }
      ]
    }
  ]

  const timeline: TimelineEvent[] = [
    {
      year: '789 AD',
      title: 'Foundation of Fes',
      description: 'Idris I establishes the city',
      significance: 'Beginning of Morocco\'s first imperial capital'
    },
    {
      year: '859 AD',
      title: 'Al-Qarawiyyin Founded',
      description: 'Fatima al-Fihri establishes the university',
      significance: 'World\'s first degree-granting university'
    },
    {
      year: '1070 AD',
      title: 'Almoravid Expansion',
      description: 'City becomes major trading hub',
      significance: 'Fes emerges as center of trans-Saharan trade'
    },
    {
      year: '1250 AD',
      title: 'Marinid Golden Age',
      description: 'Major architectural developments',
      significance: 'Most of today\'s medina architecture built'
    },
    {
      year: '1981 AD',
      title: 'UNESCO Recognition',
      description: 'Medina becomes World Heritage Site',
      significance: 'Global recognition of cultural importance'
    }
  ]

  const experiences: Experience[] = [
    {
      id: 'medina-storytelling',
      title: 'Medina Storytelling Walk',
      description: 'Journey through 1000 years of history with a local storyteller who brings the ancient walls to life',
      duration: '3 hours',
      price: 45,
      rating: 4.9,
      category: 'culture',
      image: 'https://images.unsplash.com/photo-1570026517541-7c6c7c6c6c6c?w=600&h=400&fit=crop',
      highlights: ['Hidden courtyards', 'Ancient stories', 'Local legends'],
      difficulty: 'Easy',
      timeOfDay: 'Morning'
    },
    {
      id: 'tannery-workshop',
      title: 'Master Tanner Experience',
      description: 'Work alongside master tanners and create your own leather piece using 1000-year-old techniques',
      duration: '2.5 hours',
      price: 65,
      rating: 4.8,
      category: 'crafts',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop',
      highlights: ['Hands-on creation', 'Master techniques', 'Take home piece'],
      difficulty: 'Moderate',
      timeOfDay: 'Morning'
    },
    {
      id: 'university-scholar',
      title: 'Scholar\'s Journey at Al-Qarawiyyin',
      description: 'Explore the world\'s oldest university with an Islamic studies professor and discover ancient manuscripts',
      duration: '3 hours',
      price: 85,
      rating: 5.0,
      category: 'history',
      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=600&h=400&fit=crop',
      highlights: ['Ancient manuscripts', 'Professor guide', 'University history'],
      difficulty: 'Easy',
      timeOfDay: 'Afternoon'
    },
    {
      id: 'pottery-master',
      title: 'Master Potter Workshop',
      description: 'Create your own Fes blue pottery with a master craftsman using 600-year-old techniques',
      duration: '3 hours',
      price: 70,
      rating: 4.7,
      category: 'crafts',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop',
      highlights: ['Create pottery', 'Master craftsman', 'Traditional techniques'],
      difficulty: 'Moderate',
      timeOfDay: 'Afternoon'
    },
    {
      id: 'hidden-fondouk',
      title: 'Secret Caravanserai Discovery',
      description: 'Explore hidden courtyards where Saharan traders once rested with a local historian',
      duration: '2.5 hours',
      price: 75,
      rating: 4.9,
      category: 'history',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=600&h=400&fit=crop',
      highlights: ['Hidden courtyards', 'Trade route stories', 'Local historian'],
      difficulty: 'Easy',
      timeOfDay: 'Afternoon'
    },
    {
      id: 'calligraphy-workshop',
      title: 'Arabic Calligraphy & Manuscripts',
      description: 'Learn traditional calligraphy and bookbinding techniques in the shadow of ancient libraries',
      duration: '2 hours',
      price: 55,
      rating: 4.6,
      category: 'arts',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop',
      highlights: ['Calligraphy skills', 'Bookbinding', 'Ancient techniques'],
      difficulty: 'Moderate',
      timeOfDay: 'Morning'
    },
    {
      id: 'rooftop-photography',
      title: 'Tannery Rooftop Photography',
      description: 'Capture the perfect shot of the famous tanneries with professional photography guidance',
      duration: '1.5 hours',
      price: 35,
      rating: 4.5,
      category: 'photography',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop',
      highlights: ['Professional guidance', 'Perfect angles', 'Editing tips'],
      difficulty: 'Easy',
      timeOfDay: 'Morning'
    },
    {
      id: 'evening-tea-ceremony',
      title: 'Traditional Tea Ceremony',
      description: 'Experience an authentic Moroccan tea ceremony in a hidden riad courtyard',
      duration: '1 hour',
      price: 25,
      rating: 4.8,
      category: 'culture',
      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=600&h=400&fit=crop',
      highlights: ['Authentic ceremony', 'Hidden riad', 'Traditional sweets'],
      difficulty: 'Easy',
      timeOfDay: 'Evening'
    }
  ]

  const categories = [
    { id: 'all', name: 'All Experiences', icon: Star },
    { id: 'culture', name: 'Culture & History', icon: BookOpen },
    { id: 'crafts', name: 'Traditional Crafts', icon: Hammer },
    { id: 'arts', name: 'Arts & Calligraphy', icon: Palette },
    { id: 'history', name: 'Historical Sites', icon: Clock },
    { id: 'photography', name: 'Photography', icon: Camera }
  ]

  const discoverPoint = (pointId: string) => {
    if (!discoveredPoints.includes(pointId)) {
      setDiscoveredPoints([...discoveredPoints, pointId])
    }
    setActiveStory(pointId)
  }

  const nextImage = () => {
    if (activeStory) {
      const story = storyPoints.find(s => s.id === activeStory)
      if (story) {
        setCurrentImageIndex((prev) => (prev + 1) % story.images.length)
      }
    }
  }

  const prevImage = () => {
    if (activeStory) {
      const story = storyPoints.find(s => s.id === activeStory)
      if (story) {
        setCurrentImageIndex((prev) => (prev - 1 + story.images.length) % story.images.length)
      }
    }
  }

  const addToTrip = (experience: Experience) => {
    if (!selectedExperiences.find(exp => exp.id === experience.id)) {
      setSelectedExperiences([...selectedExperiences, experience])
      
      // Enhanced feedback with visual confirmation
      setShowAddedFeedback(experience.id)
      setTimeout(() => setShowAddedFeedback(null), 3000)
      
      // Update trip summary in localStorage for persistence
      const tripSummary = JSON.parse(localStorage.getItem('tripSummary') || '{}')
      const existingDestinations = tripSummary.destinations || []
      const uniqueDestinations = existingDestinations.includes('Fes') 
        ? existingDestinations 
        : [...existingDestinations, 'Fes']
      
      const updatedSummary = {
        ...tripSummary,
        destinations: uniqueDestinations,
        totalExperiences: (tripSummary.totalExperiences || 0) + 1,
        estimatedCost: (tripSummary.estimatedCost || 0) + experience.price,
        lastUpdated: new Date().toISOString()
      }
      localStorage.setItem('tripSummary', JSON.stringify(updatedSummary))
    }
    
    // Also add to global trip context
    addExperience({
      title: experience.title,
      destinationId: 'fes',
      category: experience.category,
      duration: experience.duration,
      price: experience.price,
      description: experience.description,
      imageUrl: experience.image,
      addedFrom: 'explore'
    })
  }

  const filteredExperiences = activeFilter === 'all' 
    ? experiences 
    : experiences.filter(exp => exp.category === activeFilter)

  const getTotalSelectedCost = () => {
    return selectedExperiences.reduce((total, exp) => total + exp.price, 0)
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'history': return BookOpen
      case 'craft': return Hammer
      case 'culture': return Users
      case 'hidden': return Eye
      default: return MapPin
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'history': return 'bg-primary-500'
      case 'craft': return 'bg-morocco-500'
      case 'culture': return 'bg-sand-500'
      case 'hidden': return 'bg-sage-500'
      default: return 'bg-neutral-500'
    }
  }

  const addDestinationToTrip = () => {
    addDestination({
      name: 'Fes',
      country: 'MA',
      coordinates: { lat: 34.0181, lng: -5.0078 },
      estimatedDays: 2,
      priority: 'medium',
      addedFrom: 'explore'
    })
    alert('Fes added to your trip! Continue exploring or go to Trip Builder to plan your itinerary.')
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <div className="gradient-overlay z-10" />
          <img
            src="https://images.unsplash.com/photo-1570026517541-7c6c7c6c6c6c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
            alt="Fes Medina"
            className="w-full h-full object-cover"
          />
        </div>

        <div className="relative z-20 container-custom text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <div className="flex items-center justify-center gap-2 mb-6">
              <MapPin className="w-6 h-6 text-sand-400" />
              <span className="text-sand-200 font-medium">Fes, Morocco</span>
            </div>
            
            <h1 className="heading-xl text-white mb-6">
              Imperial City
              <span className="block bg-gradient-to-r from-sand-400 to-primary-400 bg-clip-text text-transparent">
                of Living History
              </span>
            </h1>
            
            <p className="text-body-lg text-white/90 max-w-3xl mx-auto mb-12">
              Step into the world's largest car-free medieval city, where every alley tells a story 
              and ancient crafts continue unchanged for over a millennium.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <button 
                onClick={addDestinationToTrip}
                className="btn-accent btn-lg"
              >
                <Plus className="mr-3 h-5 w-5" />
                Add to My Trip
              </button>
              
              <button 
                onClick={() => setViewMode('explore')}
                className={`btn-lg ${viewMode === 'explore' ? 'btn-primary' : 'btn-secondary'}`}
              >
                <Compass className="mr-3 h-5 w-5" />
                Interactive Map
              </button>
              
              <button 
                onClick={() => setViewMode('timeline')}
                className={`btn-lg ${viewMode === 'timeline' ? 'btn-primary' : 'btn-secondary'}`}
              >
                <Clock className="mr-3 h-5 w-5" />
                1200 Year Timeline
              </button>

              <button 
                onClick={() => setViewMode('experiences')}
                className={`btn-lg ${viewMode === 'experiences' ? 'btn-primary' : 'btn-secondary'}`}
              >
                <Star className="mr-3 h-5 w-5" />
                Book Experiences
              </button>
            </div>
          </motion.div>
        </div>

        {/* Discovery Progress */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 1.5, duration: 0.8 }}
          className="absolute top-1/2 left-8 transform -translate-y-1/2 bg-white/90 backdrop-blur-md rounded-2xl p-4 shadow-lg"
        >
          <div className="flex items-center gap-3 mb-3">
            <Award className="w-5 h-5 text-primary-500" />
            <span className="font-medium text-neutral-900">Discovery Progress</span>
          </div>
          <div className="text-2xl font-bold text-primary-600 mb-1">
            {discoveredPoints.length}/{storyPoints.length}
          </div>
          <div className="text-xs text-neutral-600">Stories Unlocked</div>
        </motion.div>
      </section>

      {/* Main Content */}
      <section className="section-padding">
        <div className="container-custom">
          {viewMode === 'explore' && (
            <div className="grid lg:grid-cols-3 gap-8">
              {/* Interactive Map */}
              <div className="lg:col-span-2">
                <div className="card-elevated">
                  <div className="relative aspect-[4/3] bg-gradient-to-br from-sand-50 to-primary-50 rounded-2xl overflow-hidden">
                    {/* Map Background */}
                    <img
                      src="https://images.unsplash.com/photo-1570026517541-7c6c7c6c6c6c?w=800&h=600&fit=crop&crop=center"
                      alt="Fes Medina Map"
                      className="w-full h-full object-cover opacity-30"
                    />
                    
                    {/* Story Points */}
                    {storyPoints.map((point) => {
                      const Icon = getCategoryIcon(point.category)
                      const isDiscovered = discoveredPoints.includes(point.id)
                      const isActive = activeStory === point.id
                      
                      return (
                        <motion.button
                          key={point.id}
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 0.5 }}
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.95 }}
                          onClick={() => discoverPoint(point.id)}
                          className={`absolute w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
                            isActive 
                              ? 'bg-primary-500 text-white ring-4 ring-primary-200' 
                              : isDiscovered
                              ? `${getCategoryColor(point.category)} text-white`
                              : 'bg-white/80 text-neutral-600 hover:bg-white'
                          }`}
                          style={{
                            left: `${point.coordinates.x}%`,
                            top: `${point.coordinates.y}%`,
                            transform: 'translate(-50%, -50%)'
                          }}
                        >
                          <Icon className="w-5 h-5" />
                          
                          {!isDiscovered && (
                            <motion.div
                              animate={{ scale: [1, 1.2, 1] }}
                              transition={{ duration: 2, repeat: Infinity }}
                              className="absolute inset-0 rounded-full bg-primary-400 opacity-30"
                            />
                          )}
                        </motion.button>
                      )
                    })}
                    
                    {/* Navigation Compass */}
                    <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-md rounded-full p-3">
                      <Navigation className="w-6 h-6 text-primary-600" />
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <h3 className="heading-md text-neutral-900 mb-4">Discover Hidden Stories</h3>
                    <p className="text-body text-neutral-600 mb-6">
                      Click on the points to unlock stories, experiences, and hidden secrets of Fes. 
                      Each discovery reveals deeper layers of this ancient city.
                    </p>
                    
                    <div className="flex flex-wrap gap-3">
                      {['history', 'craft', 'culture', 'hidden'].map((category) => {
                        const Icon = getCategoryIcon(category)
                        const count = storyPoints.filter(p => p.category === category).length
                        const discovered = discoveredPoints.filter(id => 
                          storyPoints.find(p => p.id === id)?.category === category
                        ).length
                        
                        return (
                          <div key={category} className="flex items-center gap-2 bg-neutral-100 px-3 py-2 rounded-full">
                            <div className={`w-4 h-4 rounded-full ${getCategoryColor(category)}`} />
                            <Icon className="w-4 h-4 text-neutral-600" />
                            <span className="text-sm font-medium text-neutral-700 capitalize">
                              {category} ({discovered}/{count})
                            </span>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>
              </div>

              {/* Story Details */}
              <div className="lg:col-span-1">
                <div className="sticky top-24">
                  <AnimatePresence mode="wait">
                    {activeStory ? (
                      <motion.div
                        key={activeStory}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="card-elevated"
                      >
                        {(() => {
                          const story = storyPoints.find(s => s.id === activeStory)!
                          return (
                            <>
                              {/* Image Gallery */}
                              <div className="relative aspect-[4/3] rounded-2xl overflow-hidden mb-6">
                                <img
                                  src={story.images[currentImageIndex]}
                                  alt={story.title}
                                  className="w-full h-full object-cover"
                                />
                                
                                {story.images.length > 1 && (
                                  <>
                                    <button
                                      onClick={prevImage}
                                      className="absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white"
                                    >
                                      <ChevronLeft className="w-4 h-4" />
                                    </button>
                                    <button
                                      onClick={nextImage}
                                      className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/50 hover:bg-black/70 rounded-full flex items-center justify-center text-white"
                                    >
                                      <ChevronRight className="w-4 h-4" />
                                    </button>
                                    
                                    <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex gap-1">
                                      {story.images.map((_, index) => (
                                        <div
                                          key={index}
                                          className={`w-2 h-2 rounded-full ${
                                            index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                                          }`}
                                        />
                                      ))}
                                    </div>
                                  </>
                                )}
                              </div>
                              
                              <div className="space-y-6">
                                <div>
                                  <h3 className="heading-md text-neutral-900 mb-3">{story.title}</h3>
                                  <p className="text-body text-neutral-600 mb-4">{story.longDescription}</p>
                                </div>
                                
                                <div>
                                  <h4 className="font-semibold text-neutral-900 mb-3">Did You Know?</h4>
                                  <ul className="space-y-2">
                                    {story.facts.map((fact, index) => (
                                      <li key={index} className="flex items-start gap-2 text-sm text-neutral-600">
                                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mt-2 flex-shrink-0" />
                                        {fact}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                                
                                <div>
                                  <h4 className="font-semibold text-neutral-900 mb-3">Available Experiences</h4>
                                  <div className="space-y-3">
                                    {story.experiences.map((experience, index) => (
                                      <div key={index} className="p-3 bg-neutral-50 rounded-lg">
                                        <div className="flex justify-between items-start mb-2">
                                          <h5 className="font-medium text-neutral-900 text-sm">{experience.title}</h5>
                                          <div className="text-right">
                                            <div className="text-sm font-semibold text-primary-600">{experience.price}</div>
                                            <div className="text-xs text-neutral-500">{experience.duration}</div>
                                          </div>
                                        </div>
                                        <p className="text-xs text-neutral-600">{experience.description}</p>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </>
                          )
                        })()}
                      </motion.div>
                    ) : (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="card-elevated text-center py-12"
                      >
                        <Compass className="w-16 h-16 text-neutral-300 mx-auto mb-4" />
                        <h3 className="heading-md text-neutral-900 mb-3">Start Exploring</h3>
                        <p className="text-body text-neutral-600">
                          Click on any point on the map to discover the hidden stories of Fes
                        </p>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </div>
          )}

          {viewMode === 'timeline' && (
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="heading-lg text-neutral-900 mb-4">1200 Years of History</h2>
                <p className="text-body-lg text-neutral-600">
                  Journey through the key moments that shaped Fes into the cultural capital it is today
                </p>
              </div>
              
              <div className="relative">
                {/* Timeline Line */}
                <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-primary-500 to-sand-500" />
                
                <div className="space-y-12">
                  {timeline.map((event, index) => (
                    <motion.div
                      key={event.year}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="relative flex items-start gap-8"
                    >
                      {/* Timeline Dot */}
                      <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg">
                        {event.year.split(' ')[0]}
                      </div>
                      
                      {/* Content */}
                      <div className="flex-1 card-feature">
                        <h3 className="heading-md text-neutral-900 mb-2">{event.title}</h3>
                        <p className="text-body text-neutral-600 mb-3">{event.description}</p>
                        <div className="bg-primary-50 p-4 rounded-lg">
                          <h4 className="font-semibold text-primary-900 mb-2">Historical Significance</h4>
                          <p className="text-sm text-primary-700">{event.significance}</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {viewMode === 'experiences' && (
            <div className="max-w-7xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="heading-lg text-neutral-900 mb-4">
                  Authentic Fes Experiences
                </h2>
                <p className="text-body-lg text-neutral-600 max-w-3xl mx-auto">
                  Immerse yourself in the living heritage of Morocco's cultural capital with our carefully curated experiences
                </p>
              </div>

              {/* Category Filter */}
              <div className="flex flex-wrap justify-center gap-4 mb-12">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveFilter(category.id)}
                    className={`flex items-center gap-2 px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                      activeFilter === category.id
                        ? 'bg-primary-600 text-white shadow-lg'
                        : 'bg-white text-neutral-600 hover:bg-primary-50 hover:text-primary-600'
                    }`}
                  >
                    <category.icon className="w-4 h-4" />
                    {category.name}
                  </button>
                ))}
              </div>

              {/* Selected Experiences Summary */}
              {selectedExperiences.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-primary-600 text-white rounded-2xl p-6 mb-8"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-semibold mb-1">
                        {selectedExperiences.length} Experience{selectedExperiences.length !== 1 ? 's' : ''} Selected
                      </h3>
                      <p className="text-primary-100">
                        Total: €{getTotalSelectedCost()} • Ready to add to your trip
                      </p>
                    </div>
                    <button
                      onClick={() => window.location.href = '/trip-builder'}
                      className="bg-white text-primary-600 px-6 py-3 rounded-xl font-medium hover:bg-primary-50 transition-colors"
                    >
                      View Trip Builder
                    </button>
                  </div>
                </motion.div>
              )}

              {/* Experiences Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredExperiences.map((experience, index) => {
                  const activityData = {
                    id: experience.id,
                    title: experience.title,
                    description: experience.description,
                    shortDescription: experience.description,
                    imageUrl: experience.image,
                    category: experience.category as 'cultural' | 'adventure' | 'culinary' | 'relaxation' | 'historical',
                    duration: experience.duration,
                    location: 'Fes, Morocco',
                    price: experience.price,
                    rating: experience.rating,
                    reviewCount: Math.floor(Math.random() * 100) + 20,
                    difficulty: experience.difficulty.toLowerCase() as 'easy' | 'moderate' | 'challenging',
                    groupSize: '2-8 people',
                    highlights: experience.highlights,
                    includes: ['Professional guide', 'Cultural insights', 'Photo opportunities'],
                    culturalSignificance: 'Experience authentic Moroccan traditions in the spiritual capital of Morocco',
                    bestTimeToVisit: 'Year-round, best in spring and fall',
                    languages: ['English', 'French', 'Arabic'],
                    accessibility: true,
                    instantBooking: true,
                    freeCancellation: true,
                    tags: [experience.category, experience.difficulty, experience.timeOfDay],
                    provider: {
                      name: 'Fes Cultural Tours',
                      rating: 4.8,
                      verified: true
                    }
                  }

                  return (
                    <motion.div
                      key={experience.id}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                    >
                      <EnhancedActivityCard
                        activity={activityData}
                        variant="default"
                        onBook={() => addToTrip(experience)}
                        onWishlist={() => {}}
                        onShare={() => {}}
                        showBookingButton={true}
                        showWishlistButton={false}
                        showShareButton={false}
                      />
                    </motion.div>
                  )
                })}
              </div>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  )
}