'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  MapPin, 
  Clock, 
  Star, 
  Users, 
  Plus,
  Camera,
  Waves,
  Wind,
  Fish,
  Palette,
  Music,
  Anchor
} from 'lucide-react'
import { EnhancedActivityCard } from '@/components/ui/enhanced-activity-card'
import { useTripIntegration } from '@/lib/trip-integration'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'

interface Experience {
  id: string
  title: string
  description: string
  duration: string
  price: number
  rating: number
  category: string
  image: string
  highlights: string[]
  difficulty: 'Easy' | 'Moderate' | 'Challenging'
  timeOfDay: 'Morning' | 'Afternoon' | 'Evening' | 'Full Day'
}

export default function EssaouiraPage() {
  const { addDestination, addExperience } = useTripIntegration()
  const [selectedExperiences, setSelectedExperiences] = useState<Experience[]>([])
  const [showAddedFeedback, setShowAddedFeedback] = useState<string | null>(null)
  const [activeFilter, setActiveFilter] = useState('all')

  const experiences: Experience[] = [
    {
      id: 'windsurfing-lesson',
      title: 'Windsurfing & Kitesurfing Lesson',
      description: 'Learn to windsurf or kitesurf in one of the world\'s best wind conditions with professional instructors',
      duration: '3 hours',
      price: 75,
      rating: 4.8,
      category: 'watersports',
      image: 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=600&h=400&fit=crop',
      highlights: ['Professional instruction', 'Equipment included', 'Perfect wind conditions'],
      difficulty: 'Moderate',
      timeOfDay: 'Afternoon'
    },
    {
      id: 'medina-ramparts-walk',
      title: 'UNESCO Medina & Ramparts Tour',
      description: 'Explore the UNESCO World Heritage medina with its Portuguese ramparts and ocean views',
      duration: '2.5 hours',
      price: 40,
      rating: 4.7,
      category: 'culture',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=600&h=400&fit=crop',
      highlights: ['UNESCO site', 'Ocean views', 'Historic ramparts'],
      difficulty: 'Easy',
      timeOfDay: 'Morning'
    },
    {
      id: 'fishing-boat-trip',
      title: 'Traditional Fishing Boat Experience',
      description: 'Join local fishermen on their traditional blue boats and learn about sustainable fishing practices',
      duration: '4 hours',
      price: 85,
      rating: 4.9,
      category: 'maritime',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop',
      highlights: ['Traditional boats', 'Local fishermen', 'Fresh catch'],
      difficulty: 'Easy',
      timeOfDay: 'Morning'
    },
    {
      id: 'argan-oil-cooperative',
      title: 'Argan Oil Cooperative Visit',
      description: 'Visit women\'s cooperatives to learn about argan oil production and support local communities',
      duration: '2 hours',
      price: 35,
      rating: 4.6,
      category: 'culture',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop',
      highlights: ['Women\'s cooperatives', 'Traditional methods', 'Support local community'],
      difficulty: 'Easy',
      timeOfDay: 'Afternoon'
    },
    {
      id: 'gnawa-music-experience',
      title: 'Gnawa Music & Culture Experience',
      description: 'Immerse yourself in the spiritual Gnawa music tradition with local masters and musicians',
      duration: '2.5 hours',
      price: 50,
      rating: 4.8,
      category: 'music',
      image: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=600&h=400&fit=crop',
      highlights: ['Live music', 'Cultural immersion', 'Local masters'],
      difficulty: 'Easy',
      timeOfDay: 'Evening'
    },
    {
      id: 'seafood-cooking-class',
      title: 'Fresh Seafood Cooking Class',
      description: 'Learn to prepare traditional Moroccan seafood dishes with the day\'s fresh catch',
      duration: '3.5 hours',
      price: 65,
      rating: 4.7,
      category: 'culinary',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop',
      highlights: ['Fresh seafood', 'Traditional recipes', 'Hands-on cooking'],
      difficulty: 'Easy',
      timeOfDay: 'Afternoon'
    },
    {
      id: 'artisan-workshop-tour',
      title: 'Artisan Workshop Discovery',
      description: 'Visit woodworking, metalwork, and textile workshops where traditional crafts are still practiced',
      duration: '3 hours',
      price: 45,
      rating: 4.5,
      category: 'crafts',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop',
      highlights: ['Traditional crafts', 'Artisan workshops', 'Handmade products'],
      difficulty: 'Easy',
      timeOfDay: 'Morning'
    },
    {
      id: 'sunset-camel-ride',
      title: 'Beach Sunset Camel Ride',
      description: 'Ride camels along the pristine beach as the sun sets over the Atlantic Ocean',
      duration: '2 hours',
      price: 55,
      rating: 4.9,
      category: 'leisure',
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=600&h=400&fit=crop',
      highlights: ['Beach camel ride', 'Sunset views', 'Atlantic Ocean'],
      difficulty: 'Easy',
      timeOfDay: 'Evening'
    }
  ]

  const categories = [
    { id: 'all', name: 'All Experiences', icon: Star },
    { id: 'watersports', name: 'Water Sports', icon: Waves },
    { id: 'culture', name: 'Culture & History', icon: Users },
    { id: 'maritime', name: 'Maritime', icon: Anchor },
    { id: 'music', name: 'Music & Arts', icon: Music },
    { id: 'culinary', name: 'Culinary', icon: Fish },
    { id: 'crafts', name: 'Traditional Crafts', icon: Palette },
    { id: 'leisure', name: 'Leisure', icon: Wind }
  ]

  const addToTrip = (experience: Experience) => {
    if (!selectedExperiences.find(exp => exp.id === experience.id)) {
      setSelectedExperiences([...selectedExperiences, experience])
      
      // Enhanced feedback with visual confirmation
      setShowAddedFeedback(experience.id)
      setTimeout(() => setShowAddedFeedback(null), 3000)
      
      // Update trip summary in localStorage for persistence
      const tripSummary = JSON.parse(localStorage.getItem('tripSummary') || '{}')
      const existingDestinations = tripSummary.destinations || []
      const uniqueDestinations = existingDestinations.includes('Essaouira') 
        ? existingDestinations 
        : [...existingDestinations, 'Essaouira']
      
      const updatedSummary = {
        ...tripSummary,
        destinations: uniqueDestinations,
        totalExperiences: (tripSummary.totalExperiences || 0) + 1,
        estimatedCost: (tripSummary.estimatedCost || 0) + experience.price,
        lastUpdated: new Date().toISOString()
      }
      localStorage.setItem('tripSummary', JSON.stringify(updatedSummary))
    }
    
    // Also add to global trip context
    addExperience({
      title: experience.title,
      destinationId: 'essaouira',
      category: experience.category,
      duration: experience.duration,
      price: experience.price,
      description: experience.description,
      imageUrl: experience.image,
      addedFrom: 'explore'
    })
  }

  const filteredExperiences = activeFilter === 'all' 
    ? experiences 
    : experiences.filter(exp => exp.category === activeFilter)

  const getTotalSelectedCost = () => {
    return selectedExperiences.reduce((total, exp) => total + exp.price, 0)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-cyan-50">
      <Header />
      
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/60 to-cyan-700/40 z-10"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url("https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=1920&h=1080&fit=crop")'
          }}
        ></div>
        
        <div className="relative z-20 text-center text-white max-w-4xl mx-auto px-6">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-5xl md:text-7xl font-bold mb-6"
          >
            Essaouira
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl md:text-2xl mb-8 text-cyan-100"
          >
            The Windy City of Morocco - Where Atlantic Winds Meet Ancient Walls
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <button className="bg-cyan-600 hover:bg-cyan-700 text-white px-8 py-4 rounded-xl font-semibold transition-colors">
              Explore the Coast
            </button>
            <Link href="/trip-builder" className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white px-8 py-4 rounded-xl font-semibold transition-colors">
              Plan Your Visit
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-20">
        {/* Trip Summary Widget */}
        {selectedExperiences.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="fixed top-24 right-6 z-40 bg-white rounded-2xl shadow-xl p-6 w-80 border border-neutral-200"
          >
            <h3 className="font-bold text-neutral-900 mb-4 flex items-center gap-2">
              <MapPin className="w-5 h-5 text-cyan-600" />
              Your Essaouira Trip
            </h3>
            <div className="space-y-3 mb-4">
              {selectedExperiences.slice(0, 3).map((exp) => (
                <div key={exp.id} className="flex items-center gap-3">
                  <img src={exp.image} alt={exp.title} className="w-12 h-12 rounded-lg object-cover" />
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm text-neutral-900 truncate">{exp.title}</p>
                    <p className="text-xs text-neutral-500">€{exp.price}</p>
                  </div>
                </div>
              ))}
              {selectedExperiences.length > 3 && (
                <p className="text-sm text-neutral-500">+{selectedExperiences.length - 3} more experiences</p>
              )}
            </div>
            <div className="border-t border-neutral-200 pt-4">
              <div className="flex justify-between items-center mb-4">
                <span className="font-medium text-neutral-900">Total Cost:</span>
                <span className="font-bold text-xl text-cyan-600">€{getTotalSelectedCost()}</span>
              </div>
              <Link
                href="/trip-builder"
                className="w-full bg-cyan-600 hover:bg-cyan-700 text-white py-3 px-4 rounded-xl font-medium transition-colors text-center block"
              >
                Continue Planning
              </Link>
            </div>
          </motion.div>
        )}

        {/* Experiences Section */}
        <section>
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-neutral-900 mb-4">
              Discover the Windy City
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              From world-class windsurfing to traditional fishing experiences, explore Morocco's most charming coastal city where Portuguese history meets Berber culture
            </p>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveFilter(category.id)}
                className={`flex items-center gap-2 px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  activeFilter === category.id
                    ? 'bg-cyan-600 text-white shadow-lg'
                    : 'bg-white text-neutral-600 hover:bg-cyan-50 hover:text-cyan-600'
                }`}
              >
                <category.icon className="w-4 h-4" />
                {category.name}
              </button>
            ))}
          </div>

          {/* Selected Experiences Summary */}
          {selectedExperiences.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-cyan-600 text-white rounded-2xl p-6 mb-8"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold mb-1">
                    {selectedExperiences.length} Experience{selectedExperiences.length !== 1 ? 's' : ''} Selected
                  </h3>
                  <p className="text-cyan-100">
                    Total: €{getTotalSelectedCost()} • Ready to add to your trip
                  </p>
                </div>
                <Link
                  href="/trip-builder"
                  className="bg-white text-cyan-600 px-6 py-3 rounded-xl font-medium hover:bg-cyan-50 transition-colors"
                >
                  View Trip Builder
                </Link>
              </div>
            </motion.div>
          )}

          {/* Experiences Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredExperiences.map((experience, index) => {
              const activityData = {
                id: experience.id,
                title: experience.title,
                description: experience.description,
                shortDescription: experience.description,
                imageUrl: experience.image,
                category: experience.category as 'cultural' | 'adventure' | 'culinary' | 'relaxation' | 'historical',
                duration: experience.duration,
                location: 'Essaouira, Morocco',
                price: experience.price,
                rating: experience.rating,
                reviewCount: Math.floor(Math.random() * 100) + 20,
                difficulty: experience.difficulty.toLowerCase() as 'easy' | 'moderate' | 'challenging',
                groupSize: '2-8 people',
                highlights: experience.highlights,
                includes: ['Professional guide', 'Cultural insights', 'Photo opportunities'],
                culturalSignificance: 'Experience the unique blend of Berber, Arab, and Portuguese cultures in this coastal gem',
                bestTimeToVisit: 'Year-round, especially great for windsurfing in summer',
                languages: ['English', 'French', 'Arabic'],
                accessibility: true,
                instantBooking: true,
                freeCancellation: true,
                tags: [experience.category, experience.difficulty, experience.timeOfDay],
                provider: {
                  name: 'Essaouira Coastal Tours',
                  rating: 4.7,
                  verified: true
                }
              }

              return (
                <motion.div
                  key={experience.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <EnhancedActivityCard
                    activity={activityData}
                    variant="default"
                    onBook={() => addToTrip(experience)}
                    onWishlist={() => {}}
                    onShare={() => {}}
                    showBookingButton={true}
                    showWishlistButton={false}
                    showShareButton={false}
                  />
                </motion.div>
              )
            })}
          </div>
        </section>
      </div>

      <Footer />
    </div>
  )
}