'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { Head<PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { DestinationExplorationTracker } from '@/components/navigation/destination-exploration-tracker'
import { useTripIntegration } from '@/lib/trip-integration'
import { CulturalInsights } from '@/components/cultural-immersion/cultural-insights'
import { CulturalIntegration } from '@/components/cultural-immersion/cultural-integration'
import { EnhancedActivityCard } from '@/components/ui/enhanced-activity-card'
import { EnhancedTabNavigation } from '@/components/ui/enhanced-tab-navigation'
import { ProgressiveDisclosure, FAQDisclosure } from '@/components/ui/progressive-disclosure'
import {
  MapPin,
  Clock,
  Users,
  Star,
  Heart,
  Camera,
  Palette,
  Utensils,
  Building,
  Sun,
  Moon,
  Calendar,
  Plus,
  ArrowRight,
  Play,
  Volume2,
  Thermometer,
  Wind,
  X,
  Info,
  HelpCircle,
  Map
} from 'lucide-react'

interface Experience {
  id: string
  title: string
  description: string
  shortDescription: string
  imageUrl: string
  category: 'cultural' | 'adventure' | 'culinary' | 'relaxation' | 'historical'
  duration: string
  location: string
  price: number
  originalPrice?: number
  rating: number
  reviewCount: number
  difficulty: 'easy' | 'moderate' | 'challenging'
  groupSize: string
  highlights: string[]
  includes: string[]
  culturalSignificance?: string
  bestTimeToVisit?: string
  languages: string[]
  accessibility: boolean
  instantBooking: boolean
  freeCancellation: boolean
  tags: string[]
  provider: {
    name: string
    rating: number
    verified: boolean
  }
}

interface TripPlan {
  experiences: Experience[]
  totalDuration: number
  estimatedCost: number
}

export default function MarrakechPage() {
  const { addDestination, addExperience } = useTripIntegration()
  const [selectedExperiences, setSelectedExperiences] = useState<Experience[]>([])
  const [activeFilter, setActiveFilter] = useState('all')
  const [viewMode, setViewMode] = useState<'explore' | 'plan'>('explore')
  const [currentAudio, setCurrentAudio] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('experiences')
  const [showAddedFeedback, setShowAddedFeedback] = useState<string | null>(null)

  const experiences: Experience[] = [
    {
      id: 'medina-walk',
      title: 'Medina Storytelling Walk',
      description: 'Journey through 1000 years of history with a local storyteller who brings the ancient walls to life. Discover hidden courtyards, ancient stories, and local legends as you navigate the labyrinthine streets of the medina.',
      shortDescription: 'Journey through 1000 years of history with a local storyteller',
      imageUrl: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=600&h=400&fit=crop',
      category: 'cultural',
      duration: '3 hours',
      location: 'Medina, Marrakech',
      price: 45,
      rating: 4.9,
      reviewCount: 127,
      difficulty: 'easy',
      groupSize: 'Up to 8 people',
      highlights: ['Hidden courtyards', 'Ancient stories', 'Local legends'],
      includes: ['Professional guide', 'Traditional tea', 'Historical insights'],
      culturalSignificance: 'The medina of Marrakech is a UNESCO World Heritage site, representing over 1000 years of Moroccan history and Islamic architecture.',
      bestTimeToVisit: 'Morning (9-12 AM)',
      languages: ['English', 'French', 'Arabic'],
      accessibility: true,
      instantBooking: true,
      freeCancellation: true,
      tags: ['History', 'Culture', 'Walking', 'UNESCO'],
      provider: {
        name: 'Marrakech Heritage Tours',
        rating: 4.8,
        verified: true
      }
    },
    {
      id: 'cooking-family',
      title: 'Family Kitchen Experience',
      description: 'Cook with a Berber family in their home, learning recipes passed down through generations. Start with a market visit to select fresh ingredients, then prepare traditional dishes in an authentic family setting.',
      shortDescription: 'Cook with a Berber family learning traditional recipes',
      imageUrl: 'https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=600&h=400&fit=crop',
      category: 'culinary',
      duration: '4 hours',
      location: 'Traditional Family Home',
      price: 75,
      originalPrice: 95,
      rating: 5.0,
      reviewCount: 89,
      difficulty: 'easy',
      groupSize: 'Up to 6 people',
      highlights: ['Family recipes', 'Market shopping', 'Home dining'],
      includes: ['Market tour', 'Cooking lesson', 'Full meal', 'Recipe cards'],
      culturalSignificance: 'Experience authentic Berber hospitality and learn cooking techniques that have been preserved for centuries within Moroccan families.',
      bestTimeToVisit: 'Afternoon (2-6 PM)',
      languages: ['English', 'French', 'Berber'],
      accessibility: false,
      instantBooking: true,
      freeCancellation: true,
      tags: ['Cooking', 'Family', 'Authentic', 'Berber'],
      provider: {
        name: 'Berber Family Experiences',
        rating: 4.9,
        verified: true
      }
    },
    {
      id: 'artisan-workshop',
      title: 'Master Craftsman Workshop',
      description: 'Work alongside master artisans creating traditional Moroccan crafts using ancient techniques. Learn the art of metalwork, pottery, or leather crafting in authentic workshops.',
      shortDescription: 'Learn traditional crafts from master artisans',
      imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop',
      category: 'cultural',
      duration: '2 hours',
      location: 'Artisan Quarter, Medina',
      price: 55,
      rating: 4.8,
      reviewCount: 156,
      difficulty: 'moderate',
      groupSize: 'Up to 4 people',
      highlights: ['Hands-on creation', 'Take home art', 'Master techniques'],
      includes: ['Materials', 'Tools', 'Expert instruction', 'Finished piece'],
      culturalSignificance: 'These traditional crafts represent centuries of Moroccan artisanal heritage, with techniques passed down through generations of master craftsmen.',
      bestTimeToVisit: 'Morning or Afternoon',
      languages: ['English', 'French', 'Arabic'],
      accessibility: true,
      instantBooking: false,
      freeCancellation: true,
      tags: ['Crafts', 'Artisan', 'Hands-on', 'Traditional'],
      provider: {
        name: 'Marrakech Artisan Collective',
        rating: 4.7,
        verified: true
      }
    },
    {
      id: 'rooftop-sunset',
      title: 'Secret Rooftop Sunset',
      description: 'Watch the sun set over the Atlas Mountains from a hidden rooftop with traditional music. Enjoy panoramic views of the medina while sipping mint tea and listening to local musicians.',
      shortDescription: 'Sunset views over Atlas Mountains with traditional music',
      imageUrl: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=600&h=400&fit=crop',
      category: 'relaxation',
      duration: '2 hours',
      location: 'Hidden Rooftop Terrace',
      price: 35,
      rating: 4.9,
      reviewCount: 203,
      difficulty: 'easy',
      groupSize: 'Up to 12 people',
      highlights: ['Atlas views', 'Live music', 'Mint tea'],
      includes: ['Rooftop access', 'Traditional tea', 'Live music', 'Light snacks'],
      culturalSignificance: 'Rooftop terraces are integral to Moroccan architecture and social life, offering spaces for relaxation and community gathering.',
      bestTimeToVisit: 'Evening (5-7 PM)',
      languages: ['English', 'French'],
      accessibility: false,
      instantBooking: true,
      freeCancellation: true,
      tags: ['Sunset', 'Views', 'Music', 'Relaxation'],
      provider: {
        name: 'Marrakech Rooftop Experiences',
        rating: 4.8,
        verified: true
      }
    },
    {
      id: 'hammam-ritual',
      title: 'Traditional Hammam Ritual',
      description: 'Experience the ancient cleansing ritual in a 500-year-old hammam with traditional treatments. Enjoy the full traditional experience with natural products and expert attendants.',
      shortDescription: 'Ancient cleansing ritual in historic hammam',
      imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop',
      category: 'relaxation',
      duration: '2.5 hours',
      location: 'Historic Hammam',
      price: 65,
      rating: 4.7,
      reviewCount: 98,
      difficulty: 'easy',
      groupSize: 'Individual or couples',
      highlights: ['Ancient ritual', 'Natural products', 'Deep relaxation'],
      includes: ['Hammam access', 'Traditional scrub', 'Argan oil massage', 'Mint tea'],
      culturalSignificance: 'The hammam is a cornerstone of Moroccan wellness culture, representing centuries of bathing traditions and social customs.',
      bestTimeToVisit: 'Afternoon (2-5 PM)',
      languages: ['English', 'French', 'Arabic'],
      accessibility: true,
      instantBooking: false,
      freeCancellation: false,
      tags: ['Wellness', 'Traditional', 'Relaxation', 'Spa'],
      provider: {
        name: 'Heritage Hammam Marrakech',
        rating: 4.6,
        verified: true
      }
    },
    {
      id: 'night-food-tour',
      title: 'Night Market Food Adventure',
      description: 'Navigate the evening food scene like a local, discovering hidden gems and street food secrets. Explore Jemaa el-Fnaa and surrounding streets for an authentic culinary journey.',
      shortDescription: 'Discover street food secrets with a local guide',
      imageUrl: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=600&h=400&fit=crop',
      category: 'culinary',
      duration: '3 hours',
      location: 'Jemaa el-Fnaa & Medina',
      price: 50,
      rating: 4.8,
      reviewCount: 174,
      difficulty: 'easy',
      groupSize: 'Up to 8 people',
      highlights: ['Street food', 'Local spots', 'Night atmosphere'],
      includes: ['Food tastings', 'Local guide', 'Market insights', 'Cultural stories'],
      culturalSignificance: 'Jemaa el-Fnaa is the heart of Marrakech social life, where food culture and community traditions come alive every evening.',
      bestTimeToVisit: 'Evening (7-10 PM)',
      languages: ['English', 'French', 'Arabic'],
      accessibility: true,
      instantBooking: true,
      freeCancellation: true,
      tags: ['Food', 'Night', 'Street food', 'Local'],
      provider: {
        name: 'Marrakech Food Explorers',
        rating: 4.9,
        verified: true
      }
    }
  ]

  const categories = [
    { id: 'all', name: 'All Experiences', icon: Star },
    { id: 'cultural', name: 'Culture & History', icon: Building },
    { id: 'culinary', name: 'Culinary', icon: Utensils },
    { id: 'adventure', name: 'Adventure', icon: Camera },
    { id: 'relaxation', name: 'Wellness & Relaxation', icon: Heart },
    { id: 'historical', name: 'Historical', icon: Palette }
  ]

  // Tab configuration for enhanced navigation
  const tabs = [
    { id: 'experiences', label: 'Experiences', icon: Star, badge: experiences.length.toString() },
    { id: 'cultural-guide', label: 'Cultural Guide', icon: Info },
    { id: 'map', label: 'Map & Areas', icon: Map },
    { id: 'planning', label: 'Trip Planning', icon: Calendar }
  ]

  // Cultural content for progressive disclosure
  const culturalFAQs = [
    {
      question: "What should I wear when visiting religious sites?",
      answer: "Dress modestly when visiting mosques and religious sites. Cover your shoulders, arms, and legs. Women should bring a headscarf. Remove shoes before entering prayer areas."
    },
    {
      question: "Is it appropriate to photograph people?",
      answer: "Always ask permission before photographing people, especially in traditional areas. Some may expect a small tip. Avoid photographing people during prayer or in private moments."
    },
    {
      question: "How should I behave in the souks?",
      answer: "Bargaining is expected and part of the culture. Start at about 30% of the asking price. Be respectful but firm. Don't feel pressured to buy - saying 'La, shukran' (No, thank you) is perfectly acceptable."
    },
    {
      question: "What are appropriate greetings?",
      answer: "Use 'As-salamu alaykum' (Peace be upon you) or 'Ahlan wa sahlan' (Welcome). A handshake is common between same genders. Wait for women to extend their hand first."
    },
    {
      question: "Are there dining etiquette rules?",
      answer: "Eat with your right hand only. When sharing from communal dishes, take from the section closest to you. It's polite to leave a little food on your plate to show you're satisfied."
    }
  ]

  const culturalInsights = [
    {
      title: "The Art of Moroccan Hospitality",
      content: "Moroccans take great pride in welcoming guests. If invited to someone's home, it's customary to bring a small gift like pastries or tea. Remove your shoes when entering homes and always accept offered mint tea - it's a sign of respect.",
      icon: Heart
    },
    {
      title: "Understanding Prayer Times",
      content: "Morocco follows Islamic prayer times (Fajr, Dhuhr, Asr, Maghrib, Isha). During these times, you may hear the call to prayer and notice shops briefly closing. This is normal and part of daily life.",
      icon: Clock
    },
    {
      title: "Ramadan Considerations",
      content: "If visiting during Ramadan, be respectful of those fasting. Avoid eating, drinking, or smoking in public during daylight hours. Many restaurants will be closed during the day but come alive after sunset.",
      icon: Moon
    }
  ]

  const filteredExperiences = activeFilter === 'all'
    ? experiences
    : experiences.filter(exp => exp.category === activeFilter)

  const addToTrip = (experience: Experience) => {
    if (!selectedExperiences.find(exp => exp.id === experience.id)) {
      setSelectedExperiences([...selectedExperiences, experience])
      
      // Enhanced feedback with visual confirmation
      setShowAddedFeedback(experience.id)
      setTimeout(() => setShowAddedFeedback(null), 3000)
      
      // Update trip summary in localStorage for persistence
      const tripSummary = JSON.parse(localStorage.getItem('tripSummary') || '{}')
      const existingDestinations = tripSummary.destinations || []
      const uniqueDestinations = existingDestinations.includes('Marrakech') 
        ? existingDestinations 
        : [...existingDestinations, 'Marrakech']
      
      const updatedSummary = {
        ...tripSummary,
        destinations: uniqueDestinations,
        totalExperiences: (tripSummary.totalExperiences || 0) + 1,
        estimatedCost: (tripSummary.estimatedCost || 0) + experience.price,
        lastUpdated: new Date().toISOString()
      }
      localStorage.setItem('tripSummary', JSON.stringify(updatedSummary))
    }
    
    // Also add to global trip context
    addExperience({
      title: experience.title,
      destinationId: 'marrakech',
      category: experience.category,
      duration: experience.duration,
      price: experience.price,
      description: experience.description,
      imageUrl: experience.imageUrl,
      addedFrom: 'explore'
    })
  }

  const addDestinationToTrip = () => {
    addDestination({
      name: 'Marrakech',
      country: 'MA',
      coordinates: { lat: 31.6295, lng: -7.9811 },
      estimatedDays: 3,
      priority: 'high',
      addedFrom: 'explore'
    })
    alert('Marrakech added to your trip! Continue exploring or go to Trip Builder to plan your itinerary.')
  }

  const removeFromTrip = (experienceId: string) => {
    setSelectedExperiences(selectedExperiences.filter(exp => exp.id !== experienceId))
  }

  const totalCost = selectedExperiences.reduce((sum, exp) => sum + exp.price, 0)
  const totalDuration = selectedExperiences.reduce((sum, exp) => sum + parseInt(exp.duration), 0)

  return (
    <div className="min-h-screen bg-white">
      <DestinationExplorationTracker destinationName="Marrakech" />
      <Header />
      
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <div className="gradient-overlay z-10" />
          <img
            src="https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
            alt="Marrakech"
            className="w-full h-full object-cover"
          />
        </div>

        {/* Floating Info Cards */}
        <div className="absolute inset-0 z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 0.8 }}
            className="absolute top-1/4 left-8 bg-white/90 backdrop-blur-md rounded-2xl p-4 shadow-lg"
          >
            <div className="flex items-center gap-3">
              <Thermometer className="w-5 h-5 text-morocco-500" />
              <div>
                <p className="text-sm font-medium text-neutral-900">Perfect Weather</p>
                <p className="text-xs text-neutral-600">24°C • Sunny</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2, duration: 0.8 }}
            className="absolute top-1/3 right-8 bg-white/90 backdrop-blur-md rounded-2xl p-4 shadow-lg"
          >
            <div className="flex items-center gap-3">
              <Users className="w-5 h-5 text-primary-500" />
              <div>
                <p className="text-sm font-medium text-neutral-900">Live Now</p>
                <p className="text-xs text-neutral-600">247 travelers exploring</p>
              </div>
            </div>
          </motion.div>
        </div>

        <div className="relative z-20 container-custom text-center text-white">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
          >
            <div className="flex items-center justify-center gap-2 mb-6">
              <MapPin className="w-6 h-6 text-sand-400" />
              <span className="text-sand-200 font-medium">Marrakech, Morocco</span>
            </div>
            
            <h1 className="heading-xl text-white mb-6">
              The Red City
              <span className="block bg-gradient-to-r from-sand-400 to-morocco-400 bg-clip-text text-transparent">
                Awakens Your Soul
              </span>
            </h1>
            
            <p className="text-body-lg text-white/90 max-w-3xl mx-auto mb-12">
              Where ancient palaces whisper stories, bustling souks ignite your senses, 
              and every corner holds a thousand-year-old secret waiting to be discovered.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <button 
                onClick={addDestinationToTrip}
                className="btn-accent btn-lg"
              >
                <Plus className="mr-3 h-5 w-5" />
                Add to My Trip
              </button>
              
              <button 
                onClick={() => setViewMode('explore')}
                className={`btn-lg ${viewMode === 'explore' ? 'btn-primary' : 'btn-secondary'}`}
              >
                <Camera className="mr-3 h-5 w-5" />
                Explore Experiences
              </button>
              
              <button 
                onClick={() => setViewMode('plan')}
                className={`btn-lg ${viewMode === 'plan' ? 'btn-primary' : 'btn-secondary'}`}
              >
                <Calendar className="mr-3 h-5 w-5" />
                Build Your Journey
              </button>
            </div>
          </motion.div>
        </div>

        {/* Audio Experience */}
        <motion.button
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2, duration: 0.5 }}
          className="absolute bottom-8 left-8 bg-white/20 backdrop-blur-md rounded-full p-4 text-white hover:bg-white/30 transition-all duration-300"
          onClick={() => setCurrentAudio(currentAudio ? null : 'marrakech-intro')}
        >
          {currentAudio ? <Volume2 className="w-6 h-6" /> : <Play className="w-6 h-6" />}
        </motion.button>
      </section>

      {/* Main Content */}
      <section className="section-padding">
        <div className="container-custom">
          {/* Floating Trip Summary Widget */}
          {selectedExperiences.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="fixed top-24 right-3 sm:right-6 z-40 bg-white rounded-2xl shadow-xl p-4 sm:p-6 w-[calc(100vw-1.5rem)] sm:w-80 max-w-sm border border-neutral-200"
            >
              <h3 className="font-bold text-neutral-900 mb-4 flex items-center gap-2">
                <MapPin className="w-5 h-5 text-orange-600" />
                Your Marrakech Trip
              </h3>
              <div className="space-y-3 mb-4">
                {selectedExperiences.slice(0, 3).map((exp) => (
                  <div key={exp.id} className="flex items-center gap-3">
                    <img src={exp.imageUrl} alt={exp.title} className="w-12 h-12 rounded-lg object-cover" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm text-neutral-900 truncate">{exp.title}</p>
                      <p className="text-xs text-neutral-500">€{exp.price}</p>
                    </div>
                  </div>
                ))}
                {selectedExperiences.length > 3 && (
                  <p className="text-sm text-neutral-500">+{selectedExperiences.length - 3} more experiences</p>
                )}
              </div>
              <div className="border-t border-neutral-200 pt-4">
                <div className="flex justify-between items-center mb-4">
                  <span className="font-medium text-neutral-900">Total Cost:</span>
                  <span className="font-bold text-xl text-orange-600">€{totalCost}</span>
                </div>
                <Link
                  href="/trip-builder"
                  className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white py-3 px-4 rounded-xl font-medium transition-all duration-200 text-center block min-h-[44px] flex items-center justify-center"
                >
                  Continue Planning
                </Link>
              </div>
            </motion.div>
          )}

          {/* Enhanced Tab Navigation */}
          <div className="mb-8">
            <div className="text-center mb-8">
              <h2 className="heading-lg text-neutral-900 mb-4">Discover Marrakech</h2>
              <p className="text-body text-neutral-600 max-w-2xl mx-auto">
                Immerse yourself in the Red City's rich culture, vibrant souks, and timeless traditions
              </p>
            </div>

            <EnhancedTabNavigation
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={setActiveTab}
              variant="pills"
              className="justify-center"
            />
          </div>

          {/* Tab Content */}
          <div className="mt-8">
            {activeTab === 'experiences' && (
              <div>
                {/* Category Filters */}
                <div className="flex flex-wrap gap-3 mb-8 justify-center">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setActiveFilter(category.id)}
                      className={`flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-200 min-h-[44px] ${
                        activeFilter === category.id
                          ? 'bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg'
                          : 'bg-neutral-100 text-neutral-600 hover:bg-neutral-200'
                      }`}
                    >
                      <category.icon className="w-4 h-4" />
                      <span className="text-sm font-medium">{category.name}</span>
                    </button>
                  ))}
                </div>

                {/* Experiences Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                  {filteredExperiences.map((experience, index) => (
                    <motion.div
                      key={experience.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <EnhancedActivityCard
                        activity={experience}
                        variant="detailed"
                        showBookingButton={true}
                        showWishlistButton={true}
                        showShareButton={true}
                        onBook={(activity) => addToTrip(activity)}
                        onWishlist={(activity) => {
                          // Handle wishlist functionality
                          console.log('Added to wishlist:', activity.title)
                        }}
                        onShare={(activity) => {
                          // Handle share functionality
                          if (navigator.share) {
                            navigator.share({
                              title: activity.title,
                              text: activity.shortDescription,
                              url: window.location.href
                            })
                          }
                        }}
                        className="h-full"
                      />
                    </motion.div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'cultural-guide' && (
              <div className="max-w-4xl mx-auto">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 sm:gap-8 mb-12">
                  {culturalInsights.map((insight, index) => (
                    <motion.div
                      key={insight.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-100"
                    >
                      <div className="flex items-start gap-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl flex items-center justify-center flex-shrink-0">
                          <insight.icon className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-neutral-900 mb-2">{insight.title}</h3>
                          <p className="text-neutral-700 leading-relaxed">{insight.content}</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                <div className="bg-white rounded-2xl shadow-lg p-8">
                  <h3 className="text-2xl font-bold text-neutral-900 mb-6 text-center">Cultural Etiquette & Tips</h3>
                  <FAQDisclosure faqs={culturalFAQs} />
                </div>
              </div>
            )}

            {activeTab === 'map' && (
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <h3 className="text-2xl font-bold text-neutral-900 mb-6 text-center">Marrakech Areas & Map</h3>
                <div className="grid md:grid-cols-2 gap-8">
                  <div>
                    <h4 className="text-lg font-semibold mb-4">Key Areas to Explore</h4>
                    <div className="space-y-4">
                      <div className="p-4 bg-orange-50 rounded-lg border-l-4 border-orange-500">
                        <h5 className="font-medium text-orange-900">Medina (Old City)</h5>
                        <p className="text-sm text-orange-700 mt-1">Historic walled city with souks, palaces, and traditional riads</p>
                      </div>
                      <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                        <h5 className="font-medium text-blue-900">Gueliz (New City)</h5>
                        <p className="text-sm text-blue-700 mt-1">Modern district with restaurants, cafes, and shopping centers</p>
                      </div>
                      <div className="p-4 bg-green-50 rounded-lg border-l-4 border-green-500">
                        <h5 className="font-medium text-green-900">Hivernage</h5>
                        <p className="text-sm text-green-700 mt-1">Upscale area with luxury hotels and gardens</p>
                      </div>
                    </div>
                  </div>
                  <div className="bg-neutral-100 rounded-lg p-4 flex items-center justify-center min-h-[300px]">
                    <div className="text-center">
                      <Map className="w-16 h-16 text-neutral-400 mx-auto mb-4" />
                      <p className="text-neutral-600">Interactive map coming soon</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'planning' && (
              <div className="max-w-4xl mx-auto">
                <div className="bg-white rounded-2xl shadow-lg p-8">
                  <h3 className="text-2xl font-bold text-neutral-900 mb-6 text-center">Trip Planning Assistant</h3>

                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h4 className="text-lg font-semibold mb-4">Your Selected Experiences</h4>
                      {selectedExperiences.length === 0 ? (
                        <div className="text-center py-8">
                          <Calendar className="w-12 h-12 text-neutral-300 mx-auto mb-4" />
                          <p className="text-neutral-500 mb-2">Start building your journey</p>
                          <p className="text-sm text-neutral-400">Go to Experiences tab to add activities</p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          {selectedExperiences.map((experience) => (
                            <div key={experience.id} className="flex items-center gap-3 p-3 bg-neutral-50 rounded-lg">
                              <img
                                src={experience.imageUrl}
                                alt={experience.title}
                                className="w-12 h-12 rounded-lg object-cover"
                              />
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium text-neutral-900 text-sm truncate">{experience.title}</h4>
                                <p className="text-xs text-neutral-500">{experience.duration} • €{experience.price}</p>
                              </div>
                              <button
                                onClick={() => removeFromTrip(experience.id)}
                                className="w-6 h-6 rounded-full bg-neutral-200 hover:bg-red-100 flex items-center justify-center text-neutral-500 hover:text-red-500 transition-colors duration-200 min-h-[44px] min-w-[44px]"
                              >
                                <X className="w-3 h-3" />
                              </button>
                            </div>
                          ))}

                          <div className="border-t border-neutral-200 pt-4 mt-6">
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm text-neutral-600">Total Duration:</span>
                              <span className="font-medium text-neutral-900">{totalDuration} hours</span>
                            </div>
                            <div className="flex justify-between items-center mb-4">
                              <span className="text-sm text-neutral-600">Estimated Cost:</span>
                              <span className="font-bold text-primary-600">€{totalCost}</span>
                            </div>

                            <Link href="/trip-builder" className="btn-primary w-full block text-center min-h-[44px] flex items-center justify-center">
                              Continue Planning
                              <ArrowRight className="ml-2 h-4 w-4" />
                            </Link>
                          </div>
                        </div>
                      )}
                    </div>

                    <div>
                      <h4 className="text-lg font-semibold mb-4">Planning Tips</h4>
                      <div className="space-y-3">
                        <div className="p-3 bg-orange-50 rounded-lg border-l-4 border-orange-500">
                          <p className="text-sm text-orange-800">
                            <strong>Best Time:</strong> Visit October-April for pleasant weather
                          </p>
                        </div>
                        <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                          <p className="text-sm text-blue-800">
                            <strong>Duration:</strong> Allow 3-4 days to explore Marrakech properly
                          </p>
                        </div>
                        <div className="p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                          <p className="text-sm text-green-800">
                            <strong>Budget:</strong> €50-150 per day depending on accommodation
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}