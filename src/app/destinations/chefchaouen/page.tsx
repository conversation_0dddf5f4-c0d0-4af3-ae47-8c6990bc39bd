'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  MapPin, 
  Clock, 
  Star, 
  Users, 
  Plus,
  Camera,
  Mountain,
  Palette,
  Coffee,
  ShoppingBag,
  TreePine,
  Sunrise
} from 'lucide-react'
import { useTripIntegration } from '@/lib/trip-integration'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { EnhancedActivityCard } from '@/components/ui/enhanced-activity-card'

interface Experience {
  id: string
  title: string
  description: string
  shortDescription: string
  imageUrl: string
  category: 'cultural' | 'adventure' | 'culinary' | 'relaxation' | 'historical'
  duration: string
  location: string
  price: number
  originalPrice?: number
  rating: number
  reviewCount: number
  difficulty: 'easy' | 'moderate' | 'challenging'
  groupSize: string
  highlights: string[]
  includes: string[]
  culturalSignificance?: string
  bestTimeToVisit?: string
  languages: string[]
  accessibility: boolean
  instantBooking: boolean
  freeCancellation: boolean
  tags: string[]
  provider: {
    name: string
    rating: number
    verified: boolean
  }
}

export default function ChefchaouenPage() {
  const { addDestination, addExperience } = useTripIntegration()
  const [selectedExperiences, setSelectedExperiences] = useState<Experience[]>([])
  const [showAddedFeedback, setShowAddedFeedback] = useState<string | null>(null)
  const [activeFilter, setActiveFilter] = useState('all')

  const experiences: Experience[] = [
    {
      id: 'blue-city-photography',
      title: 'Blue City Photography Tour',
      description: 'Capture the most Instagram-worthy shots of the famous blue-painted streets with a professional guide. Learn composition techniques and discover hidden photogenic corners that only locals know about.',
      shortDescription: 'Capture Instagram-worthy shots of blue-painted streets',
      imageUrl: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=600&h=400&fit=crop',
      category: 'cultural',
      duration: '3 hours',
      location: 'Chefchaouen Medina',
      price: 45,
      rating: 4.9,
      reviewCount: 156,
      difficulty: 'easy',
      groupSize: 'Up to 6 people',
      highlights: ['Professional guidance', 'Best photo spots', 'Golden hour timing'],
      includes: ['Professional photographer guide', 'Photo editing tips', 'Digital photo package'],
      culturalSignificance: 'The blue walls of Chefchaouen represent a unique cultural tradition, with each shade telling a story of the city\'s history.',
      bestTimeToVisit: 'Morning (8-11 AM)',
      languages: ['English', 'French', 'Arabic'],
      accessibility: true,
      instantBooking: true,
      freeCancellation: true,
      tags: ['Photography', 'Instagram', 'Blue City', 'Culture'],
      provider: {
        name: 'Chefchaouen Photo Tours',
        rating: 4.8,
        verified: true
      }
    },
    {
      id: 'medina-walking-tour',
      title: 'Traditional Medina Walking Tour',
      description: 'Explore the narrow blue alleys, local markets, and hidden corners of this enchanting mountain town. Discover the history behind the blue walls and meet local artisans.',
      shortDescription: 'Explore blue alleys and local markets with a guide',
      imageUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop',
      category: 'cultural',
      duration: '2.5 hours',
      location: 'Chefchaouen Medina',
      price: 35,
      rating: 4.8,
      reviewCount: 89,
      difficulty: 'easy',
      groupSize: 'Up to 10 people',
      highlights: ['Local guide', 'Hidden spots', 'Cultural insights'],
      includes: ['Local guide', 'Market visit', 'Traditional tea'],
      culturalSignificance: 'The medina represents the heart of Chefchaouen\'s Andalusian heritage and Berber traditions.',
      bestTimeToVisit: 'Afternoon (2-5 PM)',
      languages: ['English', 'French', 'Arabic', 'Berber'],
      accessibility: true,
      instantBooking: true,
      freeCancellation: true,
      tags: ['Walking', 'Culture', 'Medina', 'Local'],
      provider: {
        name: 'Blue City Guides',
        rating: 4.7,
        verified: true
      }
    },
    {
      id: 'rif-mountains-hiking',
      title: 'Rif Mountains Hiking Adventure',
      description: 'Trek through the beautiful Rif Mountains with panoramic views of the blue city below. Experience diverse flora and fauna while enjoying breathtaking mountain scenery.',
      shortDescription: 'Trek through Rif Mountains with panoramic views',
      imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop',
      category: 'adventure',
      duration: '5 hours',
      location: 'Rif Mountains',
      price: 65,
      rating: 4.7,
      reviewCount: 134,
      difficulty: 'moderate',
      groupSize: 'Up to 8 people',
      highlights: ['Mountain views', 'Nature trails', 'Panoramic vistas'],
      includes: ['Mountain guide', 'Hiking equipment', 'Lunch', 'Transportation'],
      culturalSignificance: 'The Rif Mountains are home to Berber communities who have preserved traditional mountain lifestyles for centuries.',
      bestTimeToVisit: 'Morning start (8 AM)',
      languages: ['English', 'French', 'Berber'],
      accessibility: false,
      instantBooking: false,
      freeCancellation: true,
      tags: ['Hiking', 'Mountains', 'Nature', 'Adventure'],
      provider: {
        name: 'Rif Mountain Adventures',
        rating: 4.6,
        verified: true
      }
    },
    {
      id: 'traditional-weaving',
      title: 'Traditional Weaving Workshop',
      description: 'Learn the ancient art of Berber weaving from local artisans in their family workshops. Create your own textile piece using traditional techniques passed down through generations.',
      shortDescription: 'Learn Berber weaving from local artisans',
      imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop',
      category: 'cultural',
      duration: '3 hours',
      location: 'Artisan Workshop',
      price: 55,
      rating: 4.6,
      reviewCount: 67,
      difficulty: 'moderate',
      groupSize: 'Up to 6 people',
      highlights: ['Hands-on learning', 'Local artisans', 'Take home creation'],
      includes: ['Materials', 'Expert instruction', 'Finished piece', 'Tea break'],
      culturalSignificance: 'Berber weaving represents centuries of textile traditions, with patterns that tell stories of tribal heritage.',
      bestTimeToVisit: 'Afternoon (2-5 PM)',
      languages: ['English', 'French', 'Berber'],
      accessibility: true,
      instantBooking: false,
      freeCancellation: true,
      tags: ['Crafts', 'Weaving', 'Traditional', 'Berber'],
      provider: {
        name: 'Chefchaouen Artisan Collective',
        rating: 4.5,
        verified: true
      }
    },
    {
      id: 'rooftop-sunset',
      title: 'Rooftop Sunset Experience',
      description: 'Watch the sunset over the blue city from the best rooftop terraces with traditional tea. Enjoy panoramic views and traditional music as the city transforms in golden light.',
      shortDescription: 'Sunset views from rooftop terraces with tea',
      imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop',
      category: 'relaxation',
      duration: '2 hours',
      location: 'Rooftop Terraces',
      price: 30,
      rating: 4.9,
      reviewCount: 198,
      difficulty: 'easy',
      groupSize: 'Up to 15 people',
      highlights: ['Sunset views', 'Traditional tea', 'Rooftop access'],
      includes: ['Rooftop access', 'Traditional tea', 'Light snacks', 'Photo opportunities'],
      culturalSignificance: 'Rooftop terraces are central to Moroccan social life, offering spaces for community gathering and reflection.',
      bestTimeToVisit: 'Evening (6-8 PM)',
      languages: ['English', 'French', 'Arabic'],
      accessibility: false,
      instantBooking: true,
      freeCancellation: true,
      tags: ['Sunset', 'Views', 'Relaxation', 'Photography'],
      provider: {
        name: 'Blue City Rooftops',
        rating: 4.8,
        verified: true
      }
    },
    {
      id: 'local-cooking-class',
      title: 'Mountain Cooking Class',
      description: 'Cook traditional Rif mountain dishes with local families using fresh, local ingredients. Learn authentic recipes and cooking techniques in a family setting.',
      shortDescription: 'Cook traditional mountain dishes with local families',
      imageUrl: 'https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=600&h=400&fit=crop',
      category: 'culinary',
      duration: '4 hours',
      location: 'Local Family Home',
      price: 70,
      rating: 4.8,
      reviewCount: 112,
      difficulty: 'easy',
      groupSize: 'Up to 8 people',
      highlights: ['Family cooking', 'Local ingredients', 'Traditional recipes'],
      includes: ['Market visit', 'Cooking lesson', 'Full meal', 'Recipe cards'],
      culturalSignificance: 'Mountain cuisine reflects the agricultural traditions and seasonal rhythms of Rif communities.',
      bestTimeToVisit: 'Afternoon (1-5 PM)',
      languages: ['English', 'French', 'Berber'],
      accessibility: true,
      instantBooking: false,
      freeCancellation: true,
      tags: ['Cooking', 'Family', 'Traditional', 'Local'],
      provider: {
        name: 'Mountain Family Experiences',
        rating: 4.7,
        verified: true
      }
    },
    {
      id: 'artisan-quarter-tour',
      title: 'Artisan Quarter Discovery',
      description: 'Visit local workshops where craftsmen create pottery, leather goods, and traditional textiles. Meet artisans and learn about their craft traditions.',
      shortDescription: 'Visit workshops creating pottery and traditional crafts',
      imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop',
      category: 'cultural',
      duration: '2.5 hours',
      location: 'Artisan Quarter',
      price: 40,
      rating: 4.5,
      reviewCount: 78,
      difficulty: 'easy',
      groupSize: 'Up to 10 people',
      highlights: ['Artisan workshops', 'Traditional crafts', 'Shopping opportunities'],
      includes: ['Workshop visits', 'Artisan demonstrations', 'Shopping time', 'Local guide'],
      culturalSignificance: 'The artisan quarter preserves traditional crafts that have been practiced in Chefchaouen for generations.',
      bestTimeToVisit: 'Morning (9 AM-12 PM)',
      languages: ['English', 'French', 'Arabic'],
      accessibility: true,
      instantBooking: true,
      freeCancellation: true,
      tags: ['Crafts', 'Artisans', 'Shopping', 'Traditional'],
      provider: {
        name: 'Chefchaouen Craft Tours',
        rating: 4.4,
        verified: true
      }
    },
    {
      id: 'spanish-mosque-hike',
      title: 'Spanish Mosque Sunrise Hike',
      description: 'Early morning hike to the Spanish Mosque for breathtaking sunrise views over Chefchaouen. Experience the city awakening as the first light illuminates the blue walls.',
      shortDescription: 'Sunrise hike to Spanish Mosque with city views',
      imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop',
      category: 'adventure',
      duration: '3 hours',
      location: 'Spanish Mosque Trail',
      price: 50,
      rating: 5.0,
      reviewCount: 89,
      difficulty: 'moderate',
      groupSize: 'Up to 8 people',
      highlights: ['Sunrise views', 'Historic mosque', 'City panorama'],
      includes: ['Hiking guide', 'Sunrise viewing', 'Light breakfast', 'Photography tips'],
      culturalSignificance: 'The Spanish Mosque represents the historical connections between Morocco and Andalusia.',
      bestTimeToVisit: 'Early morning (5-8 AM)',
      languages: ['English', 'French', 'Spanish'],
      accessibility: false,
      instantBooking: false,
      freeCancellation: true,
      tags: ['Hiking', 'Sunrise', 'Views', 'Historic'],
      provider: {
        name: 'Sunrise Adventures Chefchaouen',
        rating: 4.9,
        verified: true
      }
    }
  ]

  const categories = [
    { id: 'all', name: 'All Experiences', icon: Star },
    { id: 'cultural', name: 'Culture & History', icon: Users },
    { id: 'adventure', name: 'Adventure', icon: Mountain },
    { id: 'culinary', name: 'Culinary', icon: Coffee },
    { id: 'relaxation', name: 'Relaxation', icon: Sunrise },
    { id: 'historical', name: 'Historical', icon: Palette }
  ]

  const addToTrip = (experience: Experience) => {
    if (!selectedExperiences.find(exp => exp.id === experience.id)) {
      setSelectedExperiences([...selectedExperiences, experience])
      
      // Enhanced feedback with visual confirmation
      setShowAddedFeedback(experience.id)
      setTimeout(() => setShowAddedFeedback(null), 3000)
      
      // Update trip summary in localStorage for persistence
      const tripSummary = JSON.parse(localStorage.getItem('tripSummary') || '{}')
      const existingDestinations = tripSummary.destinations || []
      const uniqueDestinations = existingDestinations.includes('Chefchaouen') 
        ? existingDestinations 
        : [...existingDestinations, 'Chefchaouen']
      
      const updatedSummary = {
        ...tripSummary,
        destinations: uniqueDestinations,
        totalExperiences: (tripSummary.totalExperiences || 0) + 1,
        estimatedCost: (tripSummary.estimatedCost || 0) + experience.price,
        lastUpdated: new Date().toISOString()
      }
      localStorage.setItem('tripSummary', JSON.stringify(updatedSummary))
    }
    
    // Also add to global trip context
    addExperience({
      title: experience.title,
      destinationId: 'chefchaouen',
      category: experience.category,
      duration: experience.duration,
      price: experience.price,
      description: experience.description,
      imageUrl: experience.imageUrl,
      addedFrom: 'explore'
    })
  }

  const filteredExperiences = activeFilter === 'all' 
    ? experiences 
    : experiences.filter(exp => exp.category === activeFilter)

  const getTotalSelectedCost = () => {
    return selectedExperiences.reduce((total, exp) => total + exp.price, 0)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-sky-50">
      <Header />
      
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-900/60 to-blue-700/40 z-10"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url("https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=1920&h=1080&fit=crop")'
          }}
        ></div>
        
        <div className="relative z-20 text-center text-white max-w-4xl mx-auto px-6">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-5xl md:text-7xl font-bold mb-6"
          >
            Chefchaouen
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl md:text-2xl mb-8 text-blue-100"
          >
            The Blue Pearl of Morocco - A Mountain Gem Painted in Azure
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold transition-colors">
              Explore the Blue City
            </button>
            <Link href="/trip-builder" className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white px-8 py-4 rounded-xl font-semibold transition-colors">
              Plan Your Visit
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-20">
        {/* Trip Summary Widget */}
        {selectedExperiences.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="fixed top-24 right-6 z-40 bg-white rounded-2xl shadow-xl p-6 w-80 border border-neutral-200"
          >
            <h3 className="font-bold text-neutral-900 mb-4 flex items-center gap-2">
              <MapPin className="w-5 h-5 text-blue-600" />
              Your Chefchaouen Trip
            </h3>
            <div className="space-y-3 mb-4">
              {selectedExperiences.slice(0, 3).map((exp) => (
                <div key={exp.id} className="flex items-center gap-3">
                  <img src={exp.imageUrl} alt={exp.title} className="w-12 h-12 rounded-lg object-cover" />
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm text-neutral-900 truncate">{exp.title}</p>
                    <p className="text-xs text-neutral-500">€{exp.price}</p>
                  </div>
                </div>
              ))}
              {selectedExperiences.length > 3 && (
                <p className="text-sm text-neutral-500">+{selectedExperiences.length - 3} more experiences</p>
              )}
            </div>
            <div className="border-t border-neutral-200 pt-4">
              <div className="flex justify-between items-center mb-4">
                <span className="font-medium text-neutral-900">Total Cost:</span>
                <span className="font-bold text-xl text-blue-600">€{getTotalSelectedCost()}</span>
              </div>
              <Link
                href="/trip-builder"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-xl font-medium transition-colors text-center block"
              >
                Continue Planning
              </Link>
            </div>
          </motion.div>
        )}

        {/* Experiences Section */}
        <section>
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-neutral-900 mb-4">
              Discover the Blue Pearl
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              From photography tours through blue-painted streets to mountain hikes with panoramic views, experience the magic of Morocco's most photogenic city
            </p>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveFilter(category.id)}
                className={`flex items-center gap-2 px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  activeFilter === category.id
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-white text-neutral-600 hover:bg-blue-50 hover:text-blue-600'
                }`}
              >
                <category.icon className="w-4 h-4" />
                {category.name}
              </button>
            ))}
          </div>

          {/* Selected Experiences Summary */}
          {selectedExperiences.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-blue-600 text-white rounded-2xl p-6 mb-8"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold mb-1">
                    {selectedExperiences.length} Experience{selectedExperiences.length !== 1 ? 's' : ''} Selected
                  </h3>
                  <p className="text-blue-100">
                    Total: €{getTotalSelectedCost()} • Ready to add to your trip
                  </p>
                </div>
                <Link
                  href="/trip-builder"
                  className="bg-white text-blue-600 px-6 py-3 rounded-xl font-medium hover:bg-blue-50 transition-colors"
                >
                  View Trip Builder
                </Link>
              </div>
            </motion.div>
          )}

          {/* Experiences Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredExperiences.map((experience, index) => (
              <motion.div
                key={experience.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <EnhancedActivityCard
                  activity={experience}
                  variant="default"
                  showBookingButton={true}
                  showWishlistButton={true}
                  showShareButton={true}
                  onBook={(activity) => addToTrip(activity)}
                  onWishlist={(activity) => {
                    console.log('Added to wishlist:', activity.title)
                  }}
                  onShare={(activity) => {
                    if (navigator.share) {
                      navigator.share({
                        title: activity.title,
                        text: activity.shortDescription,
                        url: window.location.href
                      })
                    }
                  }}
                  className="h-full"
                />
              </motion.div>
            ))}
          </div>
        </section>
      </div>

      <Footer />
    </div>
  )
}