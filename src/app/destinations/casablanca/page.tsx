'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import { 
  MapPin, 
  Clock, 
  Star, 
  Users, 
  Plus,
  Camera,
  Building,
  Waves,
  Coffee,
  ShoppingBag,
  Utensils,
  Music
} from 'lucide-react'
import { EnhancedActivityCard } from '@/components/ui/enhanced-activity-card'
import { useTripIntegration } from '@/lib/trip-integration'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'

interface Experience {
  id: string
  title: string
  description: string
  duration: string
  price: number
  rating: number
  category: string
  image: string
  highlights: string[]
  difficulty: 'Easy' | 'Moderate' | 'Challenging'
  timeOfDay: 'Morning' | 'Afternoon' | 'Evening' | 'Full Day'
}

export default function CasablancaPage() {
  const { addDestination, addExperience } = useTripIntegration()
  const [selectedExperiences, setSelectedExperiences] = useState<Experience[]>([])
  const [showAddedFeedback, setShowAddedFeedback] = useState<string | null>(null)
  const [activeFilter, setActiveFilter] = useState('all')

  const experiences: Experience[] = [
    {
      id: 'hassan-ii-mosque',
      title: 'Hassan II Mosque Tour',
      description: 'Explore one of the world&apos;s largest mosques with stunning ocean views and intricate architecture',
      duration: '2 hours',
      price: 35,
      rating: 4.8,
      category: 'architecture',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=600&h=400&fit=crop',
      highlights: ['Guided tour', 'Ocean views', 'Architectural marvel'],
      difficulty: 'Easy',
      timeOfDay: 'Morning'
    },
    {
      id: 'corniche-walk',
      title: 'Corniche Seaside Walk',
      description: 'Stroll along Casablanca\'s beautiful coastline with cafes, restaurants, and ocean breeze',
      duration: '3 hours',
      price: 25,
      rating: 4.6,
      category: 'leisure',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop',
      highlights: ['Ocean views', 'Seaside cafes', 'Sunset viewing'],
      difficulty: 'Easy',
      timeOfDay: 'Afternoon'
    },
    {
      id: 'art-deco-tour',
      title: 'Art Deco Architecture Tour',
      description: 'Discover Casablanca&apos;s unique blend of Moorish and Art Deco architecture with an expert guide',
      duration: '3 hours',
      price: 45,
      rating: 4.7,
      category: 'architecture',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop',
      highlights: ['Expert guide', 'Historic buildings', 'Photography spots'],
      difficulty: 'Moderate',
      timeOfDay: 'Morning'
    },
    {
      id: 'central-market',
      title: 'Central Market Food Tour',
      description: 'Taste authentic Moroccan street food and fresh seafood in the bustling central market',
      duration: '2.5 hours',
      price: 55,
      rating: 4.9,
      category: 'culinary',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop',
      highlights: ['Street food', 'Fresh seafood', 'Local vendors'],
      difficulty: 'Easy',
      timeOfDay: 'Afternoon'
    },
    {
      id: 'morocco-mall',
      title: 'Morocco Mall Shopping Experience',
      description: 'Shop at Africa&apos;s largest mall with luxury brands, local crafts, and entertainment',
      duration: '4 hours',
      price: 30,
      rating: 4.4,
      category: 'shopping',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=400&fit=crop',
      highlights: ['Luxury shopping', 'Entertainment', 'Dining options'],
      difficulty: 'Easy',
      timeOfDay: 'Full Day'
    },
    {
      id: 'old-medina-exploration',
      title: 'Old Medina Cultural Walk',
      description: 'Explore the historic old medina with its traditional crafts, narrow alleys, and local life',
      duration: '2 hours',
      price: 40,
      rating: 4.5,
      category: 'culture',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=600&h=400&fit=crop',
      highlights: ['Traditional crafts', 'Local culture', 'Historic sites'],
      difficulty: 'Moderate',
      timeOfDay: 'Morning'
    },
    {
      id: 'casablanca-nightlife',
      title: 'Casablanca Nightlife Tour',
      description: 'Experience the vibrant nightlife with rooftop bars, live music, and modern Moroccan culture',
      duration: '4 hours',
      price: 65,
      rating: 4.6,
      category: 'nightlife',
      image: 'https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=600&h=400&fit=crop',
      highlights: ['Rooftop bars', 'Live music', 'Modern culture'],
      difficulty: 'Easy',
      timeOfDay: 'Evening'
    },
    {
      id: 'business-district-tour',
      title: 'Modern Business District Tour',
      description: 'Discover modern Casablanca with its skyscrapers, business centers, and contemporary architecture',
      duration: '2.5 hours',
      price: 35,
      rating: 4.3,
      category: 'modern',
      image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=600&h=400&fit=crop',
      highlights: ['Modern architecture', 'Business insights', 'City views'],
      difficulty: 'Easy',
      timeOfDay: 'Afternoon'
    }
  ]

  const categories = [
    { id: 'all', name: 'All Experiences', icon: Star },
    { id: 'architecture', name: 'Architecture', icon: Building },
    { id: 'culture', name: 'Culture', icon: Users },
    { id: 'culinary', name: 'Food & Dining', icon: Utensils },
    { id: 'leisure', name: 'Leisure', icon: Waves },
    { id: 'shopping', name: 'Shopping', icon: ShoppingBag },
    { id: 'nightlife', name: 'Nightlife', icon: Music },
    { id: 'modern', name: 'Modern City', icon: Building }
  ]

  const addToTrip = (experience: Experience) => {
    if (!selectedExperiences.find(exp => exp.id === experience.id)) {
      setSelectedExperiences([...selectedExperiences, experience])
      
      // Enhanced feedback with visual confirmation
      setShowAddedFeedback(experience.id)
      setTimeout(() => setShowAddedFeedback(null), 3000)
      
      // Update trip summary in localStorage for persistence
      const tripSummary = JSON.parse(localStorage.getItem('tripSummary') || '{}')
      const existingDestinations = tripSummary.destinations || []
      const uniqueDestinations = existingDestinations.includes('Casablanca') 
        ? existingDestinations 
        : [...existingDestinations, 'Casablanca']
      
      const updatedSummary = {
        ...tripSummary,
        destinations: uniqueDestinations,
        totalExperiences: (tripSummary.totalExperiences || 0) + 1,
        estimatedCost: (tripSummary.estimatedCost || 0) + experience.price,
        lastUpdated: new Date().toISOString()
      }
      localStorage.setItem('tripSummary', JSON.stringify(updatedSummary))
    }
    
    // Also add to global trip context
    addExperience({
      title: experience.title,
      destinationId: 'casablanca',
      category: experience.category,
      duration: experience.duration,
      price: experience.price,
      description: experience.description,
      imageUrl: experience.image,
      addedFrom: 'explore'
    })
  }

  const filteredExperiences = activeFilter === 'all' 
    ? experiences 
    : experiences.filter(exp => exp.category === activeFilter)

  const getTotalSelectedCost = () => {
    return selectedExperiences.reduce((total, exp) => total + exp.price, 0)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-primary-50">
      <Header />
      
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-black/40 z-10"></div>
        <div 
          className="absolute inset-0 bg-cover bg-center"
          style={{
            backgroundImage: 'url("https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=1920&h=1080&fit=crop")'
          }}
        ></div>
        
        <div className="relative z-20 text-center text-white max-w-4xl mx-auto px-6">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-5xl md:text-7xl font-bold mb-6"
          >
            Casablanca
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl md:text-2xl mb-8 text-neutral-200"
          >
            Morocco&apos;s Economic Capital - Where Tradition Meets Modernity
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <button className="bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 rounded-xl font-semibold transition-colors">
              Explore Experiences
            </button>
            <Link href="/trip-builder" className="bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white px-8 py-4 rounded-xl font-semibold transition-colors">
              Plan Your Visit
            </Link>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-20">
        {/* Trip Summary Widget */}
        {selectedExperiences.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="fixed top-24 right-6 z-40 bg-white rounded-2xl shadow-xl p-6 w-80 border border-neutral-200"
          >
            <h3 className="font-bold text-neutral-900 mb-4 flex items-center gap-2">
              <MapPin className="w-5 h-5 text-primary-600" />
              Your Casablanca Trip
            </h3>
            <div className="space-y-3 mb-4">
              {selectedExperiences.slice(0, 3).map((exp) => (
                <div key={exp.id} className="flex items-center gap-3">
                  <img src={exp.image} alt={exp.title} className="w-12 h-12 rounded-lg object-cover" />
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm text-neutral-900 truncate">{exp.title}</p>
                    <p className="text-xs text-neutral-500">€{exp.price}</p>
                  </div>
                </div>
              ))}
              {selectedExperiences.length > 3 && (
                <p className="text-sm text-neutral-500">+{selectedExperiences.length - 3} more experiences</p>
              )}
            </div>
            <div className="border-t border-neutral-200 pt-4">
              <div className="flex justify-between items-center mb-4">
                <span className="font-medium text-neutral-900">Total Cost:</span>
                <span className="font-bold text-xl text-primary-600">€{getTotalSelectedCost()}</span>
              </div>
              <Link
                href="/trip-builder"
                className="w-full bg-primary-600 hover:bg-primary-700 text-white py-3 px-4 rounded-xl font-medium transition-colors text-center block"
              >
                Continue Planning
              </Link>
            </div>
          </motion.div>
        )}

        {/* Experiences Section */}
        <section>
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-neutral-900 mb-4">
              Discover Casablanca
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              From the magnificent Hassan II Mosque to modern business districts, experience Morocco's cosmopolitan heart
            </p>
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveFilter(category.id)}
                className={`flex items-center gap-2 px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  activeFilter === category.id
                    ? 'bg-primary-600 text-white shadow-lg'
                    : 'bg-white text-neutral-600 hover:bg-primary-50 hover:text-primary-600'
                }`}
              >
                <category.icon className="w-4 h-4" />
                {category.name}
              </button>
            ))}
          </div>

          {/* Selected Experiences Summary */}
          {selectedExperiences.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-primary-600 text-white rounded-2xl p-6 mb-8"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold mb-1">
                    {selectedExperiences.length} Experience{selectedExperiences.length !== 1 ? 's' : ''} Selected
                  </h3>
                  <p className="text-primary-100">
                    Total: €{getTotalSelectedCost()} • Ready to add to your trip
                  </p>
                </div>
                <Link
                  href="/trip-builder"
                  className="bg-white text-primary-600 px-6 py-3 rounded-xl font-medium hover:bg-primary-50 transition-colors"
                >
                  View Trip Builder
                </Link>
              </div>
            </motion.div>
          )}

          {/* Experiences Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredExperiences.map((experience, index) => {
              const activityData = {
                id: experience.id,
                title: experience.title,
                description: experience.description,
                shortDescription: experience.description,
                imageUrl: experience.image,
                category: experience.category as 'cultural' | 'adventure' | 'culinary' | 'relaxation' | 'historical',
                duration: experience.duration,
                location: 'Casablanca, Morocco',
                price: experience.price,
                rating: experience.rating,
                reviewCount: Math.floor(Math.random() * 100) + 20,
                difficulty: experience.difficulty.toLowerCase() as 'easy' | 'moderate' | 'challenging',
                groupSize: '2-8 people',
                highlights: experience.highlights,
                includes: ['Professional guide', 'Cultural insights', 'Photo opportunities'],
                culturalSignificance: 'Discover the modern heart of Morocco where tradition meets contemporary life',
                bestTimeToVisit: 'Year-round, pleasant coastal climate',
                languages: ['English', 'French', 'Arabic'],
                accessibility: true,
                instantBooking: true,
                freeCancellation: true,
                tags: [experience.category, experience.difficulty, experience.timeOfDay],
                provider: {
                  name: 'Casablanca City Tours',
                  rating: 4.6,
                  verified: true
                }
              }

              return (
                <motion.div
                  key={experience.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                >
                  <EnhancedActivityCard
                    activity={activityData}
                    variant="default"
                    onBook={() => addToTrip(experience)}
                    onWishlist={() => {}}
                    onShare={() => {}}
                    showBookingButton={true}
                    showWishlistButton={false}
                    showShareButton={false}
                  />
                </motion.div>
              )
            })}
          </div>
        </section>
      </div>

      <Footer />
    </div>
  )
}