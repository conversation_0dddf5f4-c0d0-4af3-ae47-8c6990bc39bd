'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { useTripIntegration } from '@/lib/trip-integration'
import { 
  Sun, 
  Moon, 
  Star, 
  Thermometer, 
  Wind, 
  Eye,
  Camera,
  Tent,
  Navigation,
  Clock,
  Users,
  Calendar,
  MapPin,
  Sunrise,
  Sunset,
  Mountain,
  Compass,
  Coffee,
  Music,
  Heart,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Plus
} from 'lucide-react'
import { EnhancedActivityCard } from '@/components/ui/enhanced-activity-card'

interface DesertExperience {
  id: string
  title: string
  description: string
  timeOfDay: 'dawn' | 'morning' | 'afternoon' | 'sunset' | 'night'
  duration: string
  intensity: 'peaceful' | 'moderate' | 'adventurous'
  price: string
  highlights: string[]
  bestSeason: string[]
  groupSize: string
}

interface WeatherData {
  temperature: { day: number; night: number }
  conditions: string
  windSpeed: number
  visibility: string
  stargazingQuality: number
}

interface DesertCamp {
  id: string
  name: string
  type: 'luxury' | 'traditional' | 'adventure'
  description: string
  amenities: string[]
  pricePerNight: string
  image: string
  location: string
  capacity: number
}

interface Experience {
  id: string
  title: string
  description: string
  duration: string
  price: number
  rating: number
  category: string
  image: string
  highlights: string[]
  difficulty: 'Easy' | 'Moderate' | 'Challenging'
  timeOfDay: 'Morning' | 'Afternoon' | 'Evening' | 'Full Day'
}

export default function SaharaPage() {
  const { addDestination, addExperience } = useTripIntegration()
  const [currentTime, setCurrentTime] = useState<'dawn' | 'day' | 'sunset' | 'night'>('day')
  const [selectedExperience, setSelectedExperience] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'immersive' | 'planner' | 'camps' | 'experiences'>('immersive')
  const [isAudioPlaying, setIsAudioPlaying] = useState(false)
  const [selectedCamp, setSelectedCamp] = useState<string | null>(null)
  const [selectedExperiences, setSelectedExperiences] = useState<Experience[]>([])
  const [showAddedFeedback, setShowAddedFeedback] = useState<string | null>(null)
  const [activeFilter, setActiveFilter] = useState('all')

  // Simulate time progression
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(prev => {
        const times: Array<'dawn' | 'day' | 'sunset' | 'night'> = ['dawn', 'day', 'sunset', 'night']
        const currentIndex = times.indexOf(prev)
        return times[(currentIndex + 1) % times.length]
      })
    }, 8000) // Change every 8 seconds

    return () => clearInterval(interval)
  }, [])

  const experiences: DesertExperience[] = [
    {
      id: 'camel-sunrise',
      title: 'Dawn Camel Trek',
      description: 'Begin your day as the Berbers have for centuries, riding into the sunrise across golden dunes',
      timeOfDay: 'dawn',
      duration: '3 hours',
      intensity: 'peaceful',
      price: '€85',
      highlights: ['Sunrise views', 'Traditional breakfast', 'Berber stories'],
      bestSeason: ['October', 'November', 'December', 'January', 'February', 'March'],
      groupSize: '2-8 people'
    },
    {
      id: 'sandboarding',
      title: 'Dune Sandboarding',
      description: 'Surf the sand dunes on a thrilling adventure across the highest dunes in Morocco',
      timeOfDay: 'morning',
      duration: '2 hours',
      intensity: 'adventurous',
      price: '€65',
      highlights: ['Adrenaline rush', 'Professional instruction', 'Epic photos'],
      bestSeason: ['October', 'November', 'December', 'January', 'February'],
      groupSize: '1-12 people'
    },
    {
      id: 'nomad-lunch',
      title: 'Nomadic Feast',
      description: 'Share a traditional meal prepared in the sand with a nomadic family',
      timeOfDay: 'afternoon',
      duration: '2.5 hours',
      intensity: 'peaceful',
      price: '€75',
      highlights: ['Authentic cuisine', 'Cultural exchange', 'Desert cooking'],
      bestSeason: ['All year'],
      groupSize: '2-10 people'
    },
    {
      id: 'sunset-meditation',
      title: 'Sunset Meditation',
      description: 'Find inner peace as the sun sets over endless dunes in complete silence',
      timeOfDay: 'sunset',
      duration: '1.5 hours',
      intensity: 'peaceful',
      price: '€45',
      highlights: ['Guided meditation', 'Sunset views', 'Inner peace'],
      bestSeason: ['All year'],
      groupSize: '1-15 people'
    },
    {
      id: 'stargazing',
      title: 'Astronomical Journey',
      description: 'Explore the cosmos with professional telescopes in one of the world\'s darkest skies',
      timeOfDay: 'night',
      duration: '3 hours',
      intensity: 'moderate',
      price: '€95',
      highlights: ['Professional telescopes', 'Constellation stories', 'Astrophotography'],
      bestSeason: ['October', 'November', 'December', 'January', 'February', 'March'],
      groupSize: '2-12 people'
    },
    {
      id: 'night-music',
      title: 'Desert Music Circle',
      description: 'Join Berber musicians around the fire for traditional songs under the stars',
      timeOfDay: 'night',
      duration: '2 hours',
      intensity: 'peaceful',
      price: '€55',
      highlights: ['Live music', 'Campfire stories', 'Cultural immersion'],
      bestSeason: ['All year'],
      groupSize: '4-20 people'
    }
  ]

  const camps: DesertCamp[] = [
    {
      id: 'luxury-oasis',
      name: 'Desert Oasis Luxury Camp',
      type: 'luxury',
      description: 'Experience the Sahara in ultimate comfort with air-conditioned tents and gourmet dining',
      amenities: ['Private bathroom', 'AC/Heating', 'Gourmet meals', 'Spa services', 'WiFi'],
      pricePerNight: '€350',
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=600&h=400&fit=crop',
      location: 'Erg Chebbi',
      capacity: 24
    },
    {
      id: 'traditional-berber',
      name: 'Authentic Berber Camp',
      type: 'traditional',
      description: 'Sleep under the stars in traditional nomad tents with authentic Berber hospitality',
      amenities: ['Shared facilities', 'Traditional meals', 'Campfire', 'Berber music', 'Camel rides'],
      pricePerNight: '€120',
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=600&h=400&fit=crop',
      location: 'Erg Chebbi',
      capacity: 40
    },
    {
      id: 'adventure-base',
      name: 'Desert Adventure Base',
      type: 'adventure',
      description: 'Perfect for thrill-seekers with sandboarding, quad biking, and extreme desert sports',
      amenities: ['Equipment rental', 'Adventure guides', 'Basic meals', 'Group activities', 'Safety gear'],
      pricePerNight: '€95',
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=600&h=400&fit=crop',
      location: 'Erg Chebbi',
      capacity: 30
    }
  ]

  const bookableExperiences: Experience[] = [
    {
      id: 'camel-trek-sunset',
      title: 'Sunset Camel Trek',
      description: 'Journey into the golden dunes on camelback and witness the most spectacular sunset of your life',
      duration: '3 hours',
      price: 65,
      rating: 4.9,
      category: 'adventure',
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=600&h=400&fit=crop',
      highlights: ['Camel riding', 'Sunset views', 'Tea ceremony'],
      difficulty: 'Easy',
      timeOfDay: 'Evening'
    },
    {
      id: 'overnight-luxury-camp',
      title: 'Luxury Desert Camp Overnight',
      description: 'Sleep under a blanket of stars in our premium desert camp with all modern amenities',
      duration: '2 days',
      price: 350,
      rating: 5.0,
      category: 'accommodation',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop',
      highlights: ['Luxury tents', 'Gourmet meals', 'Stargazing'],
      difficulty: 'Easy',
      timeOfDay: 'Full Day'
    },
    {
      id: 'sandboarding-adventure',
      title: 'Sandboarding & Quad Biking',
      description: 'Adrenaline-pumping adventure combining sandboarding down massive dunes and quad biking',
      duration: '4 hours',
      price: 95,
      rating: 4.7,
      category: 'adventure',
      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=400&fit=crop',
      highlights: ['Sandboarding', 'Quad biking', 'Professional guide'],
      difficulty: 'Moderate',
      timeOfDay: 'Morning'
    },
    {
      id: 'berber-cultural-experience',
      title: 'Berber Family Experience',
      description: 'Share tea and stories with a local Berber family and learn about traditional desert life',
      duration: '2.5 hours',
      price: 45,
      rating: 4.8,
      category: 'culture',
      image: 'https://images.unsplash.com/photo-1559827260-dc66d52bef19?w=600&h=400&fit=crop',
      highlights: ['Family visit', 'Traditional tea', 'Cultural stories'],
      difficulty: 'Easy',
      timeOfDay: 'Afternoon'
    },
    {
      id: 'astronomy-night-tour',
      title: 'Desert Astronomy Experience',
      description: 'Professional stargazing session with telescopes and expert guide in the dark desert sky',
      duration: '3 hours',
      price: 75,
      rating: 4.9,
      category: 'nature',
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop',
      highlights: ['Professional telescopes', 'Expert guide', 'Star charts'],
      difficulty: 'Easy',
      timeOfDay: 'Evening'
    },
    {
      id: 'sunrise-hot-air-balloon',
      title: 'Sunrise Hot Air Balloon',
      description: 'Float above the endless dunes at sunrise for breathtaking aerial views of the Sahara',
      duration: '4 hours',
      price: 280,
      rating: 5.0,
      category: 'adventure',
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop',
      highlights: ['Hot air balloon', 'Sunrise views', 'Champagne breakfast'],
      difficulty: 'Easy',
      timeOfDay: 'Morning'
    },
    {
      id: 'desert-photography-workshop',
      title: 'Desert Photography Workshop',
      description: 'Learn to capture the perfect desert shots with a professional photographer guide',
      duration: '5 hours',
      price: 120,
      rating: 4.6,
      category: 'photography',
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=600&h=400&fit=crop',
      highlights: ['Professional guidance', 'Equipment provided', 'Editing tips'],
      difficulty: 'Moderate',
      timeOfDay: 'Full Day'
    },
    {
      id: 'traditional-music-night',
      title: 'Traditional Music & Dance Night',
      description: 'Experience authentic Berber music and dance around the campfire under the stars',
      duration: '2 hours',
      price: 35,
      rating: 4.7,
      category: 'culture',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop',
      highlights: ['Live music', 'Traditional dance', 'Campfire setting'],
      difficulty: 'Easy',
      timeOfDay: 'Evening'
    }
  ]

  const categories = [
    { id: 'all', name: 'All Experiences', icon: Star },
    { id: 'adventure', name: 'Adventure', icon: Mountain },
    { id: 'culture', name: 'Culture', icon: Users },
    { id: 'nature', name: 'Nature & Stars', icon: Eye },
    { id: 'accommodation', name: 'Desert Camps', icon: Tent },
    { id: 'photography', name: 'Photography', icon: Camera }
  ]

  const currentWeather: WeatherData = {
    temperature: { day: 28, night: 12 },
    conditions: 'Clear skies',
    windSpeed: 8,
    visibility: 'Excellent',
    stargazingQuality: 9.5
  }

  const addToTrip = (experience: Experience) => {
    if (!selectedExperiences.find(exp => exp.id === experience.id)) {
      setSelectedExperiences([...selectedExperiences, experience])
      
      // Enhanced feedback with visual confirmation
      setShowAddedFeedback(experience.id)
      setTimeout(() => setShowAddedFeedback(null), 3000)
      
      // Update trip summary in localStorage for persistence
      const tripSummary = JSON.parse(localStorage.getItem('tripSummary') || '{}')
      const existingDestinations = tripSummary.destinations || []
      const uniqueDestinations = existingDestinations.includes('Sahara') 
        ? existingDestinations 
        : [...existingDestinations, 'Sahara']
      
      const updatedSummary = {
        ...tripSummary,
        destinations: uniqueDestinations,
        totalExperiences: (tripSummary.totalExperiences || 0) + 1,
        estimatedCost: (tripSummary.estimatedCost || 0) + experience.price,
        lastUpdated: new Date().toISOString()
      }
      localStorage.setItem('tripSummary', JSON.stringify(updatedSummary))
    }
    
    // Also add to global trip context
    addExperience({
      title: experience.title,
      destinationId: 'sahara',
      category: experience.category,
      duration: experience.duration,
      price: experience.price,
      description: experience.description,
      imageUrl: experience.image,
      addedFrom: 'explore'
    })
  }

  const filteredExperiences = activeFilter === 'all' 
    ? bookableExperiences 
    : bookableExperiences.filter(exp => exp.category === activeFilter)

  const getTotalSelectedCost = () => {
    return selectedExperiences.reduce((total, exp) => total + exp.price, 0)
  }

  const getTimeBasedStyling = () => {
    switch (currentTime) {
      case 'dawn':
        return {
          bg: 'from-orange-300 via-pink-200 to-purple-300',
          text: 'text-orange-900',
          overlay: 'from-orange-500/30 to-pink-500/30'
        }
      case 'day':
        return {
          bg: 'from-blue-400 via-blue-300 to-yellow-200',
          text: 'text-blue-900',
          overlay: 'from-blue-500/20 to-yellow-500/20'
        }
      case 'sunset':
        return {
          bg: 'from-red-400 via-orange-400 to-yellow-300',
          text: 'text-red-900',
          overlay: 'from-red-500/40 to-orange-500/40'
        }
      case 'night':
        return {
          bg: 'from-indigo-900 via-purple-900 to-black',
          text: 'text-white',
          overlay: 'from-indigo-900/60 to-purple-900/60'
        }
    }
  }

  const getTimeIcon = () => {
    switch (currentTime) {
      case 'dawn': return Sunrise
      case 'day': return Sun
      case 'sunset': return Sunset
      case 'night': return Moon
    }
  }

  const getExperiencesForTime = () => {
    return experiences.filter(exp => 
      exp.timeOfDay === currentTime || 
      (currentTime === 'day' && (exp.timeOfDay === 'morning' || exp.timeOfDay === 'afternoon'))
    )
  }

  const styling = getTimeBasedStyling()
  const TimeIcon = getTimeIcon()

  const addDestinationToTrip = () => {
    addDestination({
      name: 'Sahara Desert',
      country: 'MA',
      coordinates: { lat: 31.0801, lng: -4.0020 },
      estimatedDays: 3,
      priority: 'high',
      addedFrom: 'explore'
    })
    alert('Sahara Desert added to your trip! Continue exploring or go to Trip Builder to plan your itinerary.')
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      {/* Immersive Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Dynamic Background */}
        <div className="absolute inset-0 z-0">
          <div className={`absolute inset-0 bg-gradient-to-br ${styling.overlay} z-10`} />
          <motion.div
            key={currentTime}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 2 }}
            className={`absolute inset-0 bg-gradient-to-br ${styling.bg}`}
          />
          <img
            src="https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80"
            alt="Sahara Desert"
            className="w-full h-full object-cover opacity-40"
          />
        </div>

        {/* Floating Weather Widget */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          className="absolute top-8 right-8 bg-white/90 backdrop-blur-md rounded-2xl p-4 shadow-lg"
        >
          <div className="flex items-center gap-3 mb-3">
            <Thermometer className="w-5 h-5 text-morocco-500" />
            <span className="font-medium text-neutral-900">Desert Conditions</span>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-neutral-600">Day:</span>
              <span className="font-medium">{currentWeather.temperature.day}°C</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600">Night:</span>
              <span className="font-medium">{currentWeather.temperature.night}°C</span>
            </div>
            <div className="flex justify-between">
              <span className="text-neutral-600">Stargazing:</span>
              <div className="flex items-center gap-1">
                <Star className="w-3 h-3 text-yellow-500 fill-current" />
                <span className="font-medium">{currentWeather.stargazingQuality}/10</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Time Indicator */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 1.2 }}
          className="absolute top-8 left-8 bg-white/90 backdrop-blur-md rounded-2xl p-4 shadow-lg"
        >
          <div className="flex items-center gap-3">
            <TimeIcon className="w-6 h-6 text-primary-500" />
            <div>
              <p className="font-medium text-neutral-900 capitalize">{currentTime}</p>
              <p className="text-xs text-neutral-600">Desert time simulation</p>
            </div>
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="relative z-20 container-custom text-center">
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1 }}
            className={styling.text}
          >
            <div className="flex items-center justify-center gap-2 mb-6">
              <MapPin className="w-6 h-6 text-sand-400" />
              <span className="text-sand-200 font-medium">Sahara Desert, Morocco</span>
            </div>
            
            <h1 className="heading-xl mb-6">
              Golden Ocean
              <span className="block bg-gradient-to-r from-sand-400 to-morocco-400 bg-clip-text text-transparent">
                of Endless Dreams
              </span>
            </h1>
            
            <p className="text-body-lg max-w-3xl mx-auto mb-12 text-white/90">
              Experience the world's most magnificent desert through the rhythm of day and night. 
              Every moment offers a different face of this ancient landscape.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <button 
                onClick={addDestinationToTrip}
                className="btn-accent btn-lg"
              >
                <Plus className="mr-3 h-5 w-5" />
                Add to My Trip
              </button>
              
              <button 
                onClick={() => setViewMode('immersive')}
                className={`btn-lg ${viewMode === 'immersive' ? 'btn-primary' : 'btn-secondary'}`}
              >
                <Eye className="mr-3 h-5 w-5" />
                Live Desert Experience
              </button>
              
              <button 
                onClick={() => setViewMode('planner')}
                className={`btn-lg ${viewMode === 'planner' ? 'btn-primary' : 'btn-secondary'}`}
              >
                <Calendar className="mr-3 h-5 w-5" />
                Plan Your Adventure
              </button>

              <button 
                onClick={() => setViewMode('camps')}
                className={`btn-lg ${viewMode === 'camps' ? 'btn-primary' : 'btn-secondary'}`}
              >
                <Tent className="mr-3 h-5 w-5" />
                Desert Camps
              </button>

              <button 
                onClick={() => setViewMode('experiences')}
                className={`btn-lg ${viewMode === 'experiences' ? 'btn-primary' : 'btn-secondary'}`}
              >
                <Star className="mr-3 h-5 w-5" />
                Book Experiences
              </button>
            </div>
          </motion.div>
        </div>

        {/* Ambient Sound Control */}
        <motion.button
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2 }}
          onClick={() => setIsAudioPlaying(!isAudioPlaying)}
          className="absolute bottom-8 left-8 bg-white/20 backdrop-blur-md rounded-full p-4 text-white hover:bg-white/30 transition-all duration-300"
        >
          {isAudioPlaying ? <Volume2 className="w-6 h-6" /> : <VolumeX className="w-6 h-6" />}
        </motion.button>
      </section>

      {/* Main Content */}
      <section className="section-padding">
        <div className="container-custom">
          {/* Floating Trip Summary Widget */}
          {selectedExperiences.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              className="fixed top-24 right-6 z-40 bg-white rounded-2xl shadow-xl p-6 w-80 border border-neutral-200 max-w-[calc(100vw-3rem)] sm:w-80"
            >
              <h3 className="font-bold text-neutral-900 mb-4 flex items-center gap-2">
                <MapPin className="w-5 h-5 text-amber-600" />
                Your Sahara Trip
              </h3>
              <div className="space-y-3 mb-4">
                {selectedExperiences.slice(0, 3).map((exp) => (
                  <div key={exp.id} className="flex items-center gap-3">
                    <img src={exp.image} alt={exp.title} className="w-12 h-12 rounded-lg object-cover" />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm text-neutral-900 truncate">{exp.title}</p>
                      <p className="text-xs text-neutral-500">€{exp.price}</p>
                    </div>
                  </div>
                ))}
                {selectedExperiences.length > 3 && (
                  <p className="text-sm text-neutral-500">+{selectedExperiences.length - 3} more experiences</p>
                )}
              </div>
              <div className="border-t border-neutral-200 pt-4">
                <div className="flex justify-between items-center mb-4">
                  <span className="font-medium text-neutral-900">Total Cost:</span>
                  <span className="font-bold text-xl text-amber-600">€{totalCost}</span>
                </div>
                <Link
                  href="/trip-builder"
                  className="w-full bg-gradient-to-r from-amber-500 to-orange-600 hover:from-amber-600 hover:to-orange-700 text-white py-3 px-4 rounded-xl font-medium transition-all duration-200 text-center block min-h-[44px] flex items-center justify-center"
                >
                  Continue Planning
                </Link>
              </div>
            </motion.div>
          )}

          {viewMode === 'immersive' && (
            <div className="space-y-12">
              {/* Time-Based Experience Showcase */}
              <div className="text-center mb-12">
                <motion.div
                  key={currentTime}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8 }}
                >
                  <h2 className="heading-lg text-neutral-900 mb-4">
                    Desert at <span className="capitalize text-primary-600">{currentTime}</span>
                  </h2>
                  <p className="text-body-lg text-neutral-600 max-w-2xl mx-auto">
                    {currentTime === 'dawn' && "Watch the desert come alive as the first rays of sun paint the dunes in gold"}
                    {currentTime === 'day' && "Experience the vast beauty and adventure opportunities under the bright desert sun"}
                    {currentTime === 'sunset' && "Witness nature's most spectacular show as the sun sets over endless dunes"}
                    {currentTime === 'night' && "Discover the magic of the desert night with pristine stargazing and Berber traditions"}
                  </p>
                </motion.div>
              </div>

              {/* Dynamic Experience Grid */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentTime}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.6 }}
                  className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
                >
                  {getExperiencesForTime().map((experience, index) => (
                    <motion.div
                      key={experience.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="card-feature group cursor-pointer"
                      onClick={() => setSelectedExperience(experience.id)}
                    >
                      <div className="relative aspect-[4/3] rounded-2xl overflow-hidden mb-6">
                        <img
                          src="https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=600&h=400&fit=crop"
                          alt={experience.title}
                          className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                        />
                        
                        <div className="absolute top-4 left-4 flex gap-2">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                            experience.intensity === 'peaceful' ? 'bg-green-100 text-green-700' :
                            experience.intensity === 'moderate' ? 'bg-yellow-100 text-yellow-700' :
                            'bg-red-100 text-red-700'
                          }`}>
                            {experience.intensity}
                          </span>
                        </div>
                        
                        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full">
                          <span className="text-xs font-medium text-neutral-700">{experience.duration}</span>
                        </div>

                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                      
                      <div className="space-y-4">
                        <div>
                          <h3 className="heading-md text-neutral-900 mb-2 group-hover:text-primary-600 transition-colors duration-300">
                            {experience.title}
                          </h3>
                          <p className="text-body text-neutral-600 mb-4">{experience.description}</p>
                          
                          <div className="flex flex-wrap gap-2 mb-4">
                            {experience.highlights.slice(0, 3).map((highlight, i) => (
                              <span key={i} className="bg-neutral-100 text-neutral-600 text-xs px-2 py-1 rounded-full">
                                {highlight}
                              </span>
                            ))}
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between pt-4 border-t border-neutral-100">
                          <div className="flex items-center gap-4 text-sm text-neutral-500">
                            <div className="flex items-center gap-1">
                              <Users className="w-4 h-4" />
                              <span>{experience.groupSize}</span>
                            </div>
                          </div>
                          <div className="font-bold text-primary-600">
                            {experience.price}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </motion.div>
              </AnimatePresence>

              {/* Time Progression Indicator */}
              <div className="text-center">
                <div className="inline-flex items-center gap-4 bg-neutral-100 rounded-full p-2">
                  {['dawn', 'day', 'sunset', 'night'].map((time) => {
                    const Icon = time === 'dawn' ? Sunrise : time === 'day' ? Sun : time === 'sunset' ? Sunset : Moon
                    return (
                      <div
                        key={time}
                        className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
                          currentTime === time || (currentTime === 'day' && time === 'day')
                            ? 'bg-primary-500 text-white'
                            : 'text-neutral-400'
                        }`}
                      >
                        <Icon className="w-5 h-5" />
                      </div>
                    )
                  })}
                </div>
                <p className="text-sm text-neutral-500 mt-3">
                  Experience changes every few seconds • Click camps or planner to explore more
                </p>
              </div>
            </div>
          )}

          {viewMode === 'camps' && (
            <div className="space-y-8">
              <div className="text-center mb-12">
                <h2 className="heading-lg text-neutral-900 mb-4">Desert Camps & Accommodation</h2>
                <p className="text-body-lg text-neutral-600 max-w-2xl mx-auto">
                  Choose your perfect desert basecamp, from luxury glamping to authentic Berber experiences
                </p>
              </div>

              <div className="grid lg:grid-cols-3 gap-8">
                {camps.map((camp, index) => (
                  <motion.div
                    key={camp.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="card-elevated group cursor-pointer"
                    onClick={() => setSelectedCamp(camp.id)}
                  >
                    <div className="relative aspect-[4/3] rounded-2xl overflow-hidden mb-6">
                      <img
                        src={camp.image}
                        alt={camp.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                      />
                      
                      <div className="absolute top-4 left-4">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                          camp.type === 'luxury' ? 'bg-purple-100 text-purple-700' :
                          camp.type === 'traditional' ? 'bg-sand-100 text-sand-700' :
                          'bg-green-100 text-green-700'
                        }`}>
                          {camp.type}
                        </span>
                      </div>
                      
                      <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full">
                        <span className="text-xs font-medium text-neutral-700">Up to {camp.capacity} guests</span>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <h3 className="heading-md text-neutral-900 mb-2">{camp.name}</h3>
                        <p className="text-body text-neutral-600 mb-4">{camp.description}</p>
                        
                        <div className="space-y-2 mb-4">
                          <h4 className="font-semibold text-neutral-900 text-sm">Amenities:</h4>
                          <div className="flex flex-wrap gap-2">
                            {camp.amenities.slice(0, 4).map((amenity, i) => (
                              <span key={i} className="bg-neutral-100 text-neutral-600 text-xs px-2 py-1 rounded-full">
                                {amenity}
                              </span>
                            ))}
                            {camp.amenities.length > 4 && (
                              <span className="text-xs text-neutral-500">+{camp.amenities.length - 4} more</span>
                            )}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between pt-4 border-t border-neutral-100">
                        <div className="flex items-center gap-1 text-sm text-neutral-500">
                          <MapPin className="w-4 h-4" />
                          <span>{camp.location}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-primary-600">{camp.pricePerNight}</div>
                          <div className="text-xs text-neutral-500">per night</div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {viewMode === 'planner' && (
            <div className="text-center py-20">
              <Compass className="w-16 h-16 text-primary-500 mx-auto mb-6" />
              <h3 className="heading-md text-neutral-900 mb-4">Desert Adventure Planner</h3>
              <p className="text-body text-neutral-600 mb-8 max-w-2xl mx-auto">
                Our comprehensive desert trip planner is coming soon. For now, explore our immersive experience 
                and camps to get inspired for your Sahara adventure.
              </p>
              <div className="flex gap-4 justify-center">
                <button 
                  onClick={() => setViewMode('immersive')}
                  className="btn-primary"
                >
                  Explore Experiences
                </button>
                <button 
                  onClick={() => setViewMode('camps')}
                  className="btn-secondary"
                >
                  View Camps
                </button>
              </div>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  )
}