'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  MessageCircle,
  Sparkles,
  X,
  Minimize2,
  Maximize2,
  HelpCircle,
  MapPin,
  Calendar,
  Star
} from 'lucide-react'
import { ConversationalAI } from './conversational-ai'
import { useCountryContext } from '@/lib/country-context'

interface AIAssistantTriggerProps {
  className?: string
  initialMessage?: string
}

export function AIAssistantTrigger({ className = '', initialMessage }: AIAssistantTriggerProps) {
  const { currentCountry } = useCountryContext()
  const [isOpen, setIsOpen] = useState(false)
  const [isMinimized, setIsMinimized] = useState(false)
  const [hasNewSuggestion, setHasNewSuggestion] = useState(false)
  const [currentSuggestion, setCurrentSuggestion] = useState('')

  // Contextual suggestions based on page and time
  const suggestions = [
    `What's the best time to visit ${currentCountry.displayName}?`,
    'Show me authentic local experiences',
    'Help me plan a 3-day itinerary',
    'What should I know about local culture?',
    'Find the best photography spots',
    'Recommend traditional food experiences',
    'What are the must-see destinations?',
    'Help me with travel budget planning'
  ]

  useEffect(() => {
    // Rotate suggestions every 10 seconds
    const interval = setInterval(() => {
      const randomSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)]
      setCurrentSuggestion(randomSuggestion)
      setHasNewSuggestion(true)
      
      // Hide suggestion after 5 seconds
      setTimeout(() => setHasNewSuggestion(false), 5000)
    }, 10000)

    // Set initial suggestion
    setCurrentSuggestion(suggestions[0])
    setTimeout(() => setHasNewSuggestion(true), 2000)

    return () => clearInterval(interval)
  }, [currentCountry])

  const handleOpen = () => {
    setIsOpen(true)
    setHasNewSuggestion(false)
  }

  const handleClose = () => {
    setIsOpen(false)
    setIsMinimized(false)
  }

  const handleMinimize = () => {
    setIsMinimized(true)
  }

  const handleMaximize = () => {
    setIsMinimized(false)
  }

  return (
    <>
      {/* Floating Trigger Button */}
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className={`fixed bottom-6 right-6 z-40 ${className}`}
          >
            {/* Suggestion Bubble */}
            <AnimatePresence>
              {hasNewSuggestion && currentSuggestion && (
                <motion.div
                  initial={{ opacity: 0, x: 20, scale: 0.9 }}
                  animate={{ opacity: 1, x: 0, scale: 1 }}
                  exit={{ opacity: 0, x: 20, scale: 0.9 }}
                  className="absolute bottom-16 right-0 mb-2 max-w-xs"
                >
                  <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-4 relative">
                    <div className="absolute -bottom-2 right-6 w-4 h-4 bg-white border-r border-b border-gray-200 transform rotate-45" />
                    
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <Sparkles className="w-4 h-4 text-white" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 mb-1">
                          AI Assistant Suggestion
                        </p>
                        <p className="text-sm text-gray-600 leading-relaxed">
                          {currentSuggestion}
                        </p>
                        
                        <button
                          onClick={() => {
                            setIsOpen(true)
                            setHasNewSuggestion(false)
                          }}
                          className="mt-2 text-xs text-primary-600 hover:text-primary-700 font-medium"
                        >
                          Ask AI Assistant →
                        </button>
                      </div>
                      
                      <button
                        onClick={() => setHasNewSuggestion(false)}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Main Trigger Button */}
            <motion.button
              onClick={handleOpen}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-14 h-14 bg-gradient-to-br from-primary-500 to-primary-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center relative group"
            >
              <MessageCircle className="w-6 h-6" />
              
              {/* Pulse animation for new suggestions */}
              {hasNewSuggestion && (
                <motion.div
                  initial={{ scale: 1, opacity: 0.7 }}
                  animate={{ scale: 1.5, opacity: 0 }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="absolute inset-0 bg-primary-500 rounded-full"
                />
              )}
              
              {/* Tooltip */}
              <div className="absolute bottom-full right-0 mb-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
                <div className="bg-gray-900 text-white text-xs px-3 py-2 rounded-lg whitespace-nowrap">
                  AI Travel Assistant
                  <div className="absolute top-full right-3 w-2 h-2 bg-gray-900 transform rotate-45 -mt-1" />
                </div>
              </div>
            </motion.button>

            {/* Quick Action Buttons */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5 }}
              className="absolute bottom-16 right-0 space-y-2"
            >
              <motion.button
                whileHover={{ scale: 1.05, x: -5 }}
                onClick={() => {
                  setIsOpen(true)
                  // Pass specific message for itinerary planning
                }}
                className="w-10 h-10 bg-white text-primary-600 rounded-full shadow-md hover:shadow-lg transition-all duration-200 flex items-center justify-center group"
                title="Plan Itinerary"
              >
                <Calendar className="w-4 h-4" />
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05, x: -5 }}
                onClick={() => {
                  setIsOpen(true)
                  // Pass specific message for experiences
                }}
                className="w-10 h-10 bg-white text-primary-600 rounded-full shadow-md hover:shadow-lg transition-all duration-200 flex items-center justify-center group"
                title="Find Experiences"
              >
                <Star className="w-4 h-4" />
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05, x: -5 }}
                onClick={() => {
                  setIsOpen(true)
                  // Pass specific message for destinations
                }}
                className="w-10 h-10 bg-white text-primary-600 rounded-full shadow-md hover:shadow-lg transition-all duration-200 flex items-center justify-center group"
                title="Explore Destinations"
              >
                <MapPin className="w-4 h-4" />
              </motion.button>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Minimized State */}
      <AnimatePresence>
        {isOpen && isMinimized && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className="fixed bottom-6 right-6 z-40"
          >
            <motion.button
              onClick={handleMaximize}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-14 h-14 bg-gradient-to-br from-primary-500 to-primary-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center relative"
            >
              <Maximize2 className="w-5 h-5" />
              
              {/* Active indicator */}
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white" />
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>

      {/* AI Assistant Modal */}
      <AnimatePresence>
        {isOpen && !isMinimized && (
          <ConversationalAI
            isOpen={isOpen}
            onClose={handleClose}
            initialMessage={initialMessage}
          />
        )}
      </AnimatePresence>
    </>
  )
}

// Hook for programmatic AI assistant interactions
export function useAIAssistant() {
  const [isOpen, setIsOpen] = useState(false)
  const [initialMessage, setInitialMessage] = useState<string>()

  const openWithMessage = (message: string) => {
    setInitialMessage(message)
    setIsOpen(true)
  }

  const open = () => setIsOpen(true)
  const close = () => setIsOpen(false)

  return {
    isOpen,
    open,
    close,
    openWithMessage,
    AIAssistantComponent: () => (
      <ConversationalAI
        isOpen={isOpen}
        onClose={close}
        initialMessage={initialMessage}
      />
    )
  }
}
