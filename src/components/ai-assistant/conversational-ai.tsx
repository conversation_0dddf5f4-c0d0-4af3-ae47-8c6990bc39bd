'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  MessageCircle,
  Send,
  Mic,
  MicOff,
  X,
  Bot,
  User,
  MapPin,
  Calendar,
  Star,
  Clock,
  DollarSign,
  Camera,
  Utensils,
  Bed,
  Plane,
  Heart,
  ThumbsUp,
  ThumbsDown,
  Copy,
  Share2,
  <PERSON>rk<PERSON>
} from 'lucide-react'
import { useCountryContext } from '@/lib/country-context'
import { useAuth } from '@/lib/auth/auth-context'

interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  suggestions?: string[]
  quickActions?: QuickAction[]
  metadata?: {
    intent?: string
    entities?: Record<string, any>
    confidence?: number
  }
}

interface QuickAction {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  action: () => void
  variant?: 'primary' | 'secondary'
}

interface ConversationalAIProps {
  isOpen: boolean
  onClose: () => void
  initialMessage?: string
}

export function ConversationalAI({ isOpen, onClose, initialMessage }: ConversationalAIProps) {
  const { currentCountry } = useCountryContext()
  const { user } = useAuth()
  const [messages, setMessages] = useState<Message[]>([])
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [isListening, setIsListening] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Initialize conversation
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: 'welcome',
        type: 'assistant',
        content: `Hello${user ? ` ${user.name}` : ''}! 👋 I'm your AI travel assistant for ${currentCountry.displayName}. I can help you plan activities, find experiences, get cultural insights, and answer any questions about your trip. What would you like to explore?`,
        timestamp: new Date(),
        suggestions: [
          'Plan a 3-day itinerary',
          'Find authentic experiences',
          'Cultural etiquette tips',
          'Best photography spots',
          'Local food recommendations'
        ]
      }
      setMessages([welcomeMessage])
    }
  }, [isOpen, currentCountry, user, messages.length])

  // Handle initial message
  useEffect(() => {
    if (initialMessage && isOpen) {
      handleSendMessage(initialMessage)
    }
  }, [initialMessage, isOpen])

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Focus input when opened
  useEffect(() => {
    if (isOpen) {
      setTimeout(() => inputRef.current?.focus(), 100)
    }
  }, [isOpen])

  const handleSendMessage = async (content: string = inputValue) => {
    if (!content.trim()) return

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: content.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsTyping(true)

    // Simulate AI processing delay
    setTimeout(() => {
      const response = generateAIResponse(content.trim())
      setMessages(prev => [...prev, response])
      setIsTyping(false)
    }, 1000 + Math.random() * 2000)
  }

  const generateAIResponse = (userInput: string): Message => {
    const input = userInput.toLowerCase()
    const countryCode = currentCountry.code.toLowerCase()
    
    // Intent detection and response generation
    if (input.includes('itinerary') || input.includes('plan') || input.includes('days')) {
      return {
        id: `ai-${Date.now()}`,
        type: 'assistant',
        content: `I'd love to help you plan your ${currentCountry.displayName} itinerary! 🗺️ Based on your interests, here's what I recommend:\n\n**Day 1: Cultural Immersion**\n• Morning: Explore the historic medina\n• Afternoon: Traditional craft workshops\n• Evening: Authentic local dining\n\n**Day 2: Natural Wonders**\n• Full day: Desert/mountain excursion\n• Sunset: Photography at scenic viewpoints\n\n**Day 3: Local Experiences**\n• Morning: Market visits and shopping\n• Afternoon: Cooking class or cultural activity\n\nWould you like me to customize this based on your specific interests?`,
        timestamp: new Date(),
        suggestions: ['Customize for adventure', 'Focus on culture', 'Add food experiences', 'Include photography'],
        quickActions: [
          {
            id: 'view-itinerary',
            label: 'View Full Itinerary',
            icon: Calendar,
            action: () => window.open(`/${countryCode}/trip-builder`, '_blank')
          },
          {
            id: 'book-experiences',
            label: 'Book Experiences',
            icon: Star,
            action: () => window.open(`/${countryCode}/experiences`, '_blank'),
            variant: 'primary'
          }
        ]
      }
    }

    if (input.includes('food') || input.includes('eat') || input.includes('restaurant')) {
      return {
        id: `ai-${Date.now()}`,
        type: 'assistant',
        content: `🍽️ Excellent choice! ${currentCountry.displayName} has incredible cuisine. Here are my top recommendations:\n\n**Must-Try Dishes:**\n• Tagine - slow-cooked stew with aromatic spices\n• Couscous - traditional Friday meal\n• Pastilla - sweet and savory pastry\n• Mint tea - the national drink\n\n**Best Food Experiences:**\n• Cooking classes with local families\n• Street food tours in the medina\n• Traditional market visits\n• Rooftop dining with city views\n\nI can help you find specific restaurants or book food experiences!`,
        timestamp: new Date(),
        suggestions: ['Find cooking classes', 'Street food tours', 'Fine dining', 'Vegetarian options'],
        quickActions: [
          {
            id: 'food-experiences',
            label: 'Food Experiences',
            icon: Utensils,
            action: () => window.open(`/${countryCode}/experiences?category=culinary`, '_blank')
          }
        ]
      }
    }

    if (input.includes('culture') || input.includes('etiquette') || input.includes('customs')) {
      return {
        id: `ai-${Date.now()}`,
        type: 'assistant',
        content: `🕌 Great question! Understanding local culture enhances your experience. Here are key cultural insights for ${currentCountry.displayName}:\n\n**Cultural Etiquette:**\n• Dress modestly, especially in religious sites\n• Use your right hand for greetings and eating\n• Remove shoes when entering homes\n• Friday is the holy day (12-2 PM prayer time)\n\n**Cultural Highlights:**\n• Traditional music and dance performances\n• Artisan workshops (pottery, weaving, metalwork)\n• Historic architecture and Islamic art\n• Berber heritage and traditions\n\nWould you like specific cultural experiences or etiquette for certain situations?`,
        timestamp: new Date(),
        suggestions: ['Religious site etiquette', 'Traditional crafts', 'Music and dance', 'Berber culture'],
        quickActions: [
          {
            id: 'cultural-guide',
            label: 'Cultural Guide',
            icon: Star,
            action: () => window.open(`/${countryCode}/cultural-guide`, '_blank')
          }
        ]
      }
    }

    if (input.includes('photo') || input.includes('camera') || input.includes('instagram')) {
      return {
        id: `ai-${Date.now()}`,
        type: 'assistant',
        content: `📸 Perfect! ${currentCountry.displayName} is incredibly photogenic. Here are the best spots and tips:\n\n**Iconic Photo Locations:**\n• Blue streets of Chefchaouen\n• Sahara Desert dunes at sunrise/sunset\n• Colorful souks and markets\n• Traditional riads and architecture\n\n**Photography Tips:**\n• Golden hour: 1 hour before sunset\n• Ask permission before photographing people\n• Respect photography restrictions in religious sites\n• Bring extra batteries (desert conditions)\n\n**Best Times:**\n• Early morning: fewer crowds, soft light\n• Late afternoon: golden hour magic\n• Blue hour: just after sunset\n\nWant specific location recommendations or photography tours?`,
        timestamp: new Date(),
        suggestions: ['Photography tours', 'Sunrise spots', 'Architecture photos', 'Portrait tips'],
        quickActions: [
          {
            id: 'photo-spots',
            label: 'Photo Locations',
            icon: Camera,
            action: () => window.open(`/${countryCode}/destinations?view=photography`, '_blank')
          }
        ]
      }
    }

    if (input.includes('budget') || input.includes('cost') || input.includes('price') || input.includes('money')) {
      return {
        id: `ai-${Date.now()}`,
        type: 'assistant',
        content: `💰 Let me help you budget for your ${currentCountry.displayName} trip!\n\n**Daily Budget Estimates (per person):**\n• Budget: €30-50/day\n• Mid-range: €50-100/day\n• Luxury: €100+/day\n\n**Cost Breakdown:**\n• Accommodation: €15-150/night\n• Meals: €10-40/day\n• Activities: €20-80/experience\n• Transportation: €5-30/day\n\n**Money-Saving Tips:**\n• Stay in riads or guesthouses\n• Eat at local restaurants\n• Use shared transportation\n• Book experiences directly with locals\n\nWould you like help finding budget-friendly options or specific pricing?`,
        timestamp: new Date(),
        suggestions: ['Budget accommodations', 'Free activities', 'Local transport', 'Group discounts'],
        quickActions: [
          {
            id: 'budget-planner',
            label: 'Budget Calculator',
            icon: DollarSign,
            action: () => window.open(`/${countryCode}/trip-builder?tab=budget`, '_blank')
          }
        ]
      }
    }

    // Default response
    return {
      id: `ai-${Date.now()}`,
      type: 'assistant',
      content: `I understand you're asking about "${userInput}". I'm here to help with all aspects of your ${currentCountry.displayName} trip! I can assist with:\n\n• **Trip Planning** - Itineraries and recommendations\n• **Cultural Insights** - Local customs and etiquette\n• **Food & Dining** - Restaurant recommendations and food tours\n• **Activities** - Experiences and attractions\n• **Photography** - Best spots and timing\n• **Budget Planning** - Cost estimates and money-saving tips\n\nWhat specific aspect would you like to explore?`,
      timestamp: new Date(),
      suggestions: ['Plan my itinerary', 'Cultural tips', 'Food recommendations', 'Photography spots', 'Budget planning'],
      metadata: {
        intent: 'general_inquiry',
        confidence: 0.7
      }
    }
  }

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const toggleVoiceInput = () => {
    setIsListening(!isListening)
    // Voice input implementation would go here
  }

  if (!isOpen) return null

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.95, y: 20 }}
      transition={{ duration: 0.2 }}
      className="fixed bottom-4 right-4 w-96 h-[600px] bg-white rounded-2xl shadow-2xl border border-gray-200 flex flex-col z-50 overflow-hidden"
    >
      {/* Header */}
      <div className="bg-gradient-to-r from-primary-500 to-primary-600 text-white p-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
            <Bot className="w-5 h-5" />
          </div>
          <div>
            <h3 className="font-semibold">AI Travel Assistant</h3>
            <p className="text-xs opacity-90">{currentCountry.displayName} Expert</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="text-white hover:bg-white hover:bg-opacity-20 p-1 rounded-full transition-colors"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            {message.type === 'assistant' && (
              <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <Bot className="w-4 h-4 text-primary-600" />
              </div>
            )}
            
            <div className={`max-w-[80%] ${message.type === 'user' ? 'order-1' : ''}`}>
              <div
                className={`p-3 rounded-2xl ${
                  message.type === 'user'
                    ? 'bg-primary-500 text-white ml-auto'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                <p className="text-sm whitespace-pre-line">{message.content}</p>
              </div>
              
              <div className="text-xs text-gray-500 mt-1 px-1">
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>

              {/* Suggestions */}
              {message.suggestions && message.suggestions.length > 0 && (
                <div className="mt-3 space-y-2">
                  {message.suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="block w-full text-left p-2 text-xs bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors border border-gray-200"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}

              {/* Quick Actions */}
              {message.quickActions && message.quickActions.length > 0 && (
                <div className="mt-3 space-y-2">
                  {message.quickActions.map((action) => (
                    <button
                      key={action.id}
                      onClick={action.action}
                      className={`flex items-center gap-2 p-2 text-xs rounded-lg transition-colors w-full ${
                        action.variant === 'primary'
                          ? 'bg-primary-500 text-white hover:bg-primary-600'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      <action.icon className="w-4 h-4" />
                      {action.label}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {message.type === 'user' && (
              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <User className="w-4 h-4 text-gray-600" />
              </div>
            )}
          </div>
        ))}

        {/* Typing indicator */}
        {isTyping && (
          <div className="flex gap-3">
            <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center flex-shrink-0">
              <Bot className="w-4 h-4 text-primary-600" />
            </div>
            <div className="bg-gray-100 p-3 rounded-2xl">
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about your trip..."
              className="w-full p-3 pr-12 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
            />
            <button
              onClick={toggleVoiceInput}
              className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full transition-colors ${
                isListening ? 'text-red-500 bg-red-50' : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              {isListening ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
            </button>
          </div>
          <button
            onClick={() => handleSendMessage()}
            disabled={!inputValue.trim() || isTyping}
            className="p-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </div>
    </motion.div>
  )
}
