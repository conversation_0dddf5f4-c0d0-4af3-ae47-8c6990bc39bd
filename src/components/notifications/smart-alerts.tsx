/**
 * CONTEXTUAL SMART NOTIFICATIONS
 * 
 * TODO LIST:
 * 
 * 1. WEATHER-BASED ALERTS
 *    - [ ] Perfect weather notifications for outdoor activities
 *    - [ ] Rain day indoor activity suggestions
 *    - [ ] Extreme weather safety alerts
 *    - [ ] Seasonal activity availability updates
 *    - [ ] Sunrise/sunset timing for photography
 * 
 * 2. EVENT & FESTIVAL NOTIFICATIONS
 *    - [ ] Local festival happening during visit dates
 *    - [ ] Traditional market days and times
 *    - [ ] Cultural celebrations and ceremonies
 *    - [ ] Music and arts events
 *    - [ ] Religious observances and impacts
 * 
 * 3. PRICING & AVAILABILITY ALERTS
 *    - [ ] Price drop notifications for saved experiences
 *    - [ ] Last-minute availability openings
 *    - [ ] Group discount opportunities
 *    - [ ] Early bird special offers
 *    - [ ] Seasonal pricing changes
 * 
 * 4. PERSONALIZED RECOMMENDATIONS
 *    - [ ] "Similar travelers recommend..." notifications
 *    - [ ] Duration optimization suggestions
 *    - [ ] Budget reallocation recommendations
 *    - [ ] Experience category balancing
 *    - [ ] Local guide availability alerts
 * 
 * 5. REAL-TIME TRAVEL UPDATES
 *    - [ ] Transportation delays and alternatives
 *    - [ ] Road condition updates
 *    - [ ] Border crossing information
 *    - [ ] Local safety and security updates
 *    - [ ] Emergency contact information
 * 
 * 6. SOCIAL & COMMUNITY ALERTS
 *    - [ ] Friends planning similar trips
 *    - [ ] Travel buddy matching opportunities
 *    - [ ] Community meetup notifications
 *    - [ ] Local guide recommendations
 *    - [ ] Group formation opportunities
 * 
 * 7. SMART TIMING SUGGESTIONS
 *    - [ ] Optimal visit times for attractions
 *    - [ ] Crowd level predictions
 *    - [ ] Photography golden hour alerts
 *    - [ ] Restaurant reservation reminders
 *    - [ ] Activity booking deadlines
 * 
 * COMPONENTS TO CREATE:
 * - NotificationCenter
 * - AlertBanner
 * - WeatherAlert
 * - PriceAlert
 * - EventNotification
 * - SmartSuggestion
 */

'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Sun,
  Cloud,
  CloudRain,
  Snowflake,
  Wind,
  Calendar,
  MapPin,
  Clock,
  TrendingDown,
  Users,
  Camera,
  Utensils,
  Shield,
  AlertTriangle,
  Info,
  CheckCircle,
  X,
  Bell,
  Star
} from 'lucide-react'
import { useCountryContext } from '@/lib/country-context'

interface SmartAlert {
  id: string
  type: 'weather' | 'event' | 'pricing' | 'safety' | 'cultural' | 'optimization'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  title: string
  message: string
  actionText?: string
  actionUrl?: string
  icon: React.ComponentType<{ className?: string }>
  color: string
  bgColor: string
  expiresAt?: Date
  location?: string
  metadata?: Record<string, any>
}

export function SmartAlerts() {
  const { currentCountry } = useCountryContext()
  const [alerts, setAlerts] = useState<SmartAlert[]>([])
  const [dismissedAlerts, setDismissedAlerts] = useState<Set<string>>(new Set())

  useEffect(() => {
    // Generate contextual alerts based on current country and conditions
    const generateAlerts = () => {
      const currentAlerts: SmartAlert[] = []
      const now = new Date()
      const hour = now.getHours()

      // Weather-based alerts
      if (currentCountry.code === 'MA') {
        // Morocco-specific alerts
        if (hour >= 6 && hour <= 8) {
          currentAlerts.push({
            id: 'weather-perfect-morning',
            type: 'weather',
            priority: 'medium',
            title: '☀️ Perfect Morning Weather',
            message: 'Ideal conditions for exploring Marrakech medina - 22°C with clear skies!',
            actionText: 'View Activities',
            actionUrl: '/ma/destinations/marrakech',
            icon: Sun,
            color: 'text-yellow-600',
            bgColor: 'bg-yellow-50',
            expiresAt: new Date(now.getTime() + 3 * 60 * 60 * 1000), // 3 hours
            location: 'Marrakech'
          })
        }

        if (hour >= 17 && hour <= 19) {
          currentAlerts.push({
            id: 'golden-hour-photography',
            type: 'weather',
            priority: 'medium',
            title: '📸 Golden Hour Alert',
            message: 'Perfect lighting for photography at Hassan II Mosque - golden hour until sunset!',
            actionText: 'Photo Spots',
            actionUrl: '/ma/destinations/casablanca',
            icon: Camera,
            color: 'text-orange-600',
            bgColor: 'bg-orange-50',
            expiresAt: new Date(now.getTime() + 2 * 60 * 60 * 1000),
            location: 'Casablanca'
          })
        }

        // Cultural events
        const dayOfWeek = now.getDay()
        if (dayOfWeek === 5) { // Friday
          currentAlerts.push({
            id: 'friday-prayers',
            type: 'cultural',
            priority: 'high',
            title: '🕌 Cultural Awareness',
            message: 'Friday prayers (12:00-14:00) - Many shops close. Perfect time to visit gardens or museums.',
            actionText: 'Alternative Activities',
            actionUrl: '/ma/cultural-guide',
            icon: Clock,
            color: 'text-purple-600',
            bgColor: 'bg-purple-50',
            location: 'All Cities'
          })
        }

        // Ramadan considerations (mock - would be calculated based on Islamic calendar)
        if (Math.random() > 0.8) {
          currentAlerts.push({
            id: 'ramadan-respect',
            type: 'cultural',
            priority: 'high',
            title: '🌙 Ramadan Etiquette',
            message: 'During Ramadan, avoid eating/drinking in public during daylight hours. Iftar experiences available!',
            actionText: 'Learn More',
            actionUrl: '/ma/cultural-guide/ramadan',
            icon: Star,
            color: 'text-indigo-600',
            bgColor: 'bg-indigo-50'
          })
        }
      }

      // Pricing alerts
      if (Math.random() > 0.7) {
        currentAlerts.push({
          id: 'price-drop-alert',
          type: 'pricing',
          priority: 'high',
          title: '💰 Price Drop Alert',
          message: 'Desert camping experience dropped by 25% - Save €40 on your Sahara adventure!',
          actionText: 'Book Now',
          actionUrl: '/ma/experiences/sahara-desert',
          icon: TrendingDown,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          expiresAt: new Date(now.getTime() + 24 * 60 * 60 * 1000) // 24 hours
        })
      }

      // Safety alerts
      if (currentCountry.code === 'MA' && Math.random() > 0.9) {
        currentAlerts.push({
          id: 'safety-reminder',
          type: 'safety',
          priority: 'medium',
          title: '🛡️ Safety Reminder',
          message: 'Your local guide Ahmed is available 24/7 at +212-XXX-XXXX for any assistance.',
          actionText: 'Save Contact',
          actionUrl: '/emergency-contacts',
          icon: Shield,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50'
        })
      }

      // Event notifications
      if (Math.random() > 0.6) {
        currentAlerts.push({
          id: 'local-festival',
          type: 'event',
          priority: 'medium',
          title: '🎉 Local Festival',
          message: 'Traditional Gnawa music festival happening in Essaouira this weekend!',
          actionText: 'Learn More',
          actionUrl: '/ma/destinations/essaouira',
          icon: Calendar,
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          location: 'Essaouira'
        })
      }

      // Optimization suggestions
      if (Math.random() > 0.8) {
        currentAlerts.push({
          id: 'trip-optimization',
          type: 'optimization',
          priority: 'low',
          title: '⚡ Trip Optimization',
          message: 'Rearranging your Fes activities could save 2 hours of travel time.',
          actionText: 'Optimize Route',
          actionUrl: '/trip-builder?optimize=true',
          icon: MapPin,
          color: 'text-indigo-600',
          bgColor: 'bg-indigo-50'
        })
      }

      // Filter out dismissed and expired alerts
      const validAlerts = currentAlerts.filter(alert => {
        if (dismissedAlerts.has(alert.id)) return false
        if (alert.expiresAt && alert.expiresAt < now) return false
        return true
      })

      setAlerts(validAlerts)
    }

    generateAlerts()

    // Refresh alerts every 30 minutes
    const interval = setInterval(generateAlerts, 30 * 60 * 1000)
    return () => clearInterval(interval)
  }, [currentCountry, dismissedAlerts])

  const dismissAlert = (alertId: string) => {
    setDismissedAlerts(prev => new Set([...prev, alertId]))
    setAlerts(prev => prev.filter(alert => alert.id !== alertId))
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent': return <AlertTriangle className="w-4 h-4" />
      case 'high': return <Bell className="w-4 h-4" />
      case 'medium': return <Info className="w-4 h-4" />
      default: return <CheckCircle className="w-4 h-4" />
    }
  }

  if (alerts.length === 0) return null

  return (
    <div className="space-y-3">
      <AnimatePresence>
        {alerts.map((alert, index) => (
          <motion.div
            key={alert.id}
            initial={{ opacity: 0, y: -20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -20, scale: 0.95 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className={`${alert.bgColor} border border-opacity-20 rounded-lg p-4 shadow-sm`}
          >
            <div className="flex items-start gap-3">
              <div className={`${alert.color} mt-0.5`}>
                <alert.icon className="w-5 h-5" />
              </div>

              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-gray-900 text-sm">{alert.title}</h4>
                      <div className={`${alert.color} opacity-70`}>
                        {getPriorityIcon(alert.priority)}
                      </div>
                    </div>

                    <p className="text-gray-700 text-sm leading-relaxed mb-3">
                      {alert.message}
                    </p>

                    <div className="flex items-center justify-between">
                      {alert.actionText && alert.actionUrl && (
                        <a
                          href={alert.actionUrl}
                          className={`${alert.color} hover:underline text-sm font-medium`}
                        >
                          {alert.actionText} →
                        </a>
                      )}

                      {alert.location && (
                        <span className="text-xs text-gray-500 flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {alert.location}
                        </span>
                      )}
                    </div>
                  </div>

                  <button
                    onClick={() => dismissAlert(alert.id)}
                    className="text-gray-400 hover:text-gray-600 transition-colors p-1"
                    aria-label="Dismiss alert"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}