'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, ArrowRight, ArrowLeft, MapPin, Calendar, Users, Heart, Star, Sparkles } from 'lucide-react'

interface JourneyBuilderModalProps {
  isOpen: boolean
  onClose: () => void
}

type Step = 'preferences' | 'destinations' | 'experiences' | 'timeline' | 'summary'

interface JourneyData {
  travelStyle: string[]
  interests: string[]
  destinations: string[]
  experiences: string[]
  duration: string
  travelers: number
  budget: string
  culturalComfort: string
}

export function JourneyBuilderModal({ isOpen, onClose }: JourneyBuilderModalProps) {
  const [currentStep, setCurrentStep] = useState<Step>('preferences')
  const [journeyData, setJourneyData] = useState<JourneyData>({
    travelStyle: [],
    interests: [],
    destinations: [],
    experiences: [],
    duration: '',
    travelers: 2,
    budget: '',
    culturalComfort: ''
  })

  const steps: { key: Step; title: string; subtitle: string }[] = [
    { key: 'preferences', title: 'Your Travel Style', subtitle: 'Tell us what makes you excited about travel' },
    { key: 'destinations', title: 'Dream Destinations', subtitle: 'Which places call to your heart?' },
    { key: 'experiences', title: 'Unique Experiences', subtitle: 'What memories do you want to create?' },
    { key: 'timeline', title: 'Trip Details', subtitle: 'When and how long is your perfect journey?' },
    { key: 'summary', title: 'Your Journey', subtitle: 'Let\'s bring your Morocco adventure to life' }
  ]

  const currentStepIndex = steps.findIndex(step => step.key === currentStep)

  const travelStyles = [
    { id: 'luxury', name: 'Luxury Explorer', desc: 'Premium accommodations & exclusive experiences', icon: '✨' },
    { id: 'authentic', name: 'Cultural Immersion', desc: 'Deep local connections & traditional experiences', icon: '🏛️' },
    { id: 'adventure', name: 'Adventure Seeker', desc: 'Thrilling activities & off-the-beaten-path', icon: '🏔️' },
    { id: 'relaxed', name: 'Relaxed Wanderer', desc: 'Leisurely pace with comfort & flexibility', icon: '🌅' },
    { id: 'family', name: 'Family Explorer', desc: 'Kid-friendly activities & family bonding', icon: '👨‍👩‍👧‍👦' },
    { id: 'romantic', name: 'Romantic Escape', desc: 'Intimate moments & couple experiences', icon: '💕' }
  ]

  const interests = [
    { id: 'history', name: 'History & Heritage', icon: '🏛️' },
    { id: 'food', name: 'Culinary Adventures', icon: '🍽️' },
    { id: 'art', name: 'Arts & Crafts', icon: '🎨' },
    { id: 'nature', name: 'Nature & Landscapes', icon: '🌿' },
    { id: 'photography', name: 'Photography', icon: '📸' },
    { id: 'wellness', name: 'Wellness & Spa', icon: '🧘‍♀️' },
    { id: 'shopping', name: 'Shopping & Souks', icon: '🛍️' },
    { id: 'music', name: 'Music & Dance', icon: '🎵' }
  ]

  const destinations = [
    { 
      id: 'marrakech', 
      name: 'Marrakech', 
      desc: 'The Red City of Palaces',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=300&fit=crop',
      highlights: ['Medina', 'Souks', 'Palaces']
    },
    { 
      id: 'fes', 
      name: 'Fes', 
      desc: 'Imperial City of Crafts',
      image: 'https://images.unsplash.com/photo-1570026517541-7c6c7c6c6c6c?w=400&h=300&fit=crop',
      highlights: ['Medina', 'Tanneries', 'Mosques']
    },
    { 
      id: 'sahara', 
      name: 'Sahara Desert', 
      desc: 'Golden Ocean of Dunes',
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=400&h=300&fit=crop',
      highlights: ['Camel Trek', 'Stargazing', 'Camps']
    },
    { 
      id: 'casablanca', 
      name: 'Casablanca', 
      desc: 'Modern Morocco Gateway',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=300&fit=crop',
      highlights: ['Hassan II', 'Corniche', 'Modern']
    },
    { 
      id: 'chefchaouen', 
      name: 'Chefchaouen', 
      desc: 'The Blue Pearl',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=300&fit=crop',
      highlights: ['Blue Streets', 'Mountains', 'Crafts']
    },
    { 
      id: 'essaouira', 
      name: 'Essaouira', 
      desc: 'Coastal Wind City',
      image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=400&h=300&fit=crop',
      highlights: ['Beach', 'Medina', 'Windsurfing']
    }
  ]

  const experiences = [
    { id: 'cooking', name: 'Cooking Classes', desc: 'Learn traditional Moroccan cuisine', icon: '👨‍🍳' },
    { id: 'hammam', name: 'Traditional Hammam', desc: 'Authentic spa experience', icon: '🛁' },
    { id: 'camel', name: 'Camel Trekking', desc: 'Desert adventure on camelback', icon: '🐪' },
    { id: 'crafts', name: 'Artisan Workshops', desc: 'Learn traditional crafts', icon: '🏺' },
    { id: 'music', name: 'Gnawa Music', desc: 'Traditional music experiences', icon: '🎵' },
    { id: 'gardens', name: 'Garden Tours', desc: 'Explore beautiful gardens', icon: '🌺' },
    { id: 'markets', name: 'Souk Adventures', desc: 'Navigate bustling markets', icon: '🏪' },
    { id: 'stargazing', name: 'Desert Stargazing', desc: 'Astronomical experiences', icon: '⭐' }
  ]

  const handleNext = () => {
    const nextIndex = currentStepIndex + 1
    if (nextIndex < steps.length) {
      setCurrentStep(steps[nextIndex].key)
    }
  }

  const handlePrevious = () => {
    const prevIndex = currentStepIndex - 1
    if (prevIndex >= 0) {
      setCurrentStep(steps[prevIndex].key)
    }
  }

  const toggleSelection = (category: keyof JourneyData, value: string) => {
    setJourneyData(prev => {
      const currentArray = prev[category] as string[]
      const isSelected = currentArray.includes(value)
      
      return {
        ...prev,
        [category]: isSelected 
          ? currentArray.filter(item => item !== value)
          : [...currentArray, value]
      }
    })
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 'preferences':
        return (
          <div className="space-y-8">
            <div>
              <h3 className="heading-md text-neutral-900 mb-6">What's your travel style?</h3>
              <div className="grid md:grid-cols-2 gap-4">
                {travelStyles.map((style) => (
                  <motion.button
                    key={style.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => toggleSelection('travelStyle', style.id)}
                    className={`p-6 rounded-2xl border-2 transition-all duration-200 text-left ${
                      journeyData.travelStyle.includes(style.id)
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-neutral-200 hover:border-primary-300'
                    }`}
                  >
                    <div className="flex items-start gap-4">
                      <span className="text-2xl">{style.icon}</span>
                      <div>
                        <h4 className="font-semibold text-neutral-900 mb-1">{style.name}</h4>
                        <p className="text-sm text-neutral-600">{style.desc}</p>
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            <div>
              <h3 className="heading-md text-neutral-900 mb-6">What interests you most?</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {interests.map((interest) => (
                  <motion.button
                    key={interest.id}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => toggleSelection('interests', interest.id)}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 text-center ${
                      journeyData.interests.includes(interest.id)
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-neutral-200 hover:border-primary-300'
                    }`}
                  >
                    <span className="text-2xl mb-2 block">{interest.icon}</span>
                    <span className="text-sm font-medium text-neutral-700">{interest.name}</span>
                  </motion.button>
                ))}
              </div>
            </div>
          </div>
        )

      case 'destinations':
        return (
          <div className="space-y-6">
            <h3 className="heading-md text-neutral-900 mb-6">Which destinations call to you?</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {destinations.map((destination) => (
                <motion.button
                  key={destination.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => toggleSelection('destinations', destination.id)}
                  className={`relative rounded-2xl overflow-hidden border-3 transition-all duration-200 ${
                    journeyData.destinations.includes(destination.id)
                      ? 'border-primary-500 ring-4 ring-primary-100'
                      : 'border-transparent hover:border-primary-300'
                  }`}
                >
                  <div className="aspect-[4/3] relative">
                    <img
                      src={destination.image}
                      alt={destination.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                    
                    {journeyData.destinations.includes(destination.id) && (
                      <div className="absolute top-3 right-3 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                        <Heart className="w-4 h-4 text-white fill-current" />
                      </div>
                    )}
                    
                    <div className="absolute bottom-4 left-4 right-4 text-white">
                      <h4 className="font-bold text-lg mb-1">{destination.name}</h4>
                      <p className="text-sm opacity-90 mb-2">{destination.desc}</p>
                      <div className="flex flex-wrap gap-1">
                        {destination.highlights.map((highlight, i) => (
                          <span key={i} className="text-xs bg-white/20 px-2 py-1 rounded-full">
                            {highlight}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>
          </div>
        )

      case 'experiences':
        return (
          <div className="space-y-6">
            <h3 className="heading-md text-neutral-900 mb-6">What experiences excite you?</h3>
            <div className="grid md:grid-cols-2 gap-4">
              {experiences.map((experience) => (
                <motion.button
                  key={experience.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => toggleSelection('experiences', experience.id)}
                  className={`p-6 rounded-2xl border-2 transition-all duration-200 text-left ${
                    journeyData.experiences.includes(experience.id)
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-neutral-200 hover:border-primary-300'
                  }`}
                >
                  <div className="flex items-center gap-4">
                    <span className="text-3xl">{experience.icon}</span>
                    <div>
                      <h4 className="font-semibold text-neutral-900 mb-1">{experience.name}</h4>
                      <p className="text-sm text-neutral-600">{experience.desc}</p>
                    </div>
                  </div>
                </motion.button>
              ))}
            </div>
          </div>
        )

      case 'timeline':
        return (
          <div className="space-y-8">
            <div>
              <h3 className="heading-md text-neutral-900 mb-6">Trip Duration</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {['3-5 days', '1 week', '2 weeks', '3+ weeks'].map((duration) => (
                  <button
                    key={duration}
                    onClick={() => setJourneyData(prev => ({ ...prev, duration }))}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                      journeyData.duration === duration
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-neutral-200 hover:border-primary-300'
                    }`}
                  >
                    <Calendar className="w-6 h-6 mx-auto mb-2 text-primary-600" />
                    <span className="text-sm font-medium">{duration}</span>
                  </button>
                ))}
              </div>
            </div>

            <div>
              <h3 className="heading-md text-neutral-900 mb-6">Number of Travelers</h3>
              <div className="flex items-center gap-4">
                <button
                  onClick={() => setJourneyData(prev => ({ ...prev, travelers: Math.max(1, prev.travelers - 1) }))}
                  className="w-12 h-12 rounded-full border-2 border-neutral-200 hover:border-primary-300 flex items-center justify-center"
                >
                  -
                </button>
                <div className="flex items-center gap-3">
                  <Users className="w-6 h-6 text-primary-600" />
                  <span className="text-2xl font-bold text-neutral-900">{journeyData.travelers}</span>
                </div>
                <button
                  onClick={() => setJourneyData(prev => ({ ...prev, travelers: prev.travelers + 1 }))}
                  className="w-12 h-12 rounded-full border-2 border-neutral-200 hover:border-primary-300 flex items-center justify-center"
                >
                  +
                </button>
              </div>
            </div>

            <div>
              <h3 className="heading-md text-neutral-900 mb-6">Budget Range</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {['Budget-friendly', 'Mid-range', 'Luxury'].map((budget) => (
                  <button
                    key={budget}
                    onClick={() => setJourneyData(prev => ({ ...prev, budget }))}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                      journeyData.budget === budget
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-neutral-200 hover:border-primary-300'
                    }`}
                  >
                    <span className="font-medium">{budget}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        )

      case 'summary':
        return (
          <div className="space-y-8">
            <div className="text-center">
              <Sparkles className="w-16 h-16 text-primary-500 mx-auto mb-4" />
              <h3 className="heading-md text-neutral-900 mb-4">Your Perfect Morocco Journey</h3>
              <p className="text-body text-neutral-600">
                Based on your preferences, we've crafted a personalized itinerary just for you.
              </p>
            </div>

            <div className="bg-gradient-to-br from-primary-50 to-sand-50 rounded-2xl p-8">
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h4 className="font-semibold text-neutral-900 mb-4">Your Journey Highlights</h4>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <MapPin className="w-5 h-5 text-primary-600" />
                      <span className="text-sm">{journeyData.destinations.length} destinations selected</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Calendar className="w-5 h-5 text-primary-600" />
                      <span className="text-sm">{journeyData.duration} adventure</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Users className="w-5 h-5 text-primary-600" />
                      <span className="text-sm">{journeyData.travelers} travelers</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Star className="w-5 h-5 text-primary-600" />
                      <span className="text-sm">{journeyData.experiences.length} unique experiences</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-neutral-900 mb-4">What's Next?</h4>
                  <ul className="space-y-2 text-sm text-neutral-600">
                    <li>• Connect with your dedicated Morocco expert</li>
                    <li>• Receive a detailed personalized itinerary</li>
                    <li>• Book accommodations and experiences</li>
                    <li>• Get 24/7 support during your journey</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="text-center">
              <button className="btn-primary btn-lg">
                Connect with Your Morocco Expert
                <ArrowRight className="ml-3 h-5 w-5" />
              </button>
              <p className="text-sm text-neutral-500 mt-3">
                Free consultation • No commitment required
              </p>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex min-h-screen items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative w-full max-w-4xl bg-white rounded-3xl shadow-strong overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-primary-600 to-primary-700 px-8 py-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="heading-md text-white mb-2">{steps[currentStepIndex].title}</h2>
                  <p className="text-primary-100">{steps[currentStepIndex].subtitle}</p>
                </div>
                <button
                  onClick={onClose}
                  className="w-10 h-10 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center transition-colors duration-200"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              {/* Progress */}
              <div className="mt-6">
                <div className="flex items-center gap-2 mb-2">
                  {steps.map((step, index) => (
                    <div
                      key={step.key}
                      className={`h-2 rounded-full flex-1 transition-all duration-300 ${
                        index <= currentStepIndex ? 'bg-white' : 'bg-white/30'
                      }`}
                    />
                  ))}
                </div>
                <p className="text-sm text-primary-100">
                  Step {currentStepIndex + 1} of {steps.length}
                </p>
              </div>
            </div>

            {/* Content */}
            <div className="p-8 max-h-[60vh] overflow-y-auto">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {renderStepContent()}
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Footer */}
            <div className="border-t border-neutral-200 px-8 py-6 flex items-center justify-between">
              <button
                onClick={handlePrevious}
                disabled={currentStepIndex === 0}
                className="btn-ghost disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Previous
              </button>

              {currentStep !== 'summary' ? (
                <button
                  onClick={handleNext}
                  className="btn-primary"
                >
                  Next Step
                  <ArrowRight className="ml-2 h-4 w-4" />
                </button>
              ) : (
                <button
                  onClick={onClose}
                  className="btn-accent"
                >
                  Start Planning
                  <Sparkles className="ml-2 h-4 w-4" />
                </button>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}