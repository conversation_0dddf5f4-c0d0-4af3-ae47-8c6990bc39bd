'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { LucideIcon } from 'lucide-react'

interface Tab {
  id: string
  label: string
  icon?: LucideIcon
  badge?: string | number
  disabled?: boolean
  className?: string
}

interface EnhancedTabNavigationProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
  variant?: 'default' | 'pills' | 'underline' | 'cards'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function EnhancedTabNavigation({
  tabs,
  activeTab,
  onTabChange,
  variant = 'default',
  size = 'md',
  className = ''
}: EnhancedTabNavigationProps) {
  const [hoveredTab, setHoveredTab] = useState<string | null>(null)

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg'
  }

  const getTabClasses = (tab: Tab, isActive: boolean, isHovered: boolean) => {
    const baseClasses = `relative flex items-center gap-2 font-medium transition-all duration-300 ${sizeClasses[size]} ${tab.className || ''}`
    
    if (tab.disabled) {
      return `${baseClasses} text-gray-400 cursor-not-allowed`
    }

    switch (variant) {
      case 'pills':
        return `${baseClasses} rounded-full ${
          isActive 
            ? 'bg-gradient-to-r from-orange-500 to-red-600 text-white shadow-lg' 
            : isHovered
            ? 'bg-orange-50 text-orange-700'
            : 'text-gray-600 hover:text-orange-600'
        }`
      
      case 'underline':
        return `${baseClasses} border-b-2 ${
          isActive 
            ? 'border-orange-500 text-orange-600' 
            : 'border-transparent text-gray-600 hover:text-orange-600 hover:border-orange-200'
        }`
      
      case 'cards':
        return `${baseClasses} rounded-xl border ${
          isActive 
            ? 'bg-gradient-to-r from-orange-500 to-red-600 text-white border-orange-500 shadow-lg' 
            : isHovered
            ? 'bg-orange-50 text-orange-700 border-orange-200'
            : 'bg-white text-gray-600 border-gray-200 hover:border-orange-200 hover:text-orange-600'
        }`
      
      default:
        return `${baseClasses} ${
          isActive 
            ? 'text-orange-600' 
            : 'text-gray-600 hover:text-orange-600'
        }`
    }
  }

  return (
    <div className={`flex items-center overflow-x-auto scrollbar-hide ${className}`}>
      {/* Mobile-friendly scrollable container */}
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>

      {variant === 'underline' ? (
        <div className="relative flex border-b border-gray-200">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id
            const isHovered = hoveredTab === tab.id
            const Icon = tab.icon

            return (
              <button
                key={tab.id}
                onClick={() => !tab.disabled && onTabChange(tab.id)}
                onMouseEnter={() => setHoveredTab(tab.id)}
                onMouseLeave={() => setHoveredTab(null)}
                className={getTabClasses(tab, isActive, isHovered)}
                disabled={tab.disabled}
              >
                {Icon && <Icon className="w-4 h-4" />}
                <span>{tab.label}</span>
                {tab.badge && (
                  <span className={`px-2 py-0.5 text-xs rounded-full ${
                    isActive 
                      ? 'bg-orange-100 text-orange-800' 
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {tab.badge}
                  </span>
                )}
                
                {/* Active indicator for underline variant */}
                {isActive && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-orange-500 to-red-600"
                    initial={false}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  />
                )}
              </button>
            )
          })}
        </div>
      ) : (
        <div className={`flex items-center flex-nowrap ${
          variant === 'pills' || variant === 'cards' ? 'gap-1 sm:gap-2' : 'gap-0.5 sm:gap-1'
        }`}>
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id
            const isHovered = hoveredTab === tab.id
            const Icon = tab.icon

            return (
              <motion.button
                key={tab.id}
                onClick={() => !tab.disabled && onTabChange(tab.id)}
                onMouseEnter={() => setHoveredTab(tab.id)}
                onMouseLeave={() => setHoveredTab(null)}
                className={getTabClasses(tab, isActive, isHovered)}
                disabled={tab.disabled}
                whileHover={!tab.disabled ? { scale: 1.02 } : {}}
                whileTap={!tab.disabled ? { scale: 0.98 } : {}}
                layout
              >
                {Icon && <Icon className="w-4 h-4" />}
                <span>{tab.label}</span>
                {tab.badge && (
                  <motion.span 
                    className={`px-2 py-0.5 text-xs rounded-full ${
                      isActive 
                        ? variant === 'pills' || variant === 'cards'
                          ? 'bg-white/20 text-white' 
                          : 'bg-orange-100 text-orange-800'
                        : 'bg-gray-100 text-gray-600'
                    }`}
                    layout
                  >
                    {tab.badge}
                  </motion.span>
                )}
              </motion.button>
            )
          })}
        </div>
      )}
    </div>
  )
}

// Usage example component
export function TabNavigationExample() {
  const [activeTab, setActiveTab] = useState('overview')

  const tabs = [
    { id: 'overview', label: 'Overview', badge: '3' },
    { id: 'itinerary', label: 'Itinerary', badge: '7' },
    { id: 'experiences', label: 'Experiences' },
    { id: 'map', label: 'Map' },
    { id: 'reviews', label: 'Reviews', disabled: true }
  ]

  return (
    <div className="space-y-8 p-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Default Tabs</h3>
        <EnhancedTabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Pills Variant</h3>
        <EnhancedTabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          variant="pills"
        />
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Underline Variant</h3>
        <EnhancedTabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          variant="underline"
        />
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Cards Variant</h3>
        <EnhancedTabNavigation
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          variant="cards"
        />
      </div>
    </div>
  )
}