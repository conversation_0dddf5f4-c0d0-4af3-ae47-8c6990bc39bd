'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Shield,
  LayoutDashboard,
  Globe,
  Users,
  FileText,
  BarChart3,
  Settings,
  MapPin,
  Star,
  UserCheck,
  ChevronDown,
  ChevronRight,
  Palette,
  Database,
  Eye,
  CheckCircle,
  Image,
  Upload,
  Languages,
  Layers,
  Building2,
  MessageSquare
} from 'lucide-react'
import { useSuperAdmin } from '@/lib/auth/super-admin-auth'

interface NavigationItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  permission?: string
  children?: NavigationItem[]
}

export function SuperAdminSidebar() {
  const pathname = usePathname()
  const { user, hasPermission } = useSuperAdmin()
  const [expandedItems, setExpandedItems] = useState<string[]>(['content'])

  const navigation: NavigationItem[] = [
    {
      name: 'Dashboard',
      href: '/super-admin/dashboard',
      icon: LayoutDashboard,
    },
    {
      name: 'Countries',
      href: '/super-admin/countries',
      icon: Globe,
      permission: 'canManageCountries',
      children: [
        {
          name: 'All Countries',
          href: '/super-admin/countries',
          icon: Globe,
        },
        {
          name: 'Add Country',
          href: '/super-admin/countries/add',
          icon: Globe,
        },
        {
          name: 'Country Settings',
          href: '/super-admin/countries/settings',
          icon: Settings,
        }
      ]
    },
    {
      name: 'Content Management',
      href: '/super-admin/content',
      icon: FileText,
      permission: 'canManageContent',
      children: [
        {
          name: 'Destinations',
          href: '/super-admin/content/destinations',
          icon: MapPin,
        },
        {
          name: 'Experiences',
          href: '/super-admin/content/experiences',
          icon: Star,
        },
        {
          name: 'Cultural Content',
          href: '/super-admin/content/cultural',
          icon: Palette,
        },
        {
          name: 'Media Library',
          href: '/super-admin/content/media',
          icon: Database,
        }
      ]
    },
    {
      name: 'Media Library',
      href: '/super-admin/media',
      icon: Image,
      permission: 'canManageContent',
    },
    {
      name: 'Import/Export',
      href: '/super-admin/import-export',
      icon: Upload,
      permission: 'canManageContent',
    },
    {
      name: 'Templates',
      href: '/super-admin/templates',
      icon: Layers,
      permission: 'canManageContent',
    },
    {
      name: 'Languages',
      href: '/super-admin/languages',
      icon: Languages,
      permission: 'canManageContent',
    },
    {
      name: 'Partner Management',
      href: '/super-admin/partners',
      icon: Users,
      permission: 'canManageContent',
      children: [
        {
          name: 'Local Guides',
          href: '/super-admin/guides',
          icon: Users,
        },
        {
          name: 'Experience Providers',
          href: '/super-admin/providers',
          icon: Building2,
        },
        {
          name: 'Reviews & Ratings',
          href: '/super-admin/reviews',
          icon: Star,
        },
        {
          name: 'Communications',
          href: '/super-admin/communications',
          icon: MessageSquare,
        },
      ],
    },
    {
      name: 'User Management',
      href: '/super-admin/users',
      icon: Users,
      permission: 'canManageUsers',
      children: [
        {
          name: 'All Users',
          href: '/super-admin/users',
          icon: Users,
        },
        {
          name: 'Admin Users',
          href: '/super-admin/users/admins',
          icon: UserCheck,
        },
        {
          name: 'Roles & Permissions',
          href: '/super-admin/users/roles',
          icon: Shield,
        }
      ]
    },
    {
      name: 'Analytics',
      href: '/super-admin/analytics',
      icon: BarChart3,
      permission: 'canViewAnalytics',
      children: [
        {
          name: 'Global Metrics',
          href: '/super-admin/analytics/global',
          icon: BarChart3,
        },
        {
          name: 'Country Analytics',
          href: '/super-admin/analytics/countries',
          icon: Globe,
        },
        {
          name: 'User Analytics',
          href: '/super-admin/analytics/users',
          icon: Users,
        }
      ]
    },
    {
      name: 'Settings',
      href: '/super-admin/settings',
      icon: Settings,
    }
  ]

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/')
  }

  const canAccessItem = (item: NavigationItem) => {
    if (!item.permission) return true
    return hasPermission(item.permission as any)
  }

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    if (!canAccessItem(item)) return null

    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.name)
    const active = isActive(item.href)

    return (
      <div key={item.name}>
        <div className="relative">
          {hasChildren ? (
            <button
              onClick={() => toggleExpanded(item.name)}
              className={`w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                active
                  ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-500'
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
              } ${level > 0 ? 'ml-4' : ''}`}
            >
              <div className="flex items-center gap-3">
                <item.icon className={`w-5 h-5 ${active ? 'text-blue-600' : 'text-slate-400'}`} />
                <span>{item.name}</span>
              </div>
              {isExpanded ? (
                <ChevronDown className="w-4 h-4" />
              ) : (
                <ChevronRight className="w-4 h-4" />
              )}
            </button>
          ) : (
            <Link
              href={item.href}
              className={`flex items-center gap-3 px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                active
                  ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-500'
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
              } ${level > 0 ? 'ml-4' : ''}`}
            >
              <item.icon className={`w-5 h-5 ${active ? 'text-blue-600' : 'text-slate-400'}`} />
              <span>{item.name}</span>
            </Link>
          )}
        </div>

        {/* Children */}
        <AnimatePresence>
          {hasChildren && isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="mt-1 space-y-1">
                {item.children?.map(child => renderNavigationItem(child, level + 1))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }

  return (
    <div className="fixed inset-y-0 left-0 z-50 w-72 bg-white shadow-xl border-r border-slate-200">
      {/* Header */}
      <div className="flex items-center gap-3 px-6 py-4 border-b border-slate-200">
        <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
          <Shield className="w-6 h-6 text-white" />
        </div>
        <div>
          <h1 className="text-lg font-bold text-slate-900">Super Admin</h1>
          <p className="text-xs text-slate-500">Administrative Portal</p>
        </div>
      </div>

      {/* User Info */}
      {user && (
        <div className="px-6 py-4 border-b border-slate-200">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-slate-200 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-slate-600">
                {user.name.charAt(0).toUpperCase()}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-slate-900 truncate">{user.name}</p>
              <p className="text-xs text-slate-500 truncate">{user.role.replace('_', ' ')}</p>
            </div>
          </div>
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-2 overflow-y-auto">
        {navigation.map(item => renderNavigationItem(item))}
      </nav>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-slate-200">
        <p className="text-xs text-slate-500 text-center">
          © 2024 Come to Morocco
        </p>
      </div>
    </div>
  )
}
