'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'
import {
  Globe,
  MapPin,
  Star,
  Users,
  Calendar,
  Thermometer,
  DollarSign,
  BookOpen,
  MessageCircle,
  Loader2,
  Search,
  Filter
} from 'lucide-react'
import { useCountryData, useWeatherData, useCurrencyConverter } from '@/lib/hooks/use-country-data'

interface CountryDataDemoProps {
  countryCode: string
}

export function CountryDataDemo({ countryCode }: CountryDataDemoProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  const {
    countryData,
    destinations,
    experiences,
    guides,
    currentSeason,
    culturalCustoms,
    languagePhrases,
    formatPrice,
    isLoading,
    error,
    searchDestinations
  } = useCountryData(countryCode)

  const { weather, isLoading: isLoadingWeather } = useWeatherData(countryCode)
  const { convert, format, currency } = useCurrencyConverter(countryCode)

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-12">
        <div className="flex items-center gap-3">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="text-lg text-slate-600">Loading country data...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <h3 className="text-red-800 font-semibold mb-2">Error Loading Data</h3>
        <p className="text-red-600">{error}</p>
      </div>
    )
  }

  if (!countryData) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h3 className="text-yellow-800 font-semibold mb-2">No Data Available</h3>
        <p className="text-yellow-600">Country data for {countryCode} is not available.</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Country Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
      >
        <div className="flex items-center gap-4 mb-6">
          <span className="text-4xl">{countryData.flag}</span>
          <div>
            <h1 className="text-2xl font-bold text-slate-900">{countryData.displayName}</h1>
            <p className="text-slate-600">{countryData.code} • {countryData.currency.name}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <MapPin className="w-6 h-6 text-blue-600" />
            </div>
            <p className="text-sm text-slate-600">Destinations</p>
            <p className="text-xl font-bold text-slate-900">{destinations.length}</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Star className="w-6 h-6 text-green-600" />
            </div>
            <p className="text-sm text-slate-600">Experiences</p>
            <p className="text-xl font-bold text-slate-900">{experiences.length}</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
            <p className="text-sm text-slate-600">Local Guides</p>
            <p className="text-xl font-bold text-slate-900">{guides.length}</p>
          </div>

          <div className="text-center">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-2">
              <DollarSign className="w-6 h-6 text-orange-600" />
            </div>
            <p className="text-sm text-slate-600">Currency</p>
            <p className="text-xl font-bold text-slate-900">{currency?.symbol}</p>
          </div>
        </div>
      </motion.div>

      {/* Weather & Season Info */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
        >
          <h3 className="text-lg font-semibold text-slate-900 mb-4 flex items-center gap-2">
            <Thermometer className="w-5 h-5 text-blue-600" />
            Current Weather & Season
          </h3>

          {isLoadingWeather ? (
            <div className="flex items-center gap-2">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span className="text-slate-600">Loading weather...</span>
            </div>
          ) : weather ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-2xl font-bold text-slate-900">{weather.current.temperature}°C</p>
                  <p className="text-slate-600">{weather.current.condition}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-slate-600">Humidity</p>
                  <p className="font-semibold">{weather.current.humidity}%</p>
                </div>
              </div>

              {currentSeason && (
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-900 mb-2">{currentSeason.name}</h4>
                  <p className="text-sm text-blue-700 mb-2">{currentSeason.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {currentSeason.activities.slice(0, 3).map((activity, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                      >
                        {activity}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <p className="text-slate-600">Weather data not available</p>
          )}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
        >
          <h3 className="text-lg font-semibold text-slate-900 mb-4 flex items-center gap-2">
            <BookOpen className="w-5 h-5 text-green-600" />
            Cultural Insights
          </h3>

          <div className="space-y-4">
            {culturalCustoms.slice(0, 3).map((custom, index) => (
              <div key={custom.id} className="border-l-4 border-green-500 pl-4">
                <h4 className="font-semibold text-slate-900">{custom.title}</h4>
                <p className="text-sm text-slate-600 mt-1">{custom.description}</p>
                <span className={`inline-block mt-2 px-2 py-1 text-xs rounded-full ${
                  custom.importance === 'critical' ? 'bg-red-100 text-red-700' :
                  custom.importance === 'high' ? 'bg-orange-100 text-orange-700' :
                  'bg-blue-100 text-blue-700'
                }`}>
                  {custom.importance} importance
                </span>
              </div>
            ))}

            {culturalCustoms.length === 0 && (
              <p className="text-slate-600">Cultural content loading...</p>
            )}
          </div>
        </motion.div>
      </div>

      {/* Language Phrases */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
      >
        <h3 className="text-lg font-semibold text-slate-900 mb-4 flex items-center gap-2">
          <MessageCircle className="w-5 h-5 text-purple-600" />
          Essential Phrases
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {languagePhrases.slice(0, 6).map((phrase, index) => (
            <div key={phrase.id} className="bg-slate-50 rounded-lg p-4">
              <p className="font-semibold text-slate-900">{phrase.english}</p>
              <p className="text-lg text-slate-700 my-1">{phrase.native}</p>
              <p className="text-sm text-slate-600 italic">{phrase.pronunciation}</p>
              <span className="inline-block mt-2 px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full">
                {phrase.category}
              </span>
            </div>
          ))}
        </div>

        {languagePhrases.length === 0 && (
          <p className="text-slate-600">Language phrases loading...</p>
        )}
      </motion.div>

      {/* Destinations Preview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-slate-900 flex items-center gap-2">
            <MapPin className="w-5 h-5 text-blue-600" />
            Top Destinations
          </h3>
          
          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
              <input
                type="text"
                placeholder="Search destinations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border border-slate-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {destinations.slice(0, 6).map((destination, index) => (
            <div key={destination.id} className="border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-2">
                <h4 className="font-semibold text-slate-900">{destination.name}</h4>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  destination.difficultyLevel === 'easy' ? 'bg-green-100 text-green-700' :
                  destination.difficultyLevel === 'moderate' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-red-100 text-red-700'
                }`}>
                  {destination.difficultyLevel}
                </span>
              </div>
              
              <p className="text-sm text-slate-600 mb-3">{destination.shortDescription}</p>
              
              <div className="flex items-center justify-between text-sm">
                <span className="text-slate-500">{destination.category}</span>
                <span className="font-semibold text-slate-900">
                  {destination.durationRecommendation.recommended} days
                </span>
              </div>
            </div>
          ))}
        </div>

        {destinations.length === 0 && (
          <p className="text-slate-600 text-center py-8">No destinations available</p>
        )}
      </motion.div>

      {/* Currency Demo */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-white rounded-xl shadow-sm border border-slate-200 p-6"
      >
        <h3 className="text-lg font-semibold text-slate-900 mb-4 flex items-center gap-2">
          <DollarSign className="w-5 h-5 text-green-600" />
          Currency & Pricing
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-green-50 rounded-lg p-4">
            <h4 className="font-semibold text-green-900 mb-2">Sample Hotel Price</h4>
            <p className="text-2xl font-bold text-green-700">
              {formatPrice(150).formatted}
            </p>
            <p className="text-sm text-green-600">per night</p>
          </div>

          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 mb-2">Sample Tour Price</h4>
            <p className="text-2xl font-bold text-blue-700">
              {formatPrice(75).formatted}
            </p>
            <p className="text-sm text-blue-600">per person</p>
          </div>

          <div className="bg-purple-50 rounded-lg p-4">
            <h4 className="font-semibold text-purple-900 mb-2">Sample Meal Price</h4>
            <p className="text-2xl font-bold text-purple-700">
              {formatPrice(25).formatted}
            </p>
            <p className="text-sm text-purple-600">per person</p>
          </div>
        </div>

        {currency && (
          <div className="mt-4 p-4 bg-slate-50 rounded-lg">
            <p className="text-sm text-slate-600">
              <strong>Currency:</strong> {currency.name} ({currency.code}) • 
              <strong> Symbol:</strong> {currency.symbol} • 
              <strong> Exchange Rate:</strong> 1 USD = {currency.exchangeRate} {currency.code}
            </p>
          </div>
        )}
      </motion.div>
    </div>
  )
}
