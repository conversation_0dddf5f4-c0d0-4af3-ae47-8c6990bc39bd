'use client'

import { motion } from 'framer-motion'
import { Heart, Users, MapPin, Shield, Star, Award, Clock, Globe } from 'lucide-react'

export function FeaturesSection() {
  const features = [
    {
      icon: Heart,
      title: 'Personalized Experiences',
      description: 'AI-powered recommendations tailored to your unique travel preferences and cultural interests.',
      highlight: 'Custom Itineraries'
    },
    {
      icon: Users,
      title: 'Expert Local Guides',
      description: 'Connect with certified local experts who share insider knowledge and authentic stories.',
      highlight: 'Verified Professionals'
    },
    {
      icon: MapPin,
      title: 'Interactive Discovery',
      description: 'Explore destinations through immersive 3D maps, virtual tours, and real-time insights.',
      highlight: 'Live Exploration'
    },
    {
      icon: Shield,
      title: 'Trusted & Secure',
      description: '24/7 support, secure payments, and comprehensive travel insurance for peace of mind.',
      highlight: 'Full Protection'
    }
  ]

  const stats = [
    { icon: Star, value: '4.9/5', label: 'Customer Rating' },
    { icon: Award, value: '15+', label: 'Years Experience' },
    { icon: Clock, value: '24/7', label: 'Support Available' },
    { icon: Globe, value: '50+', label: 'Destinations' }
  ]

  return (
    <section className="section-padding bg-gradient-to-b from-neutral-50 to-white">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center gap-2 bg-primary-50 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Star className="w-4 h-4" />
            Why Choose Us
          </div>
          <h2 className="heading-lg text-neutral-900 mb-6">
            Your Gateway to
            <span className="block bg-gradient-to-r from-morocco-500 via-sand-500 to-morocco-600 bg-clip-text text-transparent">
              Authentic Morocco
            </span>
          </h2>
          <p className="text-body-lg max-w-3xl mx-auto">
            We combine cutting-edge technology with deep local expertise to create 
            unforgettable travel experiences that go beyond the ordinary.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="card-feature group cursor-pointer"
            >
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-2xl flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300">
                  <feature.icon className="h-8 w-8 text-primary-600" />
                </div>
                <div className="absolute -top-2 -right-2 bg-morocco-500 text-white text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  {feature.highlight}
                </div>
              </div>
              
              <h3 className="heading-md text-neutral-900 mb-4 group-hover:text-primary-600 transition-colors duration-300">
                {feature.title}
              </h3>
              
              <p className="text-body text-neutral-600 group-hover:text-neutral-700 transition-colors duration-300">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-3xl p-12 text-white"
        >
          <div className="text-center mb-12">
            <h3 className="heading-md text-white mb-4">
              Trusted by Thousands of Travelers
            </h3>
            <p className="text-lg text-primary-100 max-w-2xl mx-auto">
              Join our community of satisfied travelers who have discovered the magic of Morocco with us.
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                <div className="text-primary-200 text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}