'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useTrip } from '@/lib/trip-context'
import { 
  Sparkles, 
  TrendingUp, 
  Users, 
  Clock, 
  Star,
  MapPin,
  Calendar,
  Euro,
  ChevronRight,
  Plus,
  X,
  Lightbulb,
  Target,
  Award
} from 'lucide-react'

interface SmartRecommendation {
  id: string
  type: 'destination' | 'experience' | 'optimization' | 'upgrade'
  title: string
  description: string
  reason: string
  confidence: number
  impact: 'high' | 'medium' | 'low'
  category: string
  price?: number
  duration?: string
  image?: string
  actionLabel: string
  metadata: {
    popularity?: number
    seasonality?: string
    weatherScore?: number
    budgetFit?: number
  }
}

export function SmartRecommendations() {
  const { tripData } = useTrip()
  const [recommendations, setRecommendations] = useState<SmartRecommendation[]>([])
  const [activeFilter, setActiveFilter] = useState<'all' | 'destination' | 'experience' | 'optimization'>('all')
  const [dismissedIds, setDismissedIds] = useState<string[]>([])

  useEffect(() => {
    generateSmartRecommendations()
  }, [tripData])

  const generateSmartRecommendations = () => {
    const recs: SmartRecommendation[] = []

    // Analyze current trip data
    const destinations = tripData?.destinations || []
    const totalCost = tripData?.totalCost || 0
    const days = tripData?.days || []

    // 1. Destination Recommendations
    if (destinations.includes('Marrakech') && !destinations.includes('Essaouira')) {
      recs.push({
        id: 'essaouira-coastal',
        type: 'destination',
        title: 'Add Essaouira Coastal Escape',
        description: 'Perfect coastal balance to your desert and city experience',
        reason: '89% of travelers who visit Marrakech also love Essaouira\'s coastal charm',
        confidence: 92,
        impact: 'high',
        category: 'Popular Combination',
        actionLabel: 'Add Destination',
        metadata: {
          popularity: 89,
          seasonality: 'Perfect year-round',
          weatherScore: 95,
          budgetFit: 85
        }
      })
    }

    if (destinations.includes('Fes') && destinations.includes('Marrakech') && !destinations.includes('Chefchaouen')) {
      recs.push({
        id: 'chefchaouen-blue',
        type: 'destination',
        title: 'Discover the Blue Pearl',
        description: 'Chefchaouen offers stunning mountain views and Instagram-worthy blue streets',
        reason: 'Perfectly positioned between your imperial cities for a scenic mountain retreat',
        confidence: 88,
        impact: 'medium',
        category: 'Hidden Gem',
        actionLabel: 'Explore Chefchaouen',
        metadata: {
          popularity: 76,
          seasonality: 'Best in spring/fall',
          weatherScore: 88,
          budgetFit: 92
        }
      })
    }

    // 2. Experience Recommendations
    if (destinations.includes('Sahara')) {
      recs.push({
        id: 'sahara-stargazing',
        type: 'experience',
        title: 'Astronomical Desert Experience',
        description: 'Professional stargazing with telescopes in one of the world\'s darkest skies',
        reason: 'Sahara offers world-class stargazing - don\'t miss this once-in-a-lifetime opportunity',
        confidence: 95,
        impact: 'high',
        category: 'Unique Experience',
        price: 95,
        duration: '3 hours',
        image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
        actionLabel: 'Add Experience',
        metadata: {
          popularity: 94,
          seasonality: 'Best Oct-Mar',
          weatherScore: 98
        }
      })
    }

    if (destinations.includes('Marrakech')) {
      recs.push({
        id: 'marrakech-cooking',
        type: 'experience',
        title: 'Berber Family Cooking Class',
        description: 'Learn authentic recipes in a local family home with market tour',
        reason: 'Highly rated cultural experience that 96% of travelers call "life-changing"',
        confidence: 91,
        impact: 'high',
        category: 'Cultural Immersion',
        price: 75,
        duration: '4 hours',
        image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',
        actionLabel: 'Book Experience',
        metadata: {
          popularity: 96,
          seasonality: 'Available year-round',
          weatherScore: 90
        }
      })
    }

    // 3. Trip Optimization Recommendations
    if (days.length > 0 && totalCost > 0) {
      const avgCostPerDay = totalCost / days.length
      if (avgCostPerDay < 100) {
        recs.push({
          id: 'budget-optimization',
          type: 'optimization',
          title: 'Upgrade Your Experience Level',
          description: 'Add premium experiences within your budget for unforgettable memories',
          reason: `Your current daily budget of €${avgCostPerDay.toFixed(0)} allows for luxury upgrades`,
          confidence: 85,
          impact: 'medium',
          category: 'Budget Optimization',
          actionLabel: 'See Upgrades',
          metadata: {
            budgetFit: 95
          }
        })
      }
    }

    if (destinations.length >= 3) {
      recs.push({
        id: 'transport-optimization',
        type: 'optimization',
        title: 'Optimize Your Route',
        description: 'Rearrange destinations for better flow and reduced travel time',
        reason: 'Smart routing can save 2-3 hours of travel time and reduce costs by 15%',
        confidence: 87,
        impact: 'medium',
        category: 'Route Optimization',
        actionLabel: 'Optimize Route',
        metadata: {
          budgetFit: 88
        }
      })
    }

    // 4. Seasonal and Weather-based Recommendations
    const currentMonth = new Date().getMonth()
    if (currentMonth >= 2 && currentMonth <= 4) { // Spring
      recs.push({
        id: 'spring-festival',
        type: 'experience',
        title: 'Spring Festival Experience',
        description: 'Attend the Rose Festival in Kelaat M\'Gouna during your visit',
        reason: 'Perfect timing! The famous Rose Festival happens during your travel dates',
        confidence: 93,
        impact: 'high',
        category: 'Seasonal Special',
        price: 45,
        duration: 'Full day',
        actionLabel: 'Add Festival',
        metadata: {
          seasonality: 'Spring only',
          popularity: 87
        }
      })
    }

    setRecommendations(recs.filter(rec => !dismissedIds.includes(rec.id)))
  }

  const filteredRecommendations = recommendations.filter(rec => 
    activeFilter === 'all' || rec.type === activeFilter
  )

  const dismissRecommendation = (id: string) => {
    setDismissedIds(prev => [...prev, id])
    setRecommendations(prev => prev.filter(rec => rec.id !== id))
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-green-600 bg-green-100'
      case 'medium': return 'text-yellow-600 bg-yellow-100'
      case 'low': return 'text-blue-600 bg-blue-100'
      default: return 'text-neutral-600 bg-neutral-100'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'destination': return MapPin
      case 'experience': return Star
      case 'optimization': return Target
      case 'upgrade': return Award
      default: return Lightbulb
    }
  }

  if (recommendations.length === 0) {
    return null
  }

  return (
    <div className="bg-gradient-to-br from-primary-50 to-sand-50 rounded-2xl p-6 border border-primary-100">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="font-bold text-neutral-900">Smart Recommendations</h3>
            <p className="text-sm text-neutral-600">AI-powered suggestions for your trip</p>
          </div>
        </div>

        <div className="flex bg-white rounded-lg p-1 shadow-sm">
          {['all', 'destination', 'experience', 'optimization'].map((filter) => (
            <button
              key={filter}
              onClick={() => setActiveFilter(filter as any)}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-all duration-200 capitalize ${
                activeFilter === filter
                  ? 'bg-primary-500 text-white'
                  : 'text-neutral-600 hover:text-neutral-900'
              }`}
            >
              {filter}
            </button>
          ))}
        </div>
      </div>

      <div className="space-y-4">
        <AnimatePresence>
          {filteredRecommendations.map((rec, index) => {
            const TypeIcon = getTypeIcon(rec.type)
            
            return (
              <motion.div
                key={rec.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, x: -100 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white rounded-xl p-4 shadow-sm border border-neutral-100 hover:shadow-md transition-all duration-300"
              >
                <div className="flex items-start gap-4">
                  {rec.image && (
                    <img
                      src={rec.image}
                      alt={rec.title}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <TypeIcon className="w-4 h-4 text-primary-500" />
                        <h4 className="font-semibold text-neutral-900">{rec.title}</h4>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getImpactColor(rec.impact)}`}>
                          {rec.impact} impact
                        </span>
                      </div>
                      
                      <button
                        onClick={() => dismissRecommendation(rec.id)}
                        className="text-neutral-400 hover:text-neutral-600 transition-colors"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                    
                    <p className="text-neutral-600 text-sm mb-2">{rec.description}</p>
                    
                    <div className="flex items-center gap-4 text-xs text-neutral-500 mb-3">
                      <div className="flex items-center gap-1">
                        <TrendingUp className="w-3 h-3" />
                        <span>{rec.confidence}% confidence</span>
                      </div>
                      {rec.metadata.popularity && (
                        <div className="flex items-center gap-1">
                          <Users className="w-3 h-3" />
                          <span>{rec.metadata.popularity}% popularity</span>
                        </div>
                      )}
                      {rec.price && (
                        <div className="flex items-center gap-1">
                          <Euro className="w-3 h-3" />
                          <span>€{rec.price}</span>
                        </div>
                      )}
                      {rec.duration && (
                        <div className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          <span>{rec.duration}</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="bg-primary-50 rounded-lg p-3 mb-3">
                      <p className="text-sm text-primary-700">
                        <strong>Why this matters:</strong> {rec.reason}
                      </p>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium text-neutral-500 bg-neutral-100 px-2 py-1 rounded-full">
                        {rec.category}
                      </span>
                      
                      <button className="btn-primary btn-sm flex items-center gap-2">
                        <Plus className="w-3 h-3" />
                        {rec.actionLabel}
                        <ChevronRight className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                </div>
              </motion.div>
            )
          })}
        </AnimatePresence>
      </div>

      {filteredRecommendations.length === 0 && (
        <div className="text-center py-8">
          <Lightbulb className="w-12 h-12 text-neutral-300 mx-auto mb-3" />
          <p className="text-neutral-500">No recommendations for this category yet.</p>
          <p className="text-sm text-neutral-400">Add more destinations to get personalized suggestions!</p>
        </div>
      )}
    </div>
  )
}