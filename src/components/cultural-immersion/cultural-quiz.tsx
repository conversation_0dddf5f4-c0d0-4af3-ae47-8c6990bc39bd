'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Award,
  BookO<PERSON>,
  Brain,
  CheckCircle,
  ChevronRight,
  Clock,
  Crown,
  Globe,
  Heart,
  Music,
  Palette,
  Star,
  Trophy,
  X
} from 'lucide-react'
import { ProgressiveDisclosure } from '@/components/ui/progressive-disclosure'

interface QuizQuestion {
  id: string
  type: 'history' | 'music' | 'architecture' | 'cuisine' | 'symbols'
  question: string
  options: string[]
  correctAnswer: number
  explanation: string
  image?: string
  points: number
}

interface UserProgress {
  totalScore: number
  level: number
  streak: number
  completedQuizzes: string[]
  immersionScore: number
}

const quizQuestions: QuizQuestion[] = [
  {
    id: 'history-1',
    type: 'history',
    question: 'Which dynasty built the famous Koutoubia Mosque in Marrakech?',
    options: ['Almoravid', 'Almohad', 'Saadian', 'Marinid'],
    correctAnswer: 1,
    explanation: 'The Almohad dynasty built the Koutoubia Mosque in the 12th century. It became a model for Moroccan mosque architecture.',
    image: 'https://images.unsplash.com/photo-1548018560-c7196548e84d?w=400&h=200&fit=crop',
    points: 100
  },
  {
    id: 'history-2',
    type: 'history',
    question: 'When did Morocco gain independence from France?',
    options: ['1954', '1956', '1958', '1960'],
    correctAnswer: 1,
    explanation: 'Morocco gained independence from France on March 2, 1956, under King Mohammed V.',
    points: 75
  },
  {
    id: 'music-1',
    type: 'music',
    question: 'What is the traditional Moroccan drum called?',
    options: ['Oud', 'Qanun', 'Tbel', 'Gimbri'],
    correctAnswer: 2,
    explanation: 'The Tbel is a traditional Moroccan drum, often used in Berber music and celebrations.',
    points: 75
  },
  {
    id: 'music-2',
    type: 'music',
    question: 'Which music style is traditional to the Gnawa people of Morocco?',
    options: ['Chaabi', 'Gnawa', 'Andalusi', 'Berber'],
    correctAnswer: 1,
    explanation: 'Gnawa music combines spiritual songs with dancing, and has its roots in sub-Saharan Africa.',
    points: 100
  },
  {
    id: 'architecture-1',
    type: 'architecture',
    question: 'What is the distinctive feature of Moroccan riads?',
    options: [
      'Tall minarets',
      'Central courtyard with fountain',
      'Colorful domes',
      'Large windows facing street'
    ],
    correctAnswer: 1,
    explanation: 'Riads are traditional Moroccan houses with interior gardens or courtyards, designed for privacy and family life.',
    image: 'https://images.unsplash.com/photo-1558005530-a7958896ec60?w=400&h=200&fit=crop',
    points: 125
  },
  {
    id: 'architecture-2',
    type: 'architecture',
    question: 'What is the traditional Moroccan mosaic tilework called?',
    options: ['Tadelakt', 'Zellige', 'Stucco', 'Mashrabiya'],
    correctAnswer: 1,
    explanation: 'Zellige is the art of geometric mosaic tilework made from hand-cut terra cotta tiles.',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop',
    points: 95
  },
  {
    id: 'cuisine-1',
    type: 'cuisine',
    question: 'Which spice is essential in authentic Moroccan tagines?',
    options: ['Turmeric', 'Paprika', 'Ras el Hanout', 'Cinnamon'],
    correctAnswer: 2,
    explanation: 'Ras el Hanout is a complex spice blend that gives Moroccan tagines their distinctive flavor. It can contain 10-30 different spices.',
    points: 90
  },
  {
    id: 'cuisine-2',
    type: 'cuisine',
    question: 'What is the traditional Moroccan tea ceremony drink?',
    options: ['Black tea with milk', 'Green tea with mint', 'Herbal tea', 'Iced tea'],
    correctAnswer: 1,
    explanation: 'Moroccan mint tea (Atay) is made with green tea, fresh mint, and sugar, served in small glasses.',
    image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=200&fit=crop',
    points: 80
  },
  {
    id: 'symbols-1',
    type: 'symbols',
    question: 'What does the Hand of Fatima (Khamsa) symbol represent?',
    options: [
      'Wealth and prosperity',
      'Protection against evil eye',
      'Love and marriage',
      'Harvest and fertility'
    ],
    correctAnswer: 1,
    explanation: 'The Hand of Fatima is a protective symbol believed to ward off evil eye and bring good fortune.',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=200&fit=crop',
    points: 80
  },
  {
    id: 'symbols-2',
    type: 'symbols',
    question: 'What do the colors of the Moroccan flag represent?',
    options: [
      'Red for courage, green star for Islam',
      'Red for blood, green for nature',
      'Red for desert, green for oases',
      'Red for strength, green for peace'
    ],
    correctAnswer: 0,
    explanation: 'The red represents the courage of Moroccan people, while the green pentagram represents Islam and the five pillars of faith.',
    points: 70
  }
]

export function CulturalQuiz() {
  const [currentQuestion, setCurrentQuestion] = useState<QuizQuestion | null>(null)
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null)
  const [showResult, setShowResult] = useState(false)
  const [userProgress, setUserProgress] = useState<UserProgress>({
    totalScore: 0,
    level: 1,
    streak: 0,
    completedQuizzes: [],
    immersionScore: 0
  })

  // Load progress from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('culturalProgress')
    if (saved) {
      setUserProgress(JSON.parse(saved))
    }
  }, [])

  // Save progress to localStorage
  useEffect(() => {
    localStorage.setItem('culturalProgress', JSON.stringify(userProgress))
  }, [userProgress])

  const startQuiz = (type: string) => {
    const availableQuestions = quizQuestions.filter(
      q => q.type === type && !userProgress.completedQuizzes.includes(q.id)
    )
    
    if (availableQuestions.length > 0) {
      setCurrentQuestion(availableQuestions[0])
      setSelectedAnswer(null)
      setShowResult(false)
    }
  }

  const handleAnswer = (answerIndex: number) => {
    if (selectedAnswer !== null) return
    
    setSelectedAnswer(answerIndex)
    setShowResult(true)
    
    if (answerIndex === currentQuestion?.correctAnswer) {
      const newProgress = {
        ...userProgress,
        totalScore: userProgress.totalScore + currentQuestion.points,
        completedQuizzes: [...userProgress.completedQuizzes, currentQuestion.id],
        streak: userProgress.streak + 1,
        level: Math.floor((userProgress.totalScore + currentQuestion.points) / 500) + 1,
        immersionScore: Math.min(100, userProgress.immersionScore + 10)
      }
      
      setUserProgress(newProgress)
    }
  }

  const resetProgress = () => {
    const newProgress = {
      totalScore: 0,
      level: 1,
      streak: 0,
      completedQuizzes: [],
      immersionScore: 0
    }
    setUserProgress(newProgress)
    setCurrentQuestion(null)
    setSelectedAnswer(null)
    setShowResult(false)
  }

  if (currentQuestion) {
    return (
      <div className="max-w-2xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-2xl shadow-lg p-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-2xl font-bold text-neutral-900 mb-2">
                Cultural Knowledge Quiz
              </h3>
              <p className="text-neutral-600">
                {currentQuestion.type.charAt(0).toUpperCase() + currentQuestion.type.slice(1)} Category
              </p>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-green-600">{currentQuestion.points} pts</div>
              <p className="text-sm text-neutral-600">Available</p>
            </div>
          </div>

          {currentQuestion.image && (
            <div className="mb-6">
              <img
                src={currentQuestion.image}
                alt="Question visual"
                className="w-full h-48 object-cover rounded-xl"
              />
            </div>
          )}

          <div className="mb-6">
            <h4 className="text-xl font-semibold text-neutral-900 mb-4">
              {currentQuestion.question}
            </h4>

            <div className="space-y-3">
              {currentQuestion.options.map((option, index) => (
                <motion.button
                  key={index}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleAnswer(index)}
                  disabled={selectedAnswer !== null}
                  className={`w-full p-4 text-left rounded-xl border-2 transition-all duration-200 ${
                    selectedAnswer === null
                      ? 'border-neutral-200 hover:border-green-500 hover:bg-green-50'
                      : index === currentQuestion.correctAnswer
                      ? 'border-green-500 bg-green-50'
                      : selectedAnswer === index
                      ? 'border-red-500 bg-red-50'
                      : 'border-neutral-200 opacity-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{option}</span>
                    {selectedAnswer !== null && index === currentQuestion.correctAnswer && (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    )}
                    {selectedAnswer === index && index !== currentQuestion.correctAnswer && (
                      <X className="w-5 h-5 text-red-600" />
                    )}
                  </div>
                </motion.button>
              ))}
            </div>
          </div>

          <AnimatePresence>
            {showResult && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="bg-blue-50 rounded-xl p-4"
              >
                <div className="flex items-start gap-3">
                  <Brain className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h5 className="font-semibold text-blue-900 mb-2">
                      {selectedAnswer === currentQuestion.correctAnswer ? 'Correct!' : 'Not quite right'}
                    </h5>
                    <p className="text-blue-700 text-sm">{currentQuestion.explanation}</p>
                  </div>
                </div>
                
                <button
                  onClick={() => setCurrentQuestion(null)}
                  className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Continue Learning
                </button>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Progress Header */}
      <div className="bg-gradient-to-r from-teal-500 to-blue-600 rounded-2xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-2xl font-bold mb-2">Cultural Immersion Tracker</h3>
            <p className="text-teal-100">Learn about Moroccan culture through interactive quizzes</p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">{userProgress.totalScore}</div>
            <div className="text-sm text-teal-100">Total Points</div>
          </div>
        </div>

        <div className="mt-4">
          <div className="flex justify-between text-sm mb-2">
            <span>Level {userProgress.level}</span>
            <span>{userProgress.immersionScore}% Immersion</span>
          </div>
          <div className="w-full h-2 bg-teal-400/30 rounded-full">
            <div 
              className="h-2 bg-white rounded-full transition-all duration-500"
              style={{ width: `${userProgress.immersionScore}%` }}
            />
          </div>
        </div>
      </div>

      {/* Quiz Categories */}
      <div className="grid md:grid-cols-3 gap-4">
        {['history', 'music', 'architecture', 'cuisine', 'symbols'].map((type) => {
          const completed = quizQuestions.filter(
            q => q.type === type && userProgress.completedQuizzes.includes(q.id)
          ).length
          const total = quizQuestions.filter(q => q.type === type).length
          const isCompleted = completed === total
          
          const categoryInfo = {
            history: { 
              title: 'Historical Timeline', 
              description: 'Learn about Morocco\'s rich history',
              color: 'bg-blue-100 text-blue-600',
              icon: BookOpen 
            },
            music: { 
              title: 'Traditional Music', 
              description: 'Discover Moroccan musical heritage',
              color: 'bg-purple-100 text-purple-600',
              icon: Music 
            },
            architecture: { 
              title: 'Architectural Styles', 
              description: 'Explore Islamic architecture',
              color: 'bg-orange-100 text-orange-600',
              icon: Palette 
            },
            cuisine: { 
              title: 'Regional Cuisine', 
              description: 'Master Moroccan flavors',
              color: 'bg-green-100 text-green-600',
              icon: Globe 
            },
            symbols: { 
              title: 'Cultural Symbols', 
              description: 'Understand symbolic meanings',
              color: 'bg-pink-100 text-pink-600',
              icon: Heart 
            }
          }
          
          const info = categoryInfo[type as keyof typeof categoryInfo]
          
          return (
            <motion.div
              key={type}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className={`bg-white rounded-xl p-6 shadow-lg cursor-pointer border-2 transition-all duration-200 ${
                isCompleted ? 'border-green-500 bg-green-50' : 'border-transparent hover:border-teal-300'
              }`}
              onClick={() => startQuiz(type)}
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-lg ${info.color}`}>
                  <info.icon className="w-6 h-6" />
                </div>
                <div className="flex items-center gap-2">
                  {isCompleted && <Trophy className="w-5 h-5 text-yellow-500" />}
                  <ChevronRight className="w-5 h-5 text-neutral-400" />
                </div>
              </div>
              
              <h4 className="font-semibold text-neutral-900 mb-1">{info.title}</h4>
              <p className="text-sm text-neutral-500 mb-3">{info.description}</p>
              <p className="text-sm text-neutral-600 mb-3 font-medium">
                {completed}/{total} completed
                {isCompleted && <span className="text-green-600 ml-2">✓ Mastered!</span>}
              </p>
              
              <div className="w-full h-3 bg-neutral-200 rounded-full overflow-hidden">
                <div 
                  className={`h-3 rounded-full transition-all duration-500 ${
                    isCompleted ? 'bg-green-500' : 'bg-teal-500'
                  }`}
                  style={{ width: `${(completed / total) * 100}%` }}
                />
              </div>
              
              {completed > 0 && (
                <div className="mt-3 flex items-center gap-2">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span className="text-sm text-neutral-600">
                    {quizQuestions
                      .filter(q => q.type === type && userProgress.completedQuizzes.includes(q.id))
                      .reduce((sum, q) => sum + q.points, 0)} points earned
                  </span>
                </div>
              )}
            </motion.div>
          )
        })}
      </div>

      {/* Progress Stats */}
      <div className="bg-white rounded-xl p-6 shadow-lg">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-lg font-semibold text-neutral-900">Your Progress</h4>
          <button
            onClick={resetProgress}
            className="text-sm text-red-600 hover:text-red-700"
          >
            Reset Progress
          </button>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-teal-600">{userProgress.level}</div>
            <div className="text-sm text-neutral-600">Level</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{userProgress.streak}</div>
            <div className="text-sm text-neutral-600">Day Streak</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{userProgress.completedQuizzes.length}</div>
            <div className="text-sm text-neutral-600">Quizzes Done</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{userProgress.immersionScore}%</div>
            <div className="text-sm text-neutral-600">Immersion</div>
          </div>
        </div>
      </div>

      {/* Progressive Disclosure for Advanced Cultural Learning */}
      <div className="mt-8">
        <ProgressiveDisclosure
          title="Advanced Cultural Learning Resources"
          subtitle="Deepen your understanding of Moroccan culture"
          icon={BookOpen}
          variant="card"
        >
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-neutral-900 flex items-center gap-2">
                  <Music className="w-4 h-4 text-purple-500" />
                  Music & Arts Deep Dive
                </h4>
                <div className="space-y-3 text-sm text-neutral-700">
                  <div className="p-3 bg-purple-50 rounded-lg">
                    <strong>Gnawa Music:</strong> Spiritual music tradition combining African rhythms with Islamic mysticism. Often performed during healing ceremonies.
                  </div>
                  <div className="p-3 bg-indigo-50 rounded-lg">
                    <strong>Andalusi Music:</strong> Classical tradition from Al-Andalus, featuring complex poetic lyrics and sophisticated musical structures.
                  </div>
                  <div className="p-3 bg-pink-50 rounded-lg">
                    <strong>Berber Music:</strong> Indigenous music of the Amazigh people, often featuring call-and-response patterns and traditional instruments.
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-neutral-900 flex items-center gap-2">
                  <Palette className="w-4 h-4 text-orange-500" />
                  Architectural Heritage
                </h4>
                <div className="space-y-3 text-sm text-neutral-700">
                  <div className="p-3 bg-orange-50 rounded-lg">
                    <strong>Islamic Geometry:</strong> Mathematical patterns in Moroccan architecture represent the infinite nature of Allah and create meditative spaces.
                  </div>
                  <div className="p-3 bg-red-50 rounded-lg">
                    <strong>Riad Design:</strong> Inward-facing architecture promotes family privacy while creating cool, peaceful courtyards in hot climates.
                  </div>
                  <div className="p-3 bg-yellow-50 rounded-lg">
                    <strong>Zellige Craftsmanship:</strong> Each tile is hand-cut and glazed, making every installation unique. The craft is passed down through generations.
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-teal-50 to-blue-50 rounded-xl p-6 border border-teal-200">
              <h4 className="font-semibold text-neutral-900 mb-4 flex items-center gap-2">
                <Globe className="w-4 h-4 text-teal-500" />
                Cultural Context & Modern Morocco
              </h4>
              <div className="grid md:grid-cols-2 gap-6 text-sm">
                <div>
                  <strong className="text-teal-700">Language Diversity:</strong>
                  <p className="text-neutral-600 mt-1">Morocco has three official languages: Arabic, Berber (Tamazight), and French. Many Moroccans are multilingual, also speaking Spanish or English.</p>
                </div>
                <div>
                  <strong className="text-teal-700">Modern Traditions:</strong>
                  <p className="text-neutral-600 mt-1">Contemporary Morocco blends ancient traditions with modern life. Young Moroccans often navigate between traditional family values and global influences.</p>
                </div>
              </div>
            </div>

            <div className="text-center p-4 bg-neutral-50 rounded-lg">
              <p className="text-sm text-neutral-600 mb-3">
                <strong>Cultural Immersion Tip:</strong> The best way to understand Moroccan culture is through respectful engagement with local communities.
              </p>
              <div className="flex justify-center gap-4 text-xs">
                <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full">Ask Questions</span>
                <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full">Show Interest</span>
                <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full">Be Respectful</span>
                <span className="px-3 py-1 bg-orange-100 text-orange-700 rounded-full">Learn Basics</span>
              </div>
            </div>
          </div>
        </ProgressiveDisclosure>
      </div>
    </div>
  )
}