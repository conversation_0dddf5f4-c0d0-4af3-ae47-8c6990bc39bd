'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Info,
  BookOpen,
  Heart,
  Clock,
  Users,
  Camera,
  AlertCircle,
  CheckCircle,
  Star,
  X,
  ChevronRight
} from 'lucide-react'
import { useCultural } from '@/lib/cultural-context'
import { ProgressiveDisclosure } from '@/components/ui/progressive-disclosure'

interface CulturalInsightsProps {
  destination?: string
  context?: 'trip-planning' | 'destination-page' | 'general'
  showAll?: boolean
  compact?: boolean
}

export function CulturalInsights({ 
  destination = 'all', 
  context = 'general',
  showAll = false,
  compact = false 
}: CulturalInsightsProps) {
  const { getInsightsForDestination, progress, addPoints } = useCultural()
  const [selectedInsight, setSelectedInsight] = useState<string | null>(null)
  const [acknowledgedInsights, setAcknowledgedInsights] = useState<string[]>([])

  const insights = getInsightsForDestination(destination)
  const displayInsights = showAll ? insights : insights.slice(0, compact ? 2 : 4)

  // Load acknowledged insights from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('acknowledgedInsights')
    if (saved) {
      setAcknowledgedInsights(JSON.parse(saved))
    }
  }, [])

  // Save acknowledged insights to localStorage
  useEffect(() => {
    localStorage.setItem('acknowledgedInsights', JSON.stringify(acknowledgedInsights))
  }, [acknowledgedInsights])

  const acknowledgeInsight = (insightId: string) => {
    if (!acknowledgedInsights.includes(insightId)) {
      setAcknowledgedInsights(prev => [...prev, insightId])
      addPoints(10, 'cultural-awareness') // Award points for cultural awareness
    }
  }

  const getInsightIcon = (category: string) => {
    switch (category) {
      case 'etiquette': return Users
      case 'history': return BookOpen
      case 'language': return Heart
      case 'customs': return Star
      case 'religion': return Clock
      default: return Info
    }
  }

  const getInsightColor = (importance: string) => {
    switch (importance) {
      case 'high': return 'border-red-200 bg-red-50'
      case 'medium': return 'border-orange-200 bg-orange-50'
      case 'low': return 'border-blue-200 bg-blue-50'
      default: return 'border-neutral-200 bg-neutral-50'
    }
  }

  const getImportanceIcon = (importance: string) => {
    switch (importance) {
      case 'high': return <AlertCircle className="w-4 h-4 text-red-600" />
      case 'medium': return <Info className="w-4 h-4 text-orange-600" />
      case 'low': return <CheckCircle className="w-4 h-4 text-blue-600" />
      default: return <Info className="w-4 h-4 text-neutral-600" />
    }
  }

  if (displayInsights.length === 0) {
    return null
  }

  if (compact) {
    return (
      <div className="space-y-3">
        <h4 className="text-sm font-semibold text-neutral-900 flex items-center gap-2">
          <Info className="w-4 h-4 text-primary-500" />
          Cultural Tips
        </h4>
        {displayInsights.map((insight) => {
          const Icon = getInsightIcon(insight.category)
          const isAcknowledged = acknowledgedInsights.includes(insight.id)
          
          return (
            <motion.div
              key={insight.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              className={`p-3 rounded-lg border-2 ${getInsightColor(insight.importance)} ${
                isAcknowledged ? 'opacity-75' : ''
              }`}
            >
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0 mt-0.5">
                  {getImportanceIcon(insight.importance)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Icon className="w-3 h-3 text-neutral-600" />
                    <h5 className="text-sm font-medium text-neutral-900">
                      {insight.title}
                    </h5>
                    {isAcknowledged && (
                      <CheckCircle className="w-3 h-3 text-green-600" />
                    )}
                  </div>
                  <p className="text-xs text-neutral-600 leading-relaxed">
                    {insight.description}
                  </p>
                  {!isAcknowledged && (
                    <button
                      onClick={() => acknowledgeInsight(insight.id)}
                      className="text-xs text-primary-600 hover:text-primary-700 mt-1"
                    >
                      Got it (+10 points)
                    </button>
                  )}
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>
    )
  }

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-neutral-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary-100 rounded-lg">
            <BookOpen className="w-5 h-5 text-primary-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-neutral-900">
              Cultural Insights
              {destination !== 'all' && (
                <span className="text-primary-600 ml-2 capitalize">
                  for {destination}
                </span>
              )}
            </h3>
            <p className="text-sm text-neutral-600">
              Essential cultural knowledge for respectful travel
            </p>
          </div>
        </div>
        
        {progress.immersionScore > 0 && (
          <div className="text-right">
            <div className="text-sm text-neutral-600">Cultural Level</div>
            <div className="text-lg font-semibold text-primary-600">
              {Math.round(progress.immersionScore)}%
            </div>
          </div>
        )}
      </div>

      <div className="grid gap-4">
        {displayInsights.map((insight, index) => {
          const Icon = getInsightIcon(insight.category)
          const isAcknowledged = acknowledgedInsights.includes(insight.id)
          const isSelected = selectedInsight === insight.id
          
          return (
            <motion.div
              key={insight.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`border-2 rounded-xl transition-all duration-200 ${
                getInsightColor(insight.importance)
              } ${isSelected ? 'ring-2 ring-primary-500' : ''} ${
                isAcknowledged ? 'opacity-75' : ''
              }`}
            >
              <div className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    <div className="flex-shrink-0 mt-1">
                      {getImportanceIcon(insight.importance)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Icon className="w-4 h-4 text-neutral-600" />
                        <h4 className="font-semibold text-neutral-900">
                          {insight.title}
                        </h4>
                        <span className="text-xs px-2 py-1 bg-neutral-200 text-neutral-700 rounded-full capitalize">
                          {insight.category}
                        </span>
                        {isAcknowledged && (
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        )}
                      </div>
                      <p className="text-neutral-700 text-sm leading-relaxed mb-3">
                        {insight.description}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <span className={`text-xs font-medium ${
                            insight.importance === 'high' ? 'text-red-600' :
                            insight.importance === 'medium' ? 'text-orange-600' :
                            'text-blue-600'
                          }`}>
                            {insight.importance.toUpperCase()} IMPORTANCE
                          </span>
                          
                          {insight.learnMore && (
                            <button
                              onClick={() => setSelectedInsight(
                                isSelected ? null : insight.id
                              )}
                              className="text-xs text-primary-600 hover:text-primary-700 flex items-center gap-1"
                            >
                              Learn More
                              <ChevronRight className={`w-3 h-3 transition-transform ${
                                isSelected ? 'rotate-90' : ''
                              }`} />
                            </button>
                          )}
                        </div>
                        
                        {!isAcknowledged && (
                          <motion.button
                            onClick={() => acknowledgeInsight(insight.id)}
                            className="text-xs bg-primary-500 text-white px-3 py-1 rounded-full hover:bg-primary-600 transition-colors"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            Got it (+10 points)
                          </motion.button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                <AnimatePresence>
                  {isSelected && insight.learnMore && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="mt-4 pt-4 border-t border-neutral-200"
                    >
                      <div className="bg-white/50 rounded-lg p-3">
                        <p className="text-sm text-neutral-700">
                          {insight.learnMore}
                        </p>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          )
        })}
      </div>

      {!showAll && insights.length > displayInsights.length && (
        <div className="mt-6 text-center">
          <button
            onClick={() => window.location.href = '/cultural-immersion'}
            className="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center gap-2 mx-auto"
          >
            View All Cultural Insights ({insights.length - displayInsights.length} more)
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Progressive Disclosure for Detailed Cultural Guidelines */}
      <div className="mt-8">
        <ProgressiveDisclosure
          title="Detailed Cultural Guidelines"
          subtitle="In-depth information for respectful cultural interaction"
          icon={BookOpen}
          variant="card"
        >
          <div className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-neutral-900 flex items-center gap-2">
                  <Heart className="w-4 h-4 text-red-500" />
                  Social Interactions
                </h4>
                <div className="space-y-3 text-sm text-neutral-700">
                  <div className="p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
                    <strong>Greetings:</strong> Use "As-salamu alaykum" (Peace be upon you) or "Ahlan wa sahlan" (Welcome). Wait for women to extend their hand first for handshakes.
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-500">
                    <strong>Personal Space:</strong> Moroccans tend to stand closer during conversations. This is normal and shows engagement, not aggression.
                  </div>
                  <div className="p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
                    <strong>Eye Contact:</strong> Maintain respectful eye contact with same-gender individuals. Be more reserved with opposite gender interactions.
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-semibold text-neutral-900 flex items-center gap-2">
                  <Camera className="w-4 h-4 text-blue-500" />
                  Photography Ethics
                </h4>
                <div className="space-y-3 text-sm text-neutral-700">
                  <div className="p-3 bg-amber-50 rounded-lg border-l-4 border-amber-500">
                    <strong>People:</strong> Always ask permission before photographing individuals. Some may expect a small tip (5-10 MAD).
                  </div>
                  <div className="p-3 bg-purple-50 rounded-lg border-l-4 border-purple-500">
                    <strong>Religious Sites:</strong> Photography may be restricted in mosques and religious areas. Ask guides or officials first.
                  </div>
                  <div className="p-3 bg-indigo-50 rounded-lg border-l-4 border-indigo-500">
                    <strong>Markets:</strong> Be respectful when photographing vendors and their goods. Building rapport first often leads to better photos.
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-6 border border-orange-200">
              <h4 className="font-semibold text-neutral-900 mb-4 flex items-center gap-2">
                <Clock className="w-4 h-4 text-orange-500" />
                Time-Sensitive Cultural Considerations
              </h4>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div>
                  <strong className="text-orange-700">Prayer Times:</strong>
                  <p className="text-neutral-600 mt-1">Five daily prayers may affect shop hours and activities. Plan accordingly and be respectful during call to prayer.</p>
                </div>
                <div>
                  <strong className="text-orange-700">Ramadan:</strong>
                  <p className="text-neutral-600 mt-1">During Ramadan, avoid eating/drinking in public during daylight. Many restaurants close during the day.</p>
                </div>
                <div>
                  <strong className="text-orange-700">Friday Prayers:</strong>
                  <p className="text-neutral-600 mt-1">Friday afternoons see increased mosque attendance. Some businesses may close temporarily.</p>
                </div>
              </div>
            </div>
          </div>
        </ProgressiveDisclosure>
      </div>

      {acknowledgedInsights.length > 0 && (
        <div className="mt-6 pt-4 border-t border-neutral-200">
          <div className="flex items-center gap-2 text-sm text-green-600">
            <CheckCircle className="w-4 h-4" />
            <span>
              You've acknowledged {acknowledgedInsights.length} cultural insight
              {acknowledgedInsights.length !== 1 ? 's' : ''}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}