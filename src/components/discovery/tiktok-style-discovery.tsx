'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion'
import {
  Heart,
  Share2,
  Bookmark,
  Play,
  Pause,
  Volume2,
  VolumeX,
  MapPin,
  Star,
  Clock,
  Users,
  ChevronUp,
  ChevronDown,
  MoreHorizontal,
  Camera,
  Send,
  MessageCircle,
  Eye,
  TrendingUp
} from 'lucide-react'
import { useCountryContext } from '@/lib/country-context'

interface DiscoveryContent {
  id: string
  type: 'video' | 'image' | '360'
  title: string
  description: string
  location: string
  creator: {
    name: string
    avatar: string
    isVerified: boolean
    followers: number
  }
  media: {
    url: string
    thumbnail: string
    duration?: number
  }
  stats: {
    likes: number
    shares: number
    saves: number
    views: number
    comments: number
  }
  tags: string[]
  category: string
  isLiked: boolean
  isSaved: boolean
  isFollowing: boolean
  experienceId?: string
  destinationId?: string
}

interface TikTokStyleDiscoveryProps {
  className?: string
  category?: string
  autoPlay?: boolean
}

export function TikTokStyleDiscovery({ className = '', category, autoPlay = true }: TikTokStyleDiscoveryProps) {
  const { currentCountry } = useCountryContext()
  const [currentIndex, setCurrentIndex] = useState(0)
  const [content, setContent] = useState<DiscoveryContent[]>([])
  const [isPlaying, setIsPlaying] = useState(autoPlay)
  const [isMuted, setIsMuted] = useState(true)
  const [isLoading, setIsLoading] = useState(true)
  const containerRef = useRef<HTMLDivElement>(null)
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([])

  // Mock content data
  useEffect(() => {
    const mockContent: DiscoveryContent[] = [
      {
        id: '1',
        type: 'video',
        title: 'Sunrise Over Sahara Desert',
        description: 'Waking up to this incredible view in the Sahara Desert! The colors are absolutely magical 🌅 #SaharaDesert #Morocco #Sunrise',
        location: 'Merzouga, Morocco',
        creator: {
          name: 'Ahmed_Explorer',
          avatar: '/images/creators/ahmed.jpg',
          isVerified: true,
          followers: 125000
        },
        media: {
          url: '/videos/sahara-sunrise.mp4',
          thumbnail: '/images/sahara-sunrise-thumb.jpg',
          duration: 45
        },
        stats: {
          likes: 15420,
          shares: 892,
          saves: 3240,
          views: 89500,
          comments: 456
        },
        tags: ['sahara', 'desert', 'sunrise', 'morocco', 'travel'],
        category: 'Nature & Adventure',
        isLiked: false,
        isSaved: false,
        isFollowing: false,
        experienceId: 'sahara-desert-camping',
        destinationId: 'merzouga'
      },
      {
        id: '2',
        type: 'video',
        title: 'Traditional Moroccan Cooking',
        description: 'Learning to make authentic tagine with a local family in Fes! The spices smell incredible 🍲 Recipe in comments!',
        location: 'Fes, Morocco',
        creator: {
          name: 'FoodieNomad',
          avatar: '/images/creators/foodie.jpg',
          isVerified: true,
          followers: 89000
        },
        media: {
          url: '/videos/cooking-tagine.mp4',
          thumbnail: '/images/cooking-tagine-thumb.jpg',
          duration: 60
        },
        stats: {
          likes: 8920,
          shares: 445,
          saves: 2100,
          views: 45200,
          comments: 234
        },
        tags: ['cooking', 'tagine', 'moroccan', 'food', 'culture'],
        category: 'Food & Culture',
        isLiked: true,
        isSaved: true,
        isFollowing: true,
        experienceId: 'cooking-class-fes'
      },
      {
        id: '3',
        type: 'image',
        title: 'Blue Streets of Chefchaouen',
        description: 'Every corner in Chefchaouen is Instagram-worthy! 💙 This blue city is pure magic ✨',
        location: 'Chefchaouen, Morocco',
        creator: {
          name: 'WanderlustSarah',
          avatar: '/images/creators/sarah.jpg',
          isVerified: false,
          followers: 34000
        },
        media: {
          url: '/images/chefchaouen-blue.jpg',
          thumbnail: '/images/chefchaouen-blue-thumb.jpg'
        },
        stats: {
          likes: 12340,
          shares: 678,
          saves: 4560,
          views: 67800,
          comments: 189
        },
        tags: ['chefchaouen', 'blue', 'photography', 'instagram', 'morocco'],
        category: 'Photography',
        isLiked: false,
        isSaved: true,
        isFollowing: false,
        destinationId: 'chefchaouen'
      },
      {
        id: '4',
        type: 'video',
        title: 'Marrakech Night Market Vibes',
        description: 'The energy at Jemaa el-Fnaa is unreal! Street food, music, and pure magic 🎭🍢',
        location: 'Marrakech, Morocco',
        creator: {
          name: 'NightOwlTraveler',
          avatar: '/images/creators/nightowl.jpg',
          isVerified: true,
          followers: 156000
        },
        media: {
          url: '/videos/marrakech-night.mp4',
          thumbnail: '/images/marrakech-night-thumb.jpg',
          duration: 38
        },
        stats: {
          likes: 18750,
          shares: 1240,
          saves: 2890,
          views: 112000,
          comments: 567
        },
        tags: ['marrakech', 'nightlife', 'streetfood', 'culture', 'market'],
        category: 'Culture & Nightlife',
        isLiked: false,
        isSaved: false,
        isFollowing: true,
        destinationId: 'marrakech'
      }
    ]

    setContent(mockContent)
    setIsLoading(false)
  }, [category, currentCountry])

  // Handle scroll navigation
  const handleScroll = (direction: 'up' | 'down') => {
    if (direction === 'down' && currentIndex < content.length - 1) {
      setCurrentIndex(currentIndex + 1)
    } else if (direction === 'up' && currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  // Handle video play/pause
  useEffect(() => {
    const currentVideo = videoRefs.current[currentIndex]
    if (currentVideo && content[currentIndex]?.type === 'video') {
      if (isPlaying) {
        currentVideo.play()
      } else {
        currentVideo.pause()
      }
    }
  }, [currentIndex, isPlaying, content])

  // Handle interactions
  const handleLike = (contentId: string) => {
    setContent(prev => prev.map(item => 
      item.id === contentId 
        ? { 
            ...item, 
            isLiked: !item.isLiked,
            stats: { 
              ...item.stats, 
              likes: item.isLiked ? item.stats.likes - 1 : item.stats.likes + 1 
            }
          }
        : item
    ))
  }

  const handleSave = (contentId: string) => {
    setContent(prev => prev.map(item => 
      item.id === contentId 
        ? { 
            ...item, 
            isSaved: !item.isSaved,
            stats: { 
              ...item.stats, 
              saves: item.isSaved ? item.stats.saves - 1 : item.stats.saves + 1 
            }
          }
        : item
    ))
  }

  const handleFollow = (contentId: string) => {
    setContent(prev => prev.map(item => 
      item.id === contentId 
        ? { ...item, isFollowing: !item.isFollowing }
        : item
    ))
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-black">
        <div className="text-white text-center">
          <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p>Loading amazing content...</p>
        </div>
      </div>
    )
  }

  const currentContent = content[currentIndex]

  return (
    <div 
      ref={containerRef}
      className={`relative h-screen bg-black overflow-hidden ${className}`}
    >
      {/* Content Display */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentContent?.id}
          initial={{ opacity: 0, scale: 1.1 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.3 }}
          className="absolute inset-0"
        >
          {currentContent?.type === 'video' ? (
            <video
              ref={el => videoRefs.current[currentIndex] = el}
              src={currentContent.media.url}
              poster={currentContent.media.thumbnail}
              className="w-full h-full object-cover"
              loop
              muted={isMuted}
              playsInline
              onError={() => console.log('Video error')}
            />
          ) : (
            <img
              src={currentContent?.media.url}
              alt={currentContent?.title}
              className="w-full h-full object-cover"
            />
          )}

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20" />
        </motion.div>
      </AnimatePresence>

      {/* Navigation Controls */}
      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex flex-col gap-4 z-10">
        <button
          onClick={() => handleScroll('up')}
          disabled={currentIndex === 0}
          className="w-12 h-12 bg-black/30 backdrop-blur-sm rounded-full flex items-center justify-center text-white disabled:opacity-30 hover:bg-black/50 transition-colors"
        >
          <ChevronUp className="w-6 h-6" />
        </button>
        
        <button
          onClick={() => handleScroll('down')}
          disabled={currentIndex === content.length - 1}
          className="w-12 h-12 bg-black/30 backdrop-blur-sm rounded-full flex items-center justify-center text-white disabled:opacity-30 hover:bg-black/50 transition-colors"
        >
          <ChevronDown className="w-6 h-6" />
        </button>
      </div>

      {/* Action Buttons */}
      <div className="absolute right-4 bottom-32 flex flex-col gap-6 z-10">
        {/* Creator Avatar */}
        <div className="relative">
          <img
            src={currentContent?.creator.avatar}
            alt={currentContent?.creator.name}
            className="w-12 h-12 rounded-full border-2 border-white"
            onError={(e) => {
              e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(currentContent?.creator.name || 'User')}&background=random`
            }}
          />
          {!currentContent?.isFollowing && (
            <button
              onClick={() => handleFollow(currentContent?.id || '')}
              className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold hover:bg-red-600 transition-colors"
            >
              +
            </button>
          )}
        </div>

        {/* Like Button */}
        <motion.button
          whileTap={{ scale: 0.8 }}
          onClick={() => handleLike(currentContent?.id || '')}
          className="flex flex-col items-center gap-1"
        >
          <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
            currentContent?.isLiked ? 'bg-red-500' : 'bg-black/30 backdrop-blur-sm'
          }`}>
            <Heart className={`w-6 h-6 ${currentContent?.isLiked ? 'text-white fill-current' : 'text-white'}`} />
          </div>
          <span className="text-white text-xs font-medium">
            {formatNumber(currentContent?.stats.likes || 0)}
          </span>
        </motion.button>

        {/* Comment Button */}
        <button className="flex flex-col items-center gap-1">
          <div className="w-12 h-12 bg-black/30 backdrop-blur-sm rounded-full flex items-center justify-center">
            <MessageCircle className="w-6 h-6 text-white" />
          </div>
          <span className="text-white text-xs font-medium">
            {formatNumber(currentContent?.stats.comments || 0)}
          </span>
        </button>

        {/* Save Button */}
        <motion.button
          whileTap={{ scale: 0.8 }}
          onClick={() => handleSave(currentContent?.id || '')}
          className="flex flex-col items-center gap-1"
        >
          <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
            currentContent?.isSaved ? 'bg-yellow-500' : 'bg-black/30 backdrop-blur-sm'
          }`}>
            <Bookmark className={`w-6 h-6 ${currentContent?.isSaved ? 'text-white fill-current' : 'text-white'}`} />
          </div>
          <span className="text-white text-xs font-medium">
            {formatNumber(currentContent?.stats.saves || 0)}
          </span>
        </motion.button>

        {/* Share Button */}
        <button className="flex flex-col items-center gap-1">
          <div className="w-12 h-12 bg-black/30 backdrop-blur-sm rounded-full flex items-center justify-center">
            <Share2 className="w-6 h-6 text-white" />
          </div>
          <span className="text-white text-xs font-medium">
            {formatNumber(currentContent?.stats.shares || 0)}
          </span>
        </button>
      </div>

      {/* Content Info */}
      <div className="absolute bottom-0 left-0 right-0 p-6 z-10">
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-white font-semibold">@{currentContent?.creator.name}</span>
            {currentContent?.creator.isVerified && (
              <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
            {currentContent?.isFollowing && (
              <span className="text-xs text-gray-300">Following</span>
            )}
          </div>
          
          <h3 className="text-white text-lg font-bold mb-2">{currentContent?.title}</h3>
          
          <p className="text-white text-sm leading-relaxed mb-3">
            {currentContent?.description}
          </p>
          
          <div className="flex items-center gap-4 text-white text-sm">
            <div className="flex items-center gap-1">
              <MapPin className="w-4 h-4" />
              <span>{currentContent?.location}</span>
            </div>
            <div className="flex items-center gap-1">
              <Eye className="w-4 h-4" />
              <span>{formatNumber(currentContent?.stats.views || 0)} views</span>
            </div>
          </div>
          
          {/* Tags */}
          <div className="flex flex-wrap gap-2 mt-3">
            {currentContent?.tags.map((tag, index) => (
              <span key={index} className="text-blue-300 text-sm">
                #{tag}
              </span>
            ))}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          {currentContent?.experienceId && (
            <button className="flex-1 bg-primary-500 text-white py-3 px-4 rounded-xl font-medium hover:bg-primary-600 transition-colors">
              Book Experience
            </button>
          )}
          {currentContent?.destinationId && (
            <button className="flex-1 bg-white/20 backdrop-blur-sm text-white py-3 px-4 rounded-xl font-medium hover:bg-white/30 transition-colors">
              Explore Destination
            </button>
          )}
        </div>
      </div>

      {/* Media Controls */}
      {currentContent?.type === 'video' && (
        <div className="absolute top-4 right-4 flex gap-2 z-10">
          <button
            onClick={() => setIsPlaying(!isPlaying)}
            className="w-10 h-10 bg-black/30 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/50 transition-colors"
          >
            {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
          </button>
          
          <button
            onClick={() => setIsMuted(!isMuted)}
            className="w-10 h-10 bg-black/30 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-black/50 transition-colors"
          >
            {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
          </button>
        </div>
      )}

      {/* Progress Indicator */}
      <div className="absolute top-4 left-4 flex flex-col gap-1 z-10">
        {content.map((_, index) => (
          <div
            key={index}
            className={`w-1 h-8 rounded-full transition-colors ${
              index === currentIndex ? 'bg-white' : 'bg-white/30'
            }`}
          />
        ))}
      </div>
    </div>
  )
}
