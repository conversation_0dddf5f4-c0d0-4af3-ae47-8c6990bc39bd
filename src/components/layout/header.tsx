'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Menu, 
  X, 
  MapPin, 
  User, 
  Search,
  Globe,
  Settings,
  LogOut,
  BarChart3,
  Clock,
  Star,
  ArrowRight
} from 'lucide-react'
import { useAuth } from '@/lib/auth/auth-context'
import { useCountryContext } from '@/lib/country-context'
import { SmartNotifications } from '@/components/notifications/smart-notifications'
import { CountrySelector } from '@/components/country/country-selector'

interface TripData {
  destinations: string[]
  totalCost: number
  totalDays: number
}

interface SearchResult {
  id: string
  title: string
  type: 'destination' | 'experience' | 'page'
  url: string
  description: string
  image?: string
}

export function Header() {
  const { user, logout } = useAuth()
  const { currentCountry } = useCountryContext()
  const router = useRouter()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [tripData, setTripData] = useState<TripData | null>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Load trip data
  useEffect(() => {
    const loadTripData = () => {
      try {
        const savedTrip = localStorage.getItem('currentTrip')
        if (savedTrip) {
          const trip = JSON.parse(savedTrip)
          setTripData({
            destinations: trip.destinations || [],
            totalCost: trip.totalCost || 0,
            totalDays: trip.totalDays || 0
          })
        }
      } catch (error) {
        console.error('Error loading trip data:', error)
      }
    }

    loadTripData()
    
    const handleStorageChange = () => loadTripData()
    window.addEventListener('storage', handleStorageChange)
    
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  // Search functionality
  useEffect(() => {
    if (!searchQuery.trim()) {
      setSearchResults([])
      return
    }

    const searchTimeout = setTimeout(async () => {
      setIsSearching(true)
      
      // Mock search results - in real app, this would be an API call
      const mockResults: SearchResult[] = [
        {
          id: '1',
          title: 'Marrakech',
          type: 'destination' as const,
          url: `${countryPrefix}/destinations/marrakech`,
          description: 'The Red City with vibrant souks and palaces',
          image: '/images/destinations/marrakech-hero.jpg'
        },
        {
          id: '2',
          title: 'Sahara Desert Experience',
          type: 'experience' as const,
          url: `${countryPrefix}/destinations/sahara`,
          description: 'Camel trekking and desert camping',
          image: '/images/destinations/sahara-hero.jpg'
        },
        {
          id: '3',
          title: 'Cultural Immersion',
          type: 'page' as const,
          url: `${countryPrefix}/cultural-immersion`,
          description: 'Learn about Moroccan culture and traditions'
        },
        {
          id: '4',
          title: 'Fes Medina Tour',
          type: 'experience' as const,
          url: `${countryPrefix}/destinations/fes`,
          description: 'Explore the ancient medina and traditional crafts'
        }
      ].filter(result =>
        result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        result.description.toLowerCase().includes(searchQuery.toLowerCase())
      )

      setSearchResults(mockResults)
      setIsSearching(false)
    }, 300)

    return () => clearTimeout(searchTimeout)
  }, [searchQuery])

  // Handle search input focus
  useEffect(() => {
    if (isSearchOpen && searchInputRef.current) {
      searchInputRef.current.focus()
    }
  }, [isSearchOpen])

  // Close search on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsSearchOpen(false)
        setSearchQuery('')
      }
    }

    if (isSearchOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [isSearchOpen])

  const handleSearchSelect = (result: SearchResult) => {
    router.push(result.url)
    setIsSearchOpen(false)
    setSearchQuery('')
  }

  // Get current path to determine if we're in a country-specific context
  const currentPath = typeof window !== 'undefined' ? window.location.pathname : ''
  const isCountryContext = currentPath.startsWith('/morocco/') || currentPath.startsWith('/japan/') || currentPath.startsWith('/italy/')
  const countryPrefix = isCountryContext ? `/${currentCountry.code.toLowerCase()}` : ''

  const navigation = [
    { name: 'Home', href: isCountryContext ? countryPrefix : '/' },
    { name: 'Destinations', href: `${countryPrefix}/destinations` },
    { name: 'Experiences', href: `${countryPrefix}/experiences` },
    { name: 'Plan Trip', href: '/trip-builder' }, // Trip builder is global
    { name: 'Cultural Learning', href: `${countryPrefix}/cultural-immersion` },
    { name: 'Explore', href: `${countryPrefix}/explore` },
    { name: 'About', href: '/about' }, // About is global
    { name: 'Contact', href: '/contact' }, // Contact is global
    { name: 'Help', href: '/help' } // Help is global
  ]

  return (
    <header className="bg-white shadow-sm border-b border-neutral-200 sticky top-0 z-50">
      <div className="container-custom">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-morocco-500 rounded-lg flex items-center justify-center">
              <Globe className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-neutral-900">ComeToMorocco</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-6">
            {navigation.slice(0, 4).map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-neutral-600 hover:text-primary-600 transition-colors duration-200 font-medium text-sm"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Search & Actions */}
          <div className="hidden md:flex items-center space-x-3">
            {/* Search Button */}
            <button
              onClick={() => setIsSearchOpen(true)}
              className="flex items-center gap-2 px-3 py-2 text-sm text-neutral-600 hover:text-primary-600 hover:bg-neutral-50 rounded-lg transition-colors"
            >
              <Search className="w-4 h-4" />
              <span className="hidden lg:inline">Search</span>
            </button>
            {/* Trip Summary Indicator */}
            {tripData && tripData.destinations.length > 0 && (
              <Link 
                href="/trip-builder" 
                className="flex items-center gap-2 text-sm bg-gradient-to-r from-primary-50 to-teal-50 text-primary-700 px-3 py-2 rounded-lg hover:from-primary-100 hover:to-teal-100 transition-all duration-200 border border-primary-200"
              >
                <MapPin className="w-4 h-4" />
                <span className="font-medium">{tripData.destinations.length} cities</span>
                <span className="text-primary-600 font-semibold">€{tripData.totalCost}</span>
              </Link>
            )}

            {/* Country Selector */}
            <CountrySelector variant="header" />

            {/* Smart Notifications */}
            <SmartNotifications />

            {/* User Menu */}
            {user ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center gap-2 p-2 rounded-lg hover:bg-neutral-100 transition-colors"
                >
                  <div className="w-8 h-8 bg-gradient-to-br from-orange-400 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                    {user.name?.charAt(0).toUpperCase() || user.email?.charAt(0).toUpperCase() || 'U'}
                  </div>
                  <span className="text-sm font-medium text-neutral-700">{user.name || user.email || 'User'}</span>
                </button>

                {isUserMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-neutral-200 py-2 z-50"
                  >
                    <Link
                      href="/dashboard"
                      className="flex items-center gap-2 px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-50"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <BarChart3 className="w-4 h-4" />
                      Dashboard
                    </Link>
                    <Link
                      href="/profile"
                      className="flex items-center gap-2 px-4 py-2 text-sm text-neutral-700 hover:bg-neutral-50"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <Settings className="w-4 h-4" />
                      Settings
                    </Link>
                    <hr className="my-2" />
                    <button
                      onClick={() => {
                        logout()
                        setIsUserMenuOpen(false)
                      }}
                      className="flex items-center gap-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left"
                    >
                      <LogOut className="w-4 h-4" />
                      Sign Out
                    </button>
                  </motion.div>
                )}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Link href="/login" className="btn-ghost btn-sm">
                  Sign In
                </Link>
                <Link href="/signup" className="btn-primary btn-sm">
                  Sign Up
                </Link>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-lg hover:bg-neutral-100 transition-colors"
          >
            {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden border-t border-neutral-200 py-4"
          >
            <nav className="space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block px-4 py-2 text-neutral-600 hover:text-primary-600 hover:bg-neutral-50 rounded-lg transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              
              {/* Mobile Trip Summary */}
              {tripData && tripData.destinations.length > 0 && (
                <Link
                  href="/trip-builder"
                  className="block px-4 py-2 bg-primary-50 text-primary-700 rounded-lg mt-4"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    <span>Your Trip: {tripData.destinations.length} cities • €{tripData.totalCost}</span>
                  </div>
                </Link>
              )}
              
              {user ? (
                <>
                  <Link
                    href="/dashboard"
                    className="block px-4 py-2 text-neutral-600 hover:text-primary-600 hover:bg-neutral-50 rounded-lg transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <BarChart3 className="w-4 h-4 mr-2 inline" />
                    Dashboard
                  </Link>
                  <button
                    onClick={() => {
                      logout()
                      setIsMenuOpen(false)
                    }}
                    className="block w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                  >
                    <LogOut className="w-4 h-4 mr-2 inline" />
                    Sign Out
                  </button>
                </>
              ) : (
                <>
                  <Link
                    href="/login"
                    className="block px-4 py-2 text-neutral-600 hover:text-primary-600 hover:bg-neutral-50 rounded-lg transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <User className="w-4 h-4 mr-2 inline" />
                    Sign In
                  </Link>
                  <Link
                    href="/signup"
                    className="block px-4 py-2 bg-primary-500 text-white rounded-lg transition-colors"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    Sign Up
                  </Link>
                </>
              )}
            </nav>
          </motion.div>
        )}
      </div>

      {/* Search Modal */}
      <AnimatePresence>
        {isSearchOpen && (
          <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex min-h-screen items-start justify-center p-4 pt-16">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="absolute inset-0 bg-black/50 backdrop-blur-sm"
                onClick={() => setIsSearchOpen(false)}
              />
              <motion.div
                initial={{ opacity: 0, scale: 0.95, y: -20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -20 }}
                className="relative w-full max-w-2xl bg-white rounded-2xl shadow-2xl overflow-hidden"
              >
                {/* Search Header */}
                <div className="flex items-center gap-3 p-4 border-b border-neutral-200">
                  <Search className="w-5 h-5 text-neutral-400" />
                  <input
                    ref={searchInputRef}
                    type="text"
                    placeholder={`Search destinations, experiences in ${currentCountry.displayName}...`}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1 text-lg placeholder-neutral-400 focus:outline-none"
                  />
                  <button
                    onClick={() => setIsSearchOpen(false)}
                    className="p-2 hover:bg-neutral-100 rounded-lg transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>

                {/* Search Results */}
                <div className="max-h-96 overflow-y-auto">
                  {isSearching ? (
                    <div className="flex items-center justify-center py-12">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                    </div>
                  ) : searchResults.length > 0 ? (
                    <div className="p-2">
                      {searchResults.map((result) => (
                        <button
                          key={result.id}
                          onClick={() => handleSearchSelect(result)}
                          className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-neutral-50 transition-colors text-left"
                        >
                          {result.image ? (
                            <img
                              src={result.image}
                              alt={result.title}
                              className="w-12 h-12 rounded-lg object-cover"
                            />
                          ) : (
                            <div className="w-12 h-12 bg-gradient-to-br from-primary-100 to-teal-100 rounded-lg flex items-center justify-center">
                              {result.type === 'destination' ? (
                                <MapPin className="w-5 h-5 text-primary-600" />
                              ) : result.type === 'experience' ? (
                                <Star className="w-5 h-5 text-primary-600" />
                              ) : (
                                <Globe className="w-5 h-5 text-primary-600" />
                              )}
                            </div>
                          )}
                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-neutral-900 truncate">
                              {result.title}
                            </div>
                            <div className="text-sm text-neutral-600 truncate">
                              {result.description}
                            </div>
                            <div className="text-xs text-primary-600 capitalize mt-1">
                              {result.type}
                            </div>
                          </div>
                          <ArrowRight className="w-4 h-4 text-neutral-400" />
                        </button>
                      ))}
                    </div>
                  ) : searchQuery ? (
                    <div className="text-center py-12">
                      <Search className="w-12 h-12 text-neutral-300 mx-auto mb-3" />
                      <p className="text-neutral-500">No results found for "{searchQuery}"</p>
                      <p className="text-sm text-neutral-400 mt-1">Try searching for destinations, experiences, or activities</p>
                    </div>
                  ) : (
                    <div className="p-4">
                      <div className="text-sm font-medium text-neutral-700 mb-3">Popular Searches</div>
                      <div className="space-y-2">
                        {[
                          { title: 'Marrakech Souks', type: 'destination' },
                          { title: 'Sahara Desert', type: 'destination' },
                          { title: 'Cultural Immersion', type: 'experience' },
                          { title: 'Cooking Classes', type: 'experience' }
                        ].map((item, index) => (
                          <button
                            key={index}
                            onClick={() => setSearchQuery(item.title)}
                            className="flex items-center gap-2 text-sm text-neutral-600 hover:text-primary-600 transition-colors"
                          >
                            <Clock className="w-3 h-3" />
                            {item.title}
                          </button>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Search Footer */}
                <div className="border-t border-neutral-200 p-3 bg-neutral-50">
                  <div className="flex items-center justify-between text-xs text-neutral-500">
                    <span>Press ESC to close</span>
                    <span className="flex items-center gap-1">
                      <span className="px-1.5 py-0.5 bg-white border border-neutral-200 rounded">↵</span>
                      to select
                    </span>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        )}
      </AnimatePresence>
    </header>
  )
}