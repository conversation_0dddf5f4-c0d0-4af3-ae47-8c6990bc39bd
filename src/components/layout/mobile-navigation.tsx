'use client'

import React, { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion'
import {
  Home,
  MapPin,
  Calendar,
  User,
  Menu,
  X,
  Search,
  Heart,
  Bookmark,
  Settings,
  HelpCircle,
  LogOut,
  Star,
  Camera,
  Compass,
  Play,
  MessageCircle,
  Bell,
  Globe,
  ChevronRight,
  Sparkles
} from 'lucide-react'
import { useCountryContext } from '@/lib/country-context'
import { useAuth } from '@/lib/auth/auth-context'

export function MobileNavigation() {
  const pathname = usePathname()
  const { currentCountry } = useCountryContext()
  const { user, logout } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('')
  const { scrollY } = useScroll()
  const navOpacity = useTransform(scrollY, [0, 100], [1, 0.95])
  const navY = useTransform(scrollY, [0, 100], [0, -5])

  // Update active tab based on pathname
  useEffect(() => {
    if (pathname.includes('/destinations')) setActiveTab('explore')
    else if (pathname.includes('/experiences')) setActiveTab('experiences')
    else if (pathname.includes('/trip-builder')) setActiveTab('plan')
    else if (pathname.includes('/discover')) setActiveTab('discover')
    else if (pathname.includes('/profile') || pathname.includes('/auth')) setActiveTab('profile')
    else setActiveTab('home')
  }, [pathname])

  const mainNavItems = [
    {
      id: 'home',
      href: `/${currentCountry.code}`,
      icon: Home,
      label: 'Home',
      color: 'text-blue-600'
    },
    {
      id: 'explore',
      href: `/${currentCountry.code}/destinations`,
      icon: MapPin,
      label: 'Explore',
      color: 'text-green-600'
    },
    {
      id: 'discover',
      href: `/${currentCountry.code}/discover`,
      icon: Play,
      label: 'Discover',
      color: 'text-purple-600',
      isNew: true
    },
    {
      id: 'experiences',
      href: `/${currentCountry.code}/experiences`,
      icon: Star,
      label: 'Experiences',
      color: 'text-yellow-600'
    },
    {
      id: 'plan',
      href: `/${currentCountry.code}/trip-builder`,
      icon: Calendar,
      label: 'Plan',
      color: 'text-indigo-600'
    }
  ]

  const quickActions = [
    { href: `/${currentCountry.code}/search`, icon: Search, label: 'Search', color: 'bg-blue-500' },
    { href: '/favorites', icon: Heart, label: 'Favorites', color: 'bg-red-500' },
    { href: '/saved', icon: Bookmark, label: 'Saved', color: 'bg-green-500' },
    { href: '/notifications', icon: Bell, label: 'Notifications', color: 'bg-purple-500' }
  ]

  const menuSections = [
    {
      title: 'Explore',
      items: [
        { href: `/${currentCountry.code}/destinations`, icon: MapPin, label: 'Destinations' },
        { href: `/${currentCountry.code}/experiences`, icon: Star, label: 'Experiences' },
        { href: `/${currentCountry.code}/discover`, icon: Play, label: 'Discover Feed' },
        { href: `/${currentCountry.code}/photography`, icon: Camera, label: 'Photo Spots' }
      ]
    },
    {
      title: 'Personal',
      items: [
        { href: '/favorites', icon: Heart, label: 'Favorites' },
        { href: '/saved', icon: Bookmark, label: 'Saved Items' },
        { href: '/trips', icon: Calendar, label: 'My Trips' },
        { href: '/reviews', icon: MessageCircle, label: 'My Reviews' }
      ]
    },
    {
      title: 'Support',
      items: [
        { href: '/settings', icon: Settings, label: 'Settings' },
        { href: '/help', icon: HelpCircle, label: 'Help & Support' },
        { href: '/feedback', icon: MessageCircle, label: 'Send Feedback' }
      ]
    }
  ]

  const handleTabPress = (tabId: string, href: string) => {
    setActiveTab(tabId)
    // Add haptic feedback for mobile
    if (navigator.vibrate) {
      navigator.vibrate(10)
    }
  }

  return (
    <>
      {/* Enhanced Bottom Navigation Bar */}
      <motion.nav 
        style={{ opacity: navOpacity, y: navY }}
        className="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-lg border-t border-gray-200/50 z-40 md:hidden shadow-lg"
      >
        {/* Active Tab Indicator */}
        <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-500 to-primary-600" />
        
        <div className="flex items-center justify-around py-1">
          {mainNavItems.map((item, index) => (
            <Link
              key={item.id}
              href={item.href}
              onClick={() => handleTabPress(item.id, item.href)}
              className="relative flex flex-col items-center py-2 px-2 min-w-[60px] group"
            >
              {/* Active Background */}
              {activeTab === item.id && (
                <motion.div
                  layoutId="activeBackground"
                  className="absolute inset-0 bg-primary-50 rounded-xl"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}
              
              {/* Icon Container */}
              <motion.div
                whileTap={{ scale: 0.85 }}
                className={`relative z-10 p-1.5 rounded-lg transition-all duration-200 ${
                  activeTab === item.id
                    ? item.color + ' bg-white shadow-sm'
                    : 'text-gray-500 group-hover:text-gray-700'
                }`}
              >
                <item.icon className="w-5 h-5" />
                
                {/* New Badge */}
                {item.isNew && (
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center"
                  >
                    <Sparkles className="w-2 h-2 text-white" />
                  </motion.div>
                )}
              </motion.div>
              
              {/* Label */}
              <span className={`text-xs font-medium mt-1 transition-colors duration-200 ${
                activeTab === item.id
                  ? 'text-gray-900'
                  : 'text-gray-500'
              }`}>
                {item.label}
              </span>
              
              {/* Active Indicator Dot */}
              {activeTab === item.id && (
                <motion.div
                  layoutId="activeDot"
                  className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}
            </Link>
          ))}
          
          {/* Enhanced Menu Button */}
          <motion.button
            whileTap={{ scale: 0.85 }}
            onClick={() => setIsMenuOpen(true)}
            className="relative flex flex-col items-center py-2 px-2 min-w-[60px] group"
          >
            <div className="relative z-10 p-1.5 rounded-lg text-gray-500 group-hover:text-gray-700 transition-colors duration-200">
              <Menu className="w-5 h-5" />
            </div>
            <span className="text-xs font-medium mt-1 text-gray-500">Menu</span>
          </motion.button>
        </div>
      </motion.nav>

      {/* Enhanced Slide-out Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <>
            {/* Backdrop with blur */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsMenuOpen(false)}
              className="fixed inset-0 bg-black/30 backdrop-blur-sm z-50 md:hidden"
            />

            {/* Enhanced Menu Panel */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 200 }}
              className="fixed top-0 right-0 bottom-0 w-80 bg-white shadow-2xl z-50 md:hidden overflow-hidden"
            >
              {/* Enhanced Header */}
              <div className="bg-gradient-to-r from-primary-500 to-primary-600 text-white p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {user ? (
                      <>
                        <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                          <User className="w-6 h-6" />
                        </div>
                        <div>
                          <p className="font-semibold">{user.name}</p>
                          <p className="text-sm opacity-90">{user.email}</p>
                        </div>
                      </>
                    ) : (
                      <div>
                        <p className="font-semibold">Welcome!</p>
                        <p className="text-sm opacity-90">Sign in to get started</p>
                      </div>
                    )}
                  </div>
                  <button
                    onClick={() => setIsMenuOpen(false)}
                    className="p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-full transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-4 gap-3">
                  {quickActions.map((action) => (
                    <Link
                      key={action.href}
                      href={action.href}
                      onClick={() => setIsMenuOpen(false)}
                      className="flex flex-col items-center gap-2 p-3 bg-white/20 rounded-xl hover:bg-white/30 transition-colors"
                    >
                      <div className={`w-8 h-8 ${action.color} rounded-lg flex items-center justify-center`}>
                        <action.icon className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-xs font-medium">{action.label}</span>
                    </Link>
                  ))}
                </div>
              </div>

              {/* Enhanced Menu Content */}
              <div className="flex-1 overflow-y-auto">
                {menuSections.map((section, sectionIndex) => (
                  <div key={section.title} className="py-4">
                    <h3 className="text-sm font-semibold text-gray-900 px-6 mb-3 uppercase tracking-wide">
                      {section.title}
                    </h3>
                    <div className="space-y-1 px-4">
                      {section.items.map((item) => (
                        <Link
                          key={item.href}
                          href={item.href}
                          onClick={() => setIsMenuOpen(false)}
                          className="flex items-center justify-between px-3 py-3 text-gray-700 hover:bg-gray-50 rounded-xl transition-colors group"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center group-hover:bg-primary-100 transition-colors">
                              <item.icon className="w-4 h-4 group-hover:text-primary-600 transition-colors" />
                            </div>
                            <span className="font-medium">{item.label}</span>
                          </div>
                          <ChevronRight className="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
                        </Link>
                      ))}
                    </div>
                  </div>
                ))}

                {/* Enhanced Country Selector */}
                <div className="px-6 py-4">
                  <div className="border-t border-gray-200 pt-4">
                    <h3 className="text-sm font-semibold text-gray-900 mb-3 uppercase tracking-wide">
                      Current Destination
                    </h3>
                    <div className="flex items-center gap-3 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl">
                      <span className="text-3xl">{currentCountry.flag}</span>
                      <div className="flex-1">
                        <p className="font-semibold text-gray-900">{currentCountry.displayName}</p>
                        <p className="text-sm text-gray-500">Exploring {currentCountry.displayName}</p>
                      </div>
                      <Globe className="w-5 h-5 text-gray-400" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Footer */}
              <div className="border-t border-gray-200 p-4 bg-gray-50">
                {user ? (
                  <button
                    onClick={() => {
                      logout()
                      setIsMenuOpen(false)
                    }}
                    className="flex items-center justify-center gap-3 w-full px-4 py-3 text-red-600 hover:bg-red-50 rounded-xl transition-colors font-medium"
                  >
                    <LogOut className="w-5 h-5" />
                    <span>Sign Out</span>
                  </button>
                ) : (
                  <Link
                    href="/auth/login"
                    onClick={() => setIsMenuOpen(false)}
                    className="flex items-center justify-center w-full px-4 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl font-medium hover:from-primary-600 hover:to-primary-700 transition-all shadow-lg"
                  >
                    Sign In to Continue
                  </Link>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}
