import Link from 'next/link'
import { MapPin, Mail, Phone, Instagram, Facebook, Twitter } from 'lucide-react'

export function Footer() {
  return (
    <footer className="bg-gradient-to-br from-neutral-900 via-neutral-800 to-neutral-900 text-white">
      <div className="container-custom py-20">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-12">
          {/* Brand */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center space-x-3 mb-6 group">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center group-hover:scale-105 transition-transform duration-200">
                <MapPin className="h-7 w-7 text-white" />
              </div>
              <span className="font-display font-bold text-2xl">
                ComeToMorocco
              </span>
            </Link>
            <p className="text-neutral-300 mb-8 leading-relaxed">
              Your trusted gateway to authentic Morocco experiences. 
              Discover the magic of Morocco with expert local guides.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 bg-neutral-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-all duration-200 group">
                <Instagram className="h-5 w-5 text-neutral-400 group-hover:text-white" />
              </a>
              <a href="#" className="w-10 h-10 bg-neutral-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-all duration-200 group">
                <Facebook className="h-5 w-5 text-neutral-400 group-hover:text-white" />
              </a>
              <a href="#" className="w-10 h-10 bg-neutral-800 hover:bg-primary-600 rounded-lg flex items-center justify-center transition-all duration-200 group">
                <Twitter className="h-5 w-5 text-neutral-400 group-hover:text-white" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-display font-semibold text-xl mb-6 text-white">Explore</h3>
            <ul className="space-y-4">
              <li><Link href="/destinations/marrakech" className="text-neutral-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                <span className="w-1 h-1 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                Marrakech
              </Link></li>
              <li><Link href="/destinations/fes" className="text-neutral-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                <span className="w-1 h-1 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                Fes
              </Link></li>
              <li><Link href="/destinations/sahara" className="text-neutral-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                <span className="w-1 h-1 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                Sahara Desert
              </Link></li>
              <li><Link href="/agents" className="text-neutral-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                <span className="w-1 h-1 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                Our Agents
              </Link></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="font-display font-semibold text-xl mb-6 text-white">Support</h3>
            <ul className="space-y-4">
              <li><Link href="/experiences" className="text-neutral-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                <span className="w-1 h-1 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                Experiences
              </Link></li>
              <li><Link href="/about" className="text-neutral-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                <span className="w-1 h-1 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                About Us
              </Link></li>
              <li><Link href="/contact" className="text-neutral-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                <span className="w-1 h-1 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                Contact Us
              </Link></li>
              <li><Link href="/help" className="text-neutral-300 hover:text-primary-400 transition-colors duration-200 flex items-center group">
                <span className="w-1 h-1 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></span>
                Help Center
              </Link></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="font-display font-semibold text-xl mb-6 text-white">Get in Touch</h3>
            <div className="space-y-4">
              <div className="flex items-start">
                <div className="w-8 h-8 bg-primary-600/20 rounded-lg flex items-center justify-center mr-3 mt-1">
                  <Mail className="h-4 w-4 text-primary-400" />
                </div>
                <div>
                  <p className="text-neutral-300 text-sm">Email us</p>
                  <a href="mailto:<EMAIL>" className="text-white hover:text-primary-400 transition-colors duration-200">
                    <EMAIL>
                  </a>
                </div>
              </div>
              <div className="flex items-start">
                <div className="w-8 h-8 bg-primary-600/20 rounded-lg flex items-center justify-center mr-3 mt-1">
                  <Phone className="h-4 w-4 text-primary-400" />
                </div>
                <div>
                  <p className="text-neutral-300 text-sm">Call us</p>
                  <a href="tel:+212522000000" className="text-white hover:text-primary-400 transition-colors duration-200">
                    +212 522 000 000
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-neutral-700/50 mt-16 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-neutral-400 text-sm">
              &copy; 2024 ComeToMorocco. All rights reserved.
            </p>
            <div className="flex items-center gap-6 text-sm">
              <Link href="/privacy" className="text-neutral-400 hover:text-primary-400 transition-colors duration-200">
                Privacy
              </Link>
              <Link href="/terms" className="text-neutral-400 hover:text-primary-400 transition-colors duration-200">
                Terms
              </Link>
              <Link href="/cookies" className="text-neutral-400 hover:text-primary-400 transition-colors duration-200">
                Cookies
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}