'use client'

import { useState, useRef } from 'react'
import { motion, Reorder } from 'framer-motion'
import { 
  Clock, 
  MapPin, 
  Camera, 
  Utensils, 
  Bed, 
  Car,
  Plus,
  GripVertical,
  Sun,
  Sunset,
  Moon,
  Coffee,
  Edit3,
  Trash2
} from 'lucide-react'

interface TimeBlock {
  id: string
  time: string
  period: 'morning' | 'afternoon' | 'evening'
  activity: {
    id: string
    title: string
    type: 'experience' | 'meal' | 'transport' | 'rest'
    duration: number
    location: string
    description: string
    image?: string
    cost: number
  }
  isEditing?: boolean
}

interface DayPlan {
  id: string
  date: string
  destination: string
  timeBlocks: TimeBlock[]
  totalCost: number
  totalDuration: number
}

export function VisualTimeline() {
  const [selectedDay, setSelectedDay] = useState<string>('day-1')
  const [isAddingActivity, setIsAddingActivity] = useState(false)
  
  const [dayPlans, setDayPlans] = useState<DayPlan[]>([
    {
      id: 'day-1',
      date: '2024-03-15',
      destination: 'Marrakech',
      totalCost: 185,
      totalDuration: 8,
      timeBlocks: [
        {
          id: 'block-1',
          time: '08:00',
          period: 'morning',
          activity: {
            id: 'breakfast-1',
            title: 'Traditional Breakfast',
            type: 'meal',
            duration: 1,
            location: 'Riad Terrace',
            description: 'Moroccan pancakes, fresh orange juice, and mint tea',
            image: 'https://images.unsplash.com/photo-1551218808-94e220e084d2?w=300&h=200&fit=crop',
            cost: 25
          }
        },
        {
          id: 'block-2',
          time: '09:30',
          period: 'morning',
          activity: {
            id: 'medina-tour',
            title: 'Medina Walking Tour',
            type: 'experience',
            duration: 3,
            location: 'Old Medina',
            description: 'Explore the UNESCO World Heritage medina with a local guide',
            image: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=300&h=200&fit=crop',
            cost: 65
          }
        },
        {
          id: 'block-3',
          time: '13:00',
          period: 'afternoon',
          activity: {
            id: 'lunch-1',
            title: 'Rooftop Lunch',
            type: 'meal',
            duration: 1.5,
            location: 'Café des Épices',
            description: 'Traditional tagine with panoramic medina views',
            image: 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=300&h=200&fit=crop',
            cost: 35
          }
        },
        {
          id: 'block-4',
          time: '15:00',
          period: 'afternoon',
          activity: {
            id: 'rest-1',
            title: 'Rest & Relaxation',
            type: 'rest',
            duration: 2,
            location: 'Riad',
            description: 'Afternoon rest and preparation for evening activities',
            cost: 0
          }
        },
        {
          id: 'block-5',
          time: '18:00',
          period: 'evening',
          activity: {
            id: 'sunset-1',
            title: 'Sunset at Jemaa el-Fnaa',
            type: 'experience',
            duration: 2,
            location: 'Jemaa el-Fnaa Square',
            description: 'Watch the sunset and evening entertainment begin',
            image: 'https://images.unsplash.com/photo-1544735716-392fe2489ffa?w=300&h=200&fit=crop',
            cost: 0
          }
        },
        {
          id: 'block-6',
          time: '20:30',
          period: 'evening',
          activity: {
            id: 'dinner-1',
            title: 'Traditional Dinner Show',
            type: 'meal',
            duration: 2.5,
            location: 'Chez Ali',
            description: 'Moroccan feast with traditional music and horse show',
            image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=300&h=200&fit=crop',
            cost: 60
          }
        }
      ]
    },
    {
      id: 'day-2',
      date: '2024-03-16',
      destination: 'Marrakech',
      totalCost: 220,
      totalDuration: 9,
      timeBlocks: [
        {
          id: 'block-7',
          time: '07:00',
          period: 'morning',
          activity: {
            id: 'atlas-trip',
            title: 'Atlas Mountains Day Trip',
            type: 'experience',
            duration: 8,
            location: 'Atlas Mountains',
            description: 'Full day excursion to Berber villages with lunch included',
            image: 'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=300&h=200&fit=crop',
            cost: 120
          }
        },
        {
          id: 'block-8',
          time: '19:00',
          period: 'evening',
          activity: {
            id: 'hammam-1',
            title: 'Traditional Hammam',
            type: 'experience',
            duration: 2,
            location: 'La Mamounia Spa',
            description: 'Relaxing traditional hammam and massage',
            image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=200&fit=crop',
            cost: 100
          }
        }
      ]
    }
  ])

  const getPeriodIcon = (period: string) => {
    switch (period) {
      case 'morning': return Sun
      case 'afternoon': return Coffee
      case 'evening': return Sunset
      default: return Clock
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'experience': return Camera
      case 'meal': return Utensils
      case 'transport': return Car
      case 'rest': return Bed
      default: return MapPin
    }
  }

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'experience': return 'bg-primary-500'
      case 'meal': return 'bg-morocco-500'
      case 'transport': return 'bg-blue-500'
      case 'rest': return 'bg-green-500'
      default: return 'bg-neutral-500'
    }
  }

  const getPeriodColor = (period: string) => {
    switch (period) {
      case 'morning': return 'from-yellow-100 to-orange-100'
      case 'afternoon': return 'from-blue-100 to-indigo-100'
      case 'evening': return 'from-purple-100 to-pink-100'
      default: return 'from-neutral-100 to-neutral-200'
    }
  }

  const currentDay = dayPlans.find(day => day.id === selectedDay)!

  const updateTimeBlocks = (newTimeBlocks: TimeBlock[]) => {
    setDayPlans(prev => prev.map(day => 
      day.id === selectedDay 
        ? { 
            ...day, 
            timeBlocks: newTimeBlocks,
            totalCost: newTimeBlocks.reduce((sum, block) => sum + block.activity.cost, 0),
            totalDuration: newTimeBlocks.reduce((sum, block) => sum + block.activity.duration, 0)
          }
        : day
    ))
  }

  const addNewActivity = () => {
    const newBlock: TimeBlock = {
      id: `block-${Date.now()}`,
      time: '12:00',
      period: 'afternoon',
      activity: {
        id: `activity-${Date.now()}`,
        title: 'New Activity',
        type: 'experience',
        duration: 2,
        location: 'To be determined',
        description: 'Add your activity details',
        cost: 0
      },
      isEditing: true
    }
    
    updateTimeBlocks([...currentDay.timeBlocks, newBlock])
    setIsAddingActivity(false)
  }

  const removeActivity = (blockId: string) => {
    updateTimeBlocks(currentDay.timeBlocks.filter(block => block.id !== blockId))
  }

  return (
    <div className="space-y-6">
      {/* Day Selector */}
      <div className="bg-white rounded-2xl shadow-lg border border-neutral-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-neutral-900">Day-by-Day Timeline</h3>
          <div className="flex gap-2">
            {dayPlans.map((day) => (
              <button
                key={day.id}
                onClick={() => setSelectedDay(day.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedDay === day.id
                    ? 'bg-primary-500 text-white'
                    : 'bg-neutral-100 text-neutral-600 hover:bg-neutral-200'
                }`}
              >
                Day {day.id.split('-')[1]} - {day.destination}
              </button>
            ))}
          </div>
        </div>

        {/* Day Overview */}
        <div className="grid md:grid-cols-4 gap-4 mb-6">
          <div className="bg-primary-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-4 h-4 text-primary-600" />
              <span className="text-sm font-medium text-primary-900">Total Duration</span>
            </div>
            <p className="text-lg font-bold text-primary-600">{currentDay.totalDuration} hours</p>
          </div>
          
          <div className="bg-morocco-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <MapPin className="w-4 h-4 text-morocco-600" />
              <span className="text-sm font-medium text-morocco-900">Destination</span>
            </div>
            <p className="text-lg font-bold text-morocco-600">{currentDay.destination}</p>
          </div>
          
          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <Camera className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium text-green-900">Activities</span>
            </div>
            <p className="text-lg font-bold text-green-600">
              {currentDay.timeBlocks.filter(block => block.activity.type === 'experience').length}
            </p>
          </div>
          
          <div className="bg-yellow-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-sm font-medium text-yellow-900">Total Cost</span>
            </div>
            <p className="text-lg font-bold text-yellow-600">€{currentDay.totalCost}</p>
          </div>
        </div>

        {/* Timeline */}
        <div className="space-y-4">
          <Reorder.Group 
            axis="y" 
            values={currentDay.timeBlocks} 
            onReorder={updateTimeBlocks}
            className="space-y-3"
          >
            {currentDay.timeBlocks.map((block, index) => {
              const PeriodIcon = getPeriodIcon(block.period)
              const ActivityIcon = getActivityIcon(block.activity.type)
              
              return (
                <Reorder.Item key={block.id} value={block}>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`bg-gradient-to-r ${getPeriodColor(block.period)} rounded-xl p-4 border border-neutral-200 cursor-move`}
                  >
                    <div className="flex items-start gap-4">
                      {/* Time & Period */}
                      <div className="flex flex-col items-center min-w-[80px]">
                        <div className="flex items-center gap-2 mb-2">
                          <PeriodIcon className="w-4 h-4 text-neutral-600" />
                          <span className="text-sm font-medium text-neutral-700">{block.time}</span>
                        </div>
                        <div className="w-px h-12 bg-neutral-300" />
                      </div>

                      {/* Activity Content */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <div className={`w-8 h-8 ${getActivityColor(block.activity.type)} rounded-lg flex items-center justify-center`}>
                              <ActivityIcon className="w-4 h-4 text-white" />
                            </div>
                            <div>
                              <h4 className="font-semibold text-neutral-900">{block.activity.title}</h4>
                              <p className="text-sm text-neutral-600">{block.activity.location}</p>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <button className="p-1 text-neutral-400 hover:text-neutral-600">
                              <Edit3 className="w-4 h-4" />
                            </button>
                            <button 
                              onClick={() => removeActivity(block.id)}
                              className="p-1 text-neutral-400 hover:text-red-600"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                            <GripVertical className="w-4 h-4 text-neutral-400" />
                          </div>
                        </div>

                        <div className="grid md:grid-cols-3 gap-4">
                          <div className="md:col-span-2">
                            <p className="text-sm text-neutral-600 mb-3">{block.activity.description}</p>
                            
                            <div className="flex items-center gap-4 text-xs text-neutral-500">
                              <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {block.activity.duration}h
                              </span>
                              <span className="flex items-center gap-1">
                                €{block.activity.cost}
                              </span>
                              <span className="px-2 py-1 bg-white/50 rounded-full capitalize">
                                {block.activity.type}
                              </span>
                            </div>
                          </div>
                          
                          {block.activity.image && (
                            <div className="md:col-span-1">
                              <img
                                src={block.activity.image}
                                alt={block.activity.title}
                                className="w-full h-20 object-cover rounded-lg"
                              />
                            </div>
                          )}
                        </div>

                        {/* Duration Bar */}
                        <div className="mt-3">
                          <div className="w-full bg-white/50 rounded-full h-2">
                            <div 
                              className={`h-2 ${getActivityColor(block.activity.type)} rounded-full transition-all duration-300`}
                              style={{ width: `${Math.min((block.activity.duration / 8) * 100, 100)}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </Reorder.Item>
              )
            })}
          </Reorder.Group>

          {/* Add Activity Button */}
          <button
            onClick={addNewActivity}
            className="w-full p-4 border-2 border-dashed border-neutral-300 rounded-xl text-neutral-500 hover:border-primary-300 hover:text-primary-600 transition-colors flex items-center justify-center gap-2"
          >
            <Plus className="w-5 h-5" />
            <span>Add New Activity</span>
          </button>
        </div>
      </div>
    </div>
  )
}