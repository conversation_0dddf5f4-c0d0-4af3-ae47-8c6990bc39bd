'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Cloud, 
  Sun, 
  CloudRain, 
  Snowflake,
  Euro,
  Clock,
  Users,
  Calendar,
  MapPin,
  Star,
  Thermometer,
  Wind
} from 'lucide-react'

interface WeatherData {
  temperature: number
  condition: 'sunny' | 'cloudy' | 'rainy' | 'cold'
  description: string
  humidity: number
  windSpeed: number
}

interface Activity {
  id: string
  title: string
  description: string
  price: number
  duration: string
  groupSize: string
  season: string[]
  weatherSuitable: string[]
  timeConstraint: 'flexible' | 'morning' | 'afternoon' | 'evening'
  category: string
  image: string
}

interface LocalEvent {
  id: string
  name: string
  type: 'festival' | 'market' | 'cultural' | 'seasonal'
  date: string
  location: string
  description: string
  significance: string
  duration: string
}

export function ContextualSuggestions() {
  const [currentWeather, setCurrentWeather] = useState<WeatherData>({
    temperature: 24,
    condition: 'sunny',
    description: 'Perfect weather for outdoor activities',
    humidity: 45,
    windSpeed: 8
  })

  const [userPreferences, setUserPreferences] = useState({
    budget: 150,
    timeAvailable: 6,
    groupSize: 2,
    season: 'spring'
  })

  const activities: Activity[] = [
    {
      id: 'rooftop-yoga',
      title: 'Sunrise Rooftop Yoga',
      description: 'Start your day with yoga overlooking the Atlas Mountains',
      price: 35,
      duration: '1.5 hours',
      groupSize: '1-8 people',
      season: ['spring', 'fall', 'winter'],
      weatherSuitable: ['sunny', 'cloudy'],
      timeConstraint: 'morning',
      category: 'wellness',
      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop'
    },
    {
      id: 'cooking-class',
      title: 'Traditional Tagine Cooking',
      description: 'Learn to cook authentic Moroccan dishes with a local family',
      price: 65,
      duration: '3 hours',
      groupSize: '2-6 people',
      season: ['spring', 'summer', 'fall', 'winter'],
      weatherSuitable: ['sunny', 'cloudy', 'rainy'],
      timeConstraint: 'flexible',
      category: 'culinary',
      image: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
    },
    {
      id: 'hammam-spa',
      title: 'Traditional Hammam Experience',
      description: 'Relax and rejuvenate in a 500-year-old traditional hammam',
      price: 45,
      duration: '2 hours',
      groupSize: '1-4 people',
      season: ['spring', 'summer', 'fall', 'winter'],
      weatherSuitable: ['rainy', 'cold', 'cloudy'],
      timeConstraint: 'afternoon',
      category: 'wellness',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop'
    },
    {
      id: 'desert-sunset',
      title: 'Agafay Desert Sunset',
      description: 'Watch the sunset over the stone desert with camel rides',
      price: 85,
      duration: '4 hours',
      groupSize: '2-12 people',
      season: ['spring', 'fall', 'winter'],
      weatherSuitable: ['sunny', 'cloudy'],
      timeConstraint: 'evening',
      category: 'adventure',
      image: 'https://images.unsplash.com/photo-1509316975850-ff9c5deb0cd9?w=400&h=300&fit=crop'
    }
  ]

  const localEvents: LocalEvent[] = [
    {
      id: 'rose-festival',
      name: 'Festival of Roses',
      type: 'festival',
      date: 'May 15-17, 2024',
      location: 'Kelaat M\'Gouna',
      description: 'Annual celebration of the rose harvest with traditional music and dancing',
      significance: 'Celebrates the famous Damascena roses used in cosmetics and perfumes',
      duration: '3 days'
    },
    {
      id: 'weekly-market',
      name: 'Souk El Had Weekly Market',
      type: 'market',
      date: 'Every Sunday',
      location: 'Agadir',
      description: 'Largest weekly market in the region with local produce and crafts',
      significance: 'Traditional Berber market operating for over 100 years',
      duration: 'Full day'
    },
    {
      id: 'almond-blossom',
      name: 'Almond Blossom Season',
      type: 'seasonal',
      date: 'February - March',
      location: 'Atlas Mountains',
      description: 'Spectacular pink and white almond blossoms cover the mountain valleys',
      significance: 'Best time for photography and hiking in the Atlas Mountains',
      duration: '6 weeks'
    }
  ]

  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'sunny': return Sun
      case 'cloudy': return Cloud
      case 'rainy': return CloudRain
      case 'cold': return Snowflake
      default: return Sun
    }
  }

  const getWeatherRecommendations = () => {
    return activities.filter(activity => 
      activity.weatherSuitable.includes(currentWeather.condition) &&
      activity.price <= userPreferences.budget
    )
  }

  const getBudgetAlternatives = () => {
    const overBudget = activities.filter(activity => activity.price > userPreferences.budget)
    return overBudget.map(activity => ({
      ...activity,
      alternative: activities.find(alt => 
        alt.category === activity.category && 
        alt.price <= userPreferences.budget
      )
    })).filter(item => item.alternative)
  }

  const getTimeConstraintSuggestions = () => {
    const availableHours = userPreferences.timeAvailable
    return activities.filter(activity => {
      const duration = parseInt(activity.duration)
      return duration <= availableHours
    })
  }

  const getGroupSizeAppropriate = () => {
    return activities.filter(activity => {
      const maxSize = parseInt(activity.groupSize.split('-')[1])
      return userPreferences.groupSize <= maxSize
    })
  }

  const WeatherIcon = getWeatherIcon(currentWeather.condition)

  return (
    <div className="space-y-8">
      {/* Weather-Based Recommendations */}
      <div className="bg-white rounded-2xl shadow-lg border border-neutral-200 p-6">
        <div className="flex items-center gap-3 mb-6">
          <WeatherIcon className="w-6 h-6 text-primary-500" />
          <h3 className="text-xl font-semibold text-neutral-900">Weather-Aware Suggestions</h3>
        </div>

        {/* Current Weather */}
        <div className="bg-gradient-to-r from-blue-50 to-primary-50 rounded-xl p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <WeatherIcon className="w-8 h-8 text-primary-600" />
              <div>
                <h4 className="font-semibold text-neutral-900">{currentWeather.temperature}°C</h4>
                <p className="text-sm text-neutral-600">{currentWeather.description}</p>
              </div>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2 text-sm text-neutral-600">
                <Thermometer className="w-4 h-4" />
                <span>{currentWeather.humidity}% humidity</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-neutral-600">
                <Wind className="w-4 h-4" />
                <span>{currentWeather.windSpeed} km/h wind</span>
              </div>
            </div>
          </div>
        </div>

        {/* Weather-Appropriate Activities */}
        <div className="grid md:grid-cols-2 gap-4">
          {getWeatherRecommendations().slice(0, 4).map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white border border-neutral-200 rounded-xl p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start gap-3">
                <img
                  src={activity.image}
                  alt={activity.title}
                  className="w-16 h-16 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <h4 className="font-semibold text-neutral-900 mb-1">{activity.title}</h4>
                  <p className="text-sm text-neutral-600 mb-2">{activity.description}</p>
                  <div className="flex items-center gap-4 text-xs text-neutral-500">
                    <span className="flex items-center gap-1">
                      <Euro className="w-3 h-3" />
                      {activity.price}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      {activity.duration}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Budget Optimization */}
      <div className="bg-white rounded-2xl shadow-lg border border-neutral-200 p-6">
        <div className="flex items-center gap-3 mb-6">
          <Euro className="w-6 h-6 text-green-500" />
          <h3 className="text-xl font-semibold text-neutral-900">Budget-Optimized Alternatives</h3>
        </div>

        <div className="bg-green-50 rounded-xl p-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-semibold text-green-900">Your Budget: €{userPreferences.budget}</h4>
              <p className="text-sm text-green-700">We found {getBudgetAlternatives().length} budget-friendly alternatives</p>
            </div>
            <div className="text-right">
              <button className="text-sm text-green-600 hover:text-green-700 font-medium">
                Adjust Budget
              </button>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          {getBudgetAlternatives().slice(0, 2).map((item, index) => (
            <div key={index} className="bg-neutral-50 rounded-xl p-4">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium text-neutral-900 mb-2">Original Choice</h4>
                  <div className="flex items-center gap-3">
                    <img src={item.image} alt={item.title} className="w-12 h-12 rounded-lg object-cover" />
                    <div>
                      <p className="font-medium text-neutral-700">{item.title}</p>
                      <p className="text-sm text-red-600">€{item.price} (Over budget)</p>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium text-neutral-900 mb-2">Budget Alternative</h4>
                  <div className="flex items-center gap-3">
                    <img src={item.alternative!.image} alt={item.alternative!.title} className="w-12 h-12 rounded-lg object-cover" />
                    <div>
                      <p className="font-medium text-neutral-700">{item.alternative!.title}</p>
                      <p className="text-sm text-green-600">€{item.alternative!.price} (Within budget)</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Local Events */}
      <div className="bg-white rounded-2xl shadow-lg border border-neutral-200 p-6">
        <div className="flex items-center gap-3 mb-6">
          <Calendar className="w-6 h-6 text-morocco-500" />
          <h3 className="text-xl font-semibold text-neutral-900">Local Events & Festivals</h3>
        </div>

        <div className="grid md:grid-cols-3 gap-4">
          {localEvents.map((event, index) => (
            <motion.div
              key={event.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gradient-to-br from-morocco-50 to-sand-50 rounded-xl p-4 border border-morocco-200"
            >
              <div className="flex items-start justify-between mb-3">
                <h4 className="font-semibold text-neutral-900">{event.name}</h4>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  event.type === 'festival' ? 'bg-purple-100 text-purple-700' :
                  event.type === 'market' ? 'bg-green-100 text-green-700' :
                  'bg-blue-100 text-blue-700'
                }`}>
                  {event.type}
                </span>
              </div>
              
              <p className="text-sm text-neutral-600 mb-3">{event.description}</p>
              
              <div className="space-y-2 text-xs text-neutral-500">
                <div className="flex items-center gap-2">
                  <Calendar className="w-3 h-3" />
                  <span>{event.date}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-3 h-3" />
                  <span>{event.location}</span>
                </div>
              </div>
              
              <div className="mt-3 pt-3 border-t border-morocco-200">
                <p className="text-xs text-morocco-700 font-medium">{event.significance}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}