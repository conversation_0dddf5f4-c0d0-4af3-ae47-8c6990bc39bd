'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { InternationalDataService } from './services/international-data-service'

// Country configuration interface
export interface CountryTheme {
  primary: string
  secondary: string
  accent: string
  background: string
}

export interface CountryContext {
  id: string            // Database UUID
  code: string          // ISO country code (MA, JP, etc.)
  name: string          // Display name (Morocco, Japan)
  displayName: string   // Localized display name
  locale: string        // Language locale (en-MA, en-JP)
  currency: string      // Local currency (MAD, JPY)
  currencySymbol: string // Currency symbol (د.م., ¥)
  timezone: string      // Primary timezone
  theme: CountryTheme   // Country-specific colors/styling
  features: string[]    // Enabled features for this country
  flag: string          // Flag emoji or URL
  coordinates: { lat: number; lng: number } // Center coordinates
  isActive: boolean     // Whether country is available
}

// Fallback countries configuration (used when database is unavailable)
export const FALLBACK_COUNTRIES: Record<string, CountryContext> = {
  morocco: {
    id: 'fallback-morocco',
    code: 'MA',
    name: 'Morocco',
    displayName: 'Morocco',
    locale: 'en-MA',
    currency: 'MAD',
    currencySymbol: 'د.م.',
    timezone: 'Africa/Casablanca',
    theme: {
      primary: 'from-orange-500 to-red-600',
      secondary: 'from-amber-400 to-orange-500',
      accent: 'text-orange-600',
      background: 'bg-gradient-to-br from-orange-50 to-red-50'
    },
    features: ['destinations', 'experiences', 'cultural-immersion', 'guides'],
    flag: '🇲🇦',
    coordinates: { lat: 31.7917, lng: -7.0926 },
    isActive: true
  },
  japan: {
    id: 'fallback-japan',
    code: 'JP',
    name: 'Japan',
    displayName: 'Japan',
    locale: 'en-JP',
    currency: 'JPY',
    currencySymbol: '¥',
    timezone: 'Asia/Tokyo',
    theme: {
      primary: 'from-red-500 to-pink-600',
      secondary: 'from-pink-400 to-red-500',
      accent: 'text-red-600',
      background: 'bg-gradient-to-br from-red-50 to-pink-50'
    },
    features: ['destinations', 'experiences', 'cultural-immersion'],
    flag: '🇯🇵',
    coordinates: { lat: 36.2048, lng: 138.2529 },
    isActive: false
  },
  italy: {
    id: 'fallback-italy',
    code: 'IT',
    name: 'Italy',
    displayName: 'Italy',
    locale: 'en-IT',
    currency: 'EUR',
    currencySymbol: '€',
    timezone: 'Europe/Rome',
    theme: {
      primary: 'from-green-500 to-red-600',
      secondary: 'from-green-400 to-red-500',
      accent: 'text-green-600',
      background: 'bg-gradient-to-br from-green-50 to-red-50'
    },
    features: ['destinations', 'experiences'],
    flag: '🇮🇹',
    coordinates: { lat: 41.8719, lng: 12.5674 },
    isActive: false
  }
}

// Transform database country to CountryContext
function transformDatabaseCountry(dbCountry: any): CountryContext {
  return {
    id: dbCountry.id,
    code: dbCountry.code,
    name: dbCountry.name,
    displayName: dbCountry.display_name,
    locale: `en-${dbCountry.code}`,
    currency: dbCountry.currency,
    currencySymbol: dbCountry.currency_symbol || '$',
    timezone: dbCountry.timezone,
    theme: {
      primary: dbCountry.theme_config?.primary || 'from-blue-500 to-blue-600',
      secondary: dbCountry.theme_config?.secondary || 'from-blue-400 to-blue-500',
      accent: dbCountry.theme_config?.accent || 'text-blue-600',
      background: dbCountry.theme_config?.background || 'bg-gradient-to-br from-blue-50 to-indigo-50'
    },
    features: dbCountry.features || [],
    flag: dbCountry.flag || '🌍',
    coordinates: dbCountry.coordinates || { lat: 0, lng: 0 },
    isActive: dbCountry.is_active
  }
}

// Context for country state management
interface CountryContextType {
  currentCountry: CountryContext
  availableCountries: CountryContext[]
  switchCountry: (countryCode: string) => void
  isLoading: boolean
}

const CountryContextProvider = createContext<CountryContextType | undefined>(undefined)

// Hook to use country context
export function useCountryContext(): CountryContextType {
  const context = useContext(CountryContextProvider)
  if (!context) {
    throw new Error('useCountryContext must be used within a CountryProvider')
  }
  return context
}

// Load countries from database with fallback
async function loadCountries(): Promise<CountryContext[]> {
  try {
    // Check if Supabase is properly configured
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseKey ||
        supabaseUrl.includes('placeholder') ||
        supabaseKey.includes('placeholder')) {
      console.warn('Supabase not configured, using fallback countries')
      return Object.values(FALLBACK_COUNTRIES).filter(c => c.isActive)
    }

    // Try to load from database
    const dbCountries = await InternationalDataService.getActiveCountries()

    if (dbCountries && dbCountries.length > 0) {
      return dbCountries.map(transformDatabaseCountry)
    } else {
      console.warn('No countries found in database, using fallback')
      return Object.values(FALLBACK_COUNTRIES).filter(c => c.isActive)
    }
  } catch (error) {
    console.warn('Failed to load countries from database, using fallback:', error)
    return Object.values(FALLBACK_COUNTRIES).filter(c => c.isActive)
  }
}

// Country provider component
interface CountryProviderProps {
  children: ReactNode
  initialCountry?: string
}

export function CountryProvider({ children, initialCountry }: CountryProviderProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [isLoading, setIsLoading] = useState(true)
  const [availableCountries, setAvailableCountries] = useState<CountryContext[]>([])
  const [currentCountryCode, setCurrentCountryCode] = useState<string>('morocco')

  // Extract country from URL path
  const getCountryFromPath = (): string => {
    const pathSegments = pathname.split('/').filter(Boolean)
    const countryFromPath = pathSegments[0]
    
    // Check if first segment is a valid country code
    const validCountry = availableCountries.find(c => c.code.toLowerCase() === countryFromPath?.toLowerCase())
    if (validCountry) {
      return validCountry.code.toLowerCase()
    }
    
    return initialCountry || 'morocco' // Default to Morocco
  }

  // Get current country object
  const getCurrentCountry = (): CountryContext => {
    const country = availableCountries.find(c => c.code.toLowerCase() === currentCountryCode.toLowerCase())
    return country || availableCountries[0] || FALLBACK_COUNTRIES.morocco
  }

  const currentCountry = getCurrentCountry()

  // Switch country function
  const switchCountry = async (countryCode: string) => {
    const targetCountry = availableCountries.find(c => c.code.toLowerCase() === countryCode.toLowerCase())
    if (!targetCountry?.isActive) {
      console.warn(`Country ${countryCode} is not available`)
      return
    }

    setCurrentCountryCode(countryCode.toLowerCase())
    
    // Track country switch for analytics
    try {
      await InternationalDataService.trackUserActivity(
        'anonymous', // Will be updated when auth is implemented
        targetCountry.id,
        'country_switched',
        { 
          from_country: currentCountry.code,
          to_country: countryCode,
          from_path: pathname 
        }
      )
    } catch (err) {
      console.warn('Failed to track country switch:', err)
    }
    
    // Update URL to reflect country change
    const pathSegments = pathname.split('/').filter(Boolean)
    const currentCountryInPath = pathSegments[0]
    
    // Check if current path starts with a country code
    const hasCountryInPath = availableCountries.some(c => 
      c.code.toLowerCase() === currentCountryInPath?.toLowerCase()
    )
    
    if (hasCountryInPath) {
      // Replace existing country in path
      pathSegments[0] = countryCode.toLowerCase()
    } else {
      // Add country at the beginning of path
      pathSegments.unshift(countryCode.toLowerCase())
    }
    
    const newPath = '/' + pathSegments.join('/')
    router.push(newPath)
  }

  // Load countries on mount
  useEffect(() => {
    const initializeCountries = async () => {
      try {
        const countries = await loadCountries()
        setAvailableCountries(countries)
      } catch (error) {
        console.error('Failed to initialize countries:', error)
        // Use fallback countries as last resort
        setAvailableCountries(Object.values(FALLBACK_COUNTRIES).filter(c => c.isActive))
      } finally {
        setIsLoading(false)
      }
    }

    initializeCountries()
  }, [])

  // Update country when URL changes or countries are loaded
  useEffect(() => {
    if (availableCountries.length > 0) {
      const countryFromPath = getCountryFromPath()
      if (countryFromPath !== currentCountryCode) {
        setCurrentCountryCode(countryFromPath)
      }
    }
  }, [pathname, availableCountries])

  // Save user's country preference
  useEffect(() => {
    if (typeof window !== 'undefined' && currentCountryCode) {
      localStorage.setItem('preferred-country', currentCountryCode)
    }
  }, [currentCountryCode])

  const contextValue: CountryContextType = {
    currentCountry,
    availableCountries,
    switchCountry,
    isLoading
  }

  return (
    <CountryContextProvider.Provider value={contextValue}>
      {children}
    </CountryContextProvider.Provider>
  )
}

// Utility functions
export function formatCurrency(amount: number, countryContext: CountryContext): string {
  try {
    return new Intl.NumberFormat(countryContext.locale, {
      style: 'currency',
      currency: countryContext.currency,
    }).format(amount)
  } catch (err) {
    // Fallback formatting
    return `${countryContext.currencySymbol}${amount.toFixed(2)}`
  }
}

export function formatDate(date: Date, countryContext: CountryContext): string {
  try {
    return new Intl.DateTimeFormat(countryContext.locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date)
  } catch (err) {
    // Fallback formatting
    return date.toLocaleDateString()
  }
}

export function getCountryFromCode(code: string, countries: CountryContext[]): CountryContext | null {
  return countries.find(country => country.code.toLowerCase() === code.toLowerCase()) || null
}

// Additional utility hooks for international platform

// Hook for country-specific theme
export function useCountryTheme() {
  const { currentCountry } = useCountryContext()
  
  return {
    theme: currentCountry.theme,
    primaryGradient: currentCountry.theme.primary,
    secondaryGradient: currentCountry.theme.secondary,
    accentColor: currentCountry.theme.accent,
    backgroundColor: currentCountry.theme.background,
    flag: currentCountry.flag
  }
}

// Hook for country-specific features
export function useCountryFeatures() {
  const { currentCountry } = useCountryContext()
  
  const hasFeature = (feature: string): boolean => {
    return currentCountry.features.includes(feature)
  }
  
  return {
    features: currentCountry.features,
    hasFeature,
    hasDestinations: hasFeature('destinations'),
    hasExperiences: hasFeature('experiences'),
    hasCulturalImmersion: hasFeature('cultural-immersion'),
    hasGuides: hasFeature('guides')
  }
}

// Hook for country-specific currency formatting
export function useCountryCurrency() {
  const { currentCountry } = useCountryContext()
  
  const formatPrice = (amount: number): string => {
    return formatCurrency(amount, currentCountry)
  }
  
  const formatPriceRange = (min: number, max: number): string => {
    if (min === max) return formatPrice(min)
    return `${formatPrice(min)} - ${formatPrice(max)}`
  }
  
  return {
    currency: currentCountry.currency,
    currencySymbol: currentCountry.currencySymbol,
    formatPrice,
    formatPriceRange
  }
}

// Hook for country-specific date/time formatting
export function useCountryDateTime() {
  const { currentCountry } = useCountryContext()
  
  const formatDate = (date: Date): string => {
    return formatDate(date, currentCountry)
  }
  
  const formatTime = (date: Date): string => {
    try {
      return new Intl.DateTimeFormat(currentCountry.locale, {
        hour: '2-digit',
        minute: '2-digit',
        timeZone: currentCountry.timezone
      }).format(date)
    } catch (err) {
      return date.toLocaleTimeString()
    }
  }
  
  const formatDateTime = (date: Date): string => {
    try {
      return new Intl.DateTimeFormat(currentCountry.locale, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        timeZone: currentCountry.timezone
      }).format(date)
    } catch (err) {
      return date.toLocaleString()
    }
  }
  
  return {
    timezone: currentCountry.timezone,
    locale: currentCountry.locale,
    formatDate,
    formatTime,
    formatDateTime
  }
}

// Hook for URL building with country context
export function useCountryRouting() {
  const { currentCountry, switchCountry } = useCountryContext()
  const router = useRouter()
  
  const buildCountryPath = (path: string, countryCode?: string): string => {
    const targetCountry = countryCode || currentCountry.code.toLowerCase()
    const cleanPath = path.replace(/^\//, '').replace(/^[a-z]{2,3}\//, '')
    return `/${targetCountry}${cleanPath ? `/${cleanPath}` : ''}`
  }
  
  const navigateToCountryPath = (path: string, countryCode?: string) => {
    const fullPath = buildCountryPath(path, countryCode)
    router.push(fullPath)
  }
  
  const switchCountryAndNavigate = (countryCode: string, path: string = '') => {
    const fullPath = buildCountryPath(path, countryCode)
    switchCountry(countryCode)
    router.push(fullPath)
  }
  
  return {
    buildCountryPath,
    navigateToCountryPath,
    switchCountryAndNavigate,
    currentCountryCode: currentCountry.code.toLowerCase()
  }
}

// Legacy compatibility hook
export function useCountry() {
  const { currentCountry, availableCountries, switchCountry } = useCountryContext()
  
  return {
    selectedCountry: currentCountry.code.toLowerCase(),
    setSelectedCountry: switchCountry,
    countries: availableCountries.map(country => ({
      code: country.code.toLowerCase(),
      name: country.name,
      flag: country.flag,
      currency: country.currency,
      timezone: country.timezone
    }))
  }
}