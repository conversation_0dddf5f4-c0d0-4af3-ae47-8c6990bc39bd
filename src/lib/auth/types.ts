// User role definitions and authentication types

export type UserRole = 'traveler' | 'agent' | 'admin' | 'super_admin'

export interface User {
  id: string
  email: string
  name: string
  avatar?: string
  role: UserRole
  phone?: string
  preferences?: UserPreferences
  cultural_comfort_level?: 'conservative' | 'moderate' | 'adventurous'
  budget_range?: [number, number]
  travel_style?: string[]
  agent_id?: string
  created_at: string
  updated_at: string
  last_login?: string
  is_verified: boolean
  is_active: boolean
}

export interface UserPreferences {
  activities: string[]
  accommodation_type: string[]
  food_preferences: string[]
  cultural_interests: string[]
  mobility_requirements?: string[]
  language_preference: string
  currency_preference: string
  notification_preferences: {
    email: boolean
    sms: boolean
    push: boolean
    marketing: boolean
  }
}

export interface Agent extends User {
  role: 'agent'
  specializations: string[]
  languages: string[]
  experience_years: number
  rating: number
  total_bookings: number
  availability_status: 'available' | 'busy' | 'offline'
  bio: string
  certifications: string[]
  regions_covered: string[]
}

export interface AuthSession {
  user: User
  access_token: string
  refresh_token: string
  expires_at: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface SignupData {
  email: string
  password: string
  name: string
  phone?: string
  role?: UserRole
}

export interface AuthError {
  code: string
  message: string
  details?: any
}

export interface AuthState {
  user: User | null
  session: AuthSession | null
  loading: boolean
  error: AuthError | null
}

// Permission definitions
export type Permission = 
  | 'read:own_profile'
  | 'update:own_profile'
  | 'read:bookings'
  | 'create:bookings'
  | 'update:bookings'
  | 'cancel:bookings'
  | 'read:conversations'
  | 'create:messages'
  | 'read:locations'
  | 'create:favorites'
  | 'read:agents'
  | 'assign:agent'
  | 'manage:users'
  | 'manage:agents'
  | 'manage:content'
  | 'manage:bookings'
  | 'view:analytics'
  | 'manage:system'

export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  traveler: [
    'read:own_profile',
    'update:own_profile',
    'read:bookings',
    'create:bookings',
    'update:bookings',
    'cancel:bookings',
    'read:conversations',
    'create:messages',
    'read:locations',
    'create:favorites',
    'read:agents',
    'assign:agent'
  ],
  agent: [
    'read:own_profile',
    'update:own_profile',
    'read:bookings',
    'update:bookings',
    'read:conversations',
    'create:messages',
    'read:locations',
    'read:agents',
    'manage:bookings'
  ],
  admin: [
    'read:own_profile',
    'update:own_profile',
    'read:bookings',
    'create:bookings',
    'update:bookings',
    'cancel:bookings',
    'read:conversations',
    'create:messages',
    'read:locations',
    'create:favorites',
    'read:agents',
    'assign:agent',
    'manage:users',
    'manage:agents',
    'manage:content',
    'manage:bookings',
    'view:analytics'
  ],
  super_admin: [
    'read:own_profile',
    'update:own_profile',
    'read:bookings',
    'create:bookings',
    'update:bookings',
    'cancel:bookings',
    'read:conversations',
    'create:messages',
    'read:locations',
    'create:favorites',
    'read:agents',
    'assign:agent',
    'manage:users',
    'manage:agents',
    'manage:content',
    'manage:bookings',
    'view:analytics',
    'manage:system'
  ]
}
