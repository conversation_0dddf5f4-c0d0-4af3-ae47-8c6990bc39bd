'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase/client'
import { UserService } from '@/lib/services/user-service'
import type { Database } from '@/lib/supabase/client'

type UserProfile = Database['public']['Tables']['users']['Row']

interface AuthContextType {
  user: User | null
  userProfile: UserProfile | null
  session: Session | null
  loading: boolean
  error: string | null
  signIn: (email: string, password: string) => Promise<{ data: any; error: any }>
  signUp: (email: string, password: string, metadata?: any) => Promise<{ data: any; error: any }>
  signOut: () => Promise<void>
  updateProfile: (updates: any) => Promise<{ data: any; error: any }>
  // Legacy methods for backward compatibility
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Check if Supabase is properly configured
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const isSupabaseConfigured = supabaseUrl && 
      supabaseUrl !== 'your_supabase_url' && 
      supabaseUrl !== 'your_project_url' &&
      supabaseUrl !== 'https://placeholder.supabase.co'

    if (!isSupabaseConfigured) {
      // Fall back to mock authentication
      console.log('Supabase not configured, using mock authentication')
      initializeMockAuth()
      return
    }

    // Initialize real Supabase authentication
    initializeSupabaseAuth()
  }, [])

  const initializeMockAuth = () => {
    // Mock users for testing
    const MOCK_USERS = {
      '<EMAIL>': {
        id: '550e8400-e29b-41d4-a716-446655440001',
        email: '<EMAIL>',
        name: 'Super Admin',
        role: 'super_admin' as const,
        preferred_country: 'morocco',
        cultural_sensitivity_level: 8,
        travel_preferences: {
          travel_style: 'cultural_immersion',
          interests: ['history', 'culture', 'management']
        },
        avatar_url: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      },
      '<EMAIL>': {
        id: '550e8400-e29b-41d4-a716-446655440002',
        email: '<EMAIL>',
        name: 'Morocco Admin',
        role: 'country_admin' as const,
        preferred_country: 'morocco',
        cultural_sensitivity_level: 7,
        travel_preferences: {
          travel_style: 'cultural_immersion',
          interests: ['culture', 'heritage', 'management']
        },
        avatar_url: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      },
      '<EMAIL>': {
        id: '550e8400-e29b-41d4-a716-446655440003',
        email: '<EMAIL>',
        name: 'Youssef Alami',
        role: 'partner' as const,
        preferred_country: 'morocco',
        cultural_sensitivity_level: 9,
        travel_preferences: {
          specialties: ['cultural_tours', 'desert_expeditions'],
          languages: ['arabic', 'french', 'english']
        },
        avatar_url: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      },
      '<EMAIL>': {
        id: '550e8400-e29b-41d4-a716-446655440004',
        email: '<EMAIL>',
        name: 'Sarah Johnson',
        role: 'customer' as const,
        preferred_country: 'morocco',
        cultural_sensitivity_level: 6,
        travel_preferences: {
          travel_style: 'family_friendly',
          interests: ['culture', 'food', 'photography']
        },
        avatar_url: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    }

    // Check for existing session
    const savedUser = localStorage.getItem('currentUser')
    if (savedUser) {
      try {
        const mockProfile = JSON.parse(savedUser)
        setUserProfile(mockProfile)
        // Create a mock user object for compatibility
        setUser({
          id: mockProfile.id,
          email: mockProfile.email,
          user_metadata: { name: mockProfile.name },
          app_metadata: {},
          aud: 'authenticated',
          created_at: mockProfile.created_at,
          updated_at: mockProfile.updated_at
        } as unknown as User)
      } catch (error) {
        console.error('Error parsing saved user:', error)
        localStorage.removeItem('currentUser')
      }
    }
    setLoading(false)

    // Store mock auth functions globally for access
    ;(window as any).mockAuth = {
      signIn: async (email: string, password: string) => {
        const mockUser = MOCK_USERS[email as keyof typeof MOCK_USERS]
        if (mockUser && password) {
          setUserProfile(mockUser as any)
          setUser({
            id: mockUser.id,
            email: mockUser.email,
            user_metadata: { name: mockUser.name },
            app_metadata: {},
            aud: 'authenticated',
            created_at: mockUser.created_at,
            updated_at: mockUser.updated_at
          } as unknown as User)
          localStorage.setItem('currentUser', JSON.stringify(mockUser))
          return { data: { user: mockUser }, error: null }
        }
        return { data: null, error: { message: 'Invalid credentials' } }
      },
      signOut: async () => {
        setUser(null)
        setUserProfile(null)
        localStorage.removeItem('currentUser')
      }
    }
  }

  const initializeSupabaseAuth = () => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session)
      setUser(session?.user ?? null)
      if (session?.user) {
        loadUserProfile(session.user.id)
      }
      setLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        setSession(session)
        setUser(session?.user ?? null)
        
        if (session?.user) {
          await loadUserProfile(session.user.id)
        } else {
          setUserProfile(null)
        }
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }

  const loadUserProfile = async (userId: string) => {
    try {
      const profile = await UserService.getUserProfile(userId)
      setUserProfile(profile)
    } catch (error) {
      console.error('Error loading user profile:', error)
    }
  }

  const signIn = async (email: string, password: string) => {
    // Check if using mock auth
    if ((window as any).mockAuth) {
      return (window as any).mockAuth.signIn(email, password)
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    return { data, error }
  }

  const signUp = async (email: string, password: string, metadata = {}) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    })
    return { data, error }
  }

  const signOut = async () => {
    // Check if using mock auth
    if ((window as any).mockAuth) {
      return (window as any).mockAuth.signOut()
    }

    await supabase.auth.signOut()
  }

  const updateProfile = async (updates: any) => {
    if ((window as any).mockAuth) {
      // Mock profile update
      if (userProfile) {
        const updatedProfile = { ...userProfile, ...updates }
        setUserProfile(updatedProfile)
        localStorage.setItem('currentUser', JSON.stringify(updatedProfile))
        return { data: updatedProfile, error: null }
      }
      return { data: null, error: { message: 'No user profile found' } }
    }

    const { data, error } = await supabase.auth.updateUser({
      data: updates
    })
    return { data, error }
  }

  // Legacy methods for backward compatibility
  const login = async (email: string, password: string): Promise<boolean> => {
    const { data, error } = await signIn(email, password)
    return !error && data
  }

  const logout = () => {
    signOut()
  }

  return (
    <AuthContext.Provider value={{
      user,
      userProfile,
      session,
      loading,
      error,
      signIn,
      signUp,
      signOut,
      updateProfile,
      login,
      logout
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}