'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { useRouter } from 'next/navigation'

// Demo credentials for testing (remove in production)
const DEMO_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
}

// Super Admin Role Types
export type SuperAdminRole = 'super_admin' | 'country_admin' | 'content_manager' | 'cultural_validator'

export interface SuperAdminPermissions {
  canManageCountries: boolean
  canManageUsers: boolean
  canManageContent: boolean
  canValidateCulturalContent: boolean
  canViewAnalytics: boolean
  canManagePartners: boolean
  countryAccess: string[] // Country codes the admin has access to
}

export interface SuperAdminUser {
  id: string
  email: string
  name: string
  role: SuperAdminRole
  permissions: SuperAdminPermissions
  countryAccess: string[]
  isActive: boolean
  lastLogin?: Date
  createdAt: Date
}

// Permission configurations for each role
const ROLE_PERMISSIONS: Record<SuperAdminRole, SuperAdminPermissions> = {
  super_admin: {
    canManageCountries: true,
    canManageUsers: true,
    canManageContent: true,
    canValidateCulturalContent: true,
    canViewAnalytics: true,
    canManagePartners: true,
    countryAccess: ['*'] // Access to all countries
  },
  country_admin: {
    canManageCountries: false,
    canManageUsers: false,
    canManageContent: true,
    canValidateCulturalContent: true,
    canViewAnalytics: true,
    canManagePartners: true,
    countryAccess: [] // Set per user
  },
  content_manager: {
    canManageCountries: false,
    canManageUsers: false,
    canManageContent: true,
    canValidateCulturalContent: false,
    canViewAnalytics: false,
    canManagePartners: false,
    countryAccess: [] // Set per user
  },
  cultural_validator: {
    canManageCountries: false,
    canManageUsers: false,
    canManageContent: false,
    canValidateCulturalContent: true,
    canViewAnalytics: false,
    canManagePartners: false,
    countryAccess: [] // Set per user
  }
}

// Super Admin Context
interface SuperAdminContextType {
  user: SuperAdminUser | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (email: string, password: string) => Promise<boolean>
  logout: () => void
  hasPermission: (permission: keyof SuperAdminPermissions) => boolean
  hasCountryAccess: (countryCode: string) => boolean
}

const SuperAdminContext = createContext<SuperAdminContextType | undefined>(undefined)

// Super Admin Provider Component
export function SuperAdminProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<SuperAdminUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  // Check for existing session on mount
  useEffect(() => {
    checkExistingSession()
  }, [])

  const checkExistingSession = async () => {
    try {
      // Check for demo session first
      const demoToken = localStorage.getItem('super-admin-token')
      const demoUserData = localStorage.getItem('super-admin-user')

      if (demoToken === 'demo-token-123' && demoUserData) {
        try {
          const userData = JSON.parse(demoUserData)
          setUser(userData)
          setIsAuthenticated(true)
          setIsLoading(false)
          return
        } catch (error) {
          console.error('Error parsing demo user data:', error)
        }
      }

      // Check for real session
      const token = localStorage.getItem('super_admin_token')
      if (!token) {
        setIsLoading(false)
        return
      }

      // Validate token with backend
      const response = await fetch('/api/super-admin/validate-session', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
        setIsAuthenticated(true)
      } else {
        localStorage.removeItem('super_admin_token')
      }
    } catch (error) {
      console.error('Session validation failed:', error)
      localStorage.removeItem('super_admin_token')
      localStorage.removeItem('super-admin-token')
      localStorage.removeItem('super-admin-user')
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)

      // Demo authentication (remove in production)
      if (email === DEMO_CREDENTIALS.email && password === DEMO_CREDENTIALS.password) {
        const demoUser: SuperAdminUser = {
          id: 'demo-super-admin',
          email: DEMO_CREDENTIALS.email,
          name: 'Demo Super Admin',
          role: 'super_admin',
          permissions: ROLE_PERMISSIONS.super_admin,
          countryAccess: ['MAR', 'JPN', 'ITA', 'ESP'],
          isActive: true,
          lastLogin: new Date(),
          createdAt: new Date('2024-01-01')
        }

        setUser(demoUser)
        setIsAuthenticated(true)

        // Store in localStorage for persistence
        localStorage.setItem('super-admin-user', JSON.stringify(demoUser))
        localStorage.setItem('super-admin-token', 'demo-token-123')

        return true
      }

      // Try real API authentication
      const response = await fetch('/api/super-admin/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      })

      if (response.ok) {
        const { user: userData, token } = await response.json()

        // Store token and user data
        localStorage.setItem('super_admin_token', token)
        setUser(userData)
        
        // Redirect to dashboard
        router.push('/super-admin/dashboard')
        return true
      } else {
        const error = await response.json()
        console.error('Login failed:', error.message)
        return false
      }
    } catch (error) {
      console.error('Login error:', error)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = () => {
    localStorage.removeItem('super_admin_token')
    localStorage.removeItem('super-admin-token')
    localStorage.removeItem('super-admin-user')
    setUser(null)
    setIsAuthenticated(false)
    router.push('/super-admin/login')
  }

  const hasPermission = (permission: keyof SuperAdminPermissions): boolean => {
    if (!user) return false
    return user.permissions[permission] === true
  }

  const hasCountryAccess = (countryCode: string): boolean => {
    if (!user) return false
    if (user.permissions.countryAccess.includes('*')) return true
    return user.permissions.countryAccess.includes(countryCode.toUpperCase())
  }

  const contextValue: SuperAdminContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    hasPermission,
    hasCountryAccess
  }

  return (
    <SuperAdminContext.Provider value={contextValue}>
      {children}
    </SuperAdminContext.Provider>
  )
}

// Hook to use Super Admin context
export function useSuperAdmin(): SuperAdminContextType {
  const context = useContext(SuperAdminContext)
  if (!context) {
    throw new Error('useSuperAdmin must be used within a SuperAdminProvider')
  }
  return context
}

// Utility functions for role management
export function getRolePermissions(role: SuperAdminRole): SuperAdminPermissions {
  return ROLE_PERMISSIONS[role]
}

export function canAccessCountry(user: SuperAdminUser, countryCode: string): boolean {
  if (user.permissions.countryAccess.includes('*')) return true
  return user.permissions.countryAccess.includes(countryCode.toUpperCase())
}

export function isHigherRole(role1: SuperAdminRole, role2: SuperAdminRole): boolean {
  const roleHierarchy = ['cultural_validator', 'content_manager', 'country_admin', 'super_admin']
  return roleHierarchy.indexOf(role1) > roleHierarchy.indexOf(role2)
}

// Protected route wrapper
export function withSuperAdminAuth<T extends object>(
  Component: React.ComponentType<T>,
  requiredPermission?: keyof SuperAdminPermissions
) {
  return function ProtectedComponent(props: T) {
    const { isAuthenticated, isLoading, hasPermission } = useSuperAdmin()
    const router = useRouter()

    useEffect(() => {
      if (!isLoading) {
        if (!isAuthenticated) {
          router.push('/super-admin/login')
          return
        }

        if (requiredPermission && !hasPermission(requiredPermission)) {
          router.push('/super-admin/unauthorized')
          return
        }
      }
    }, [isAuthenticated, isLoading, hasPermission, router])

    if (isLoading) {
      return <div>Loading...</div>
    }

    if (!isAuthenticated) {
      return null
    }

    if (requiredPermission && !hasPermission(requiredPermission)) {
      return null
    }

    return <Component {...props} />
  }
}
