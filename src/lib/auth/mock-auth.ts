// Mock authentication service

import { User, AuthSession, LoginCredentials, SignupData, AuthError, Permission, ROLE_PERMISSIONS } from './types'
import { ALL_MOCK_USERS, MOCK_CREDENTIALS } from './mock-data'

class MockAuthService {
  private currentSession: AuthSession | null = null
  private sessionKey = 'cometomorocco_session'

  constructor() {
    // Load session from localStorage on initialization
    this.loadSession()
  }

  private loadSession(): void {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem(this.sessionKey)
      if (stored) {
        try {
          const session = JSON.parse(stored)
          // Check if session is still valid
          if (new Date(session.expires_at) > new Date()) {
            this.currentSession = session
          } else {
            this.clearSession()
          }
        } catch (error) {
          this.clearSession()
        }
      }
    }
  }

  private saveSession(session: AuthSession): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.session<PERSON><PERSON>, JSON.stringify(session))
    }
    this.currentSession = session
  }

  private clearSession(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.sessionKey)
    }
    this.currentSession = null
  }

  private generateToken(): string {
    return `mock_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private createSession(user: User): AuthSession {
    const now = new Date()
    const expiresAt = new Date(now.getTime() + 24 * 60 * 60 * 1000) // 24 hours

    return {
      user: {
        ...user,
        last_login: now.toISOString()
      },
      access_token: this.generateToken(),
      refresh_token: this.generateToken(),
      expires_at: expiresAt.toISOString()
    }
  }

  async login(credentials: LoginCredentials): Promise<{ session: AuthSession | null; error: AuthError | null }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    const { email, password } = credentials

    // Check if user exists
    const user = ALL_MOCK_USERS.find(u => u.email === email)
    if (!user) {
      return {
        session: null,
        error: {
          code: 'user_not_found',
          message: 'No user found with this email address'
        }
      }
    }

    // Check if user is active
    if (!user.is_active) {
      return {
        session: null,
        error: {
          code: 'user_inactive',
          message: 'This account has been deactivated'
        }
      }
    }

    // Check password
    const expectedPassword = MOCK_CREDENTIALS[email as keyof typeof MOCK_CREDENTIALS]
    if (password !== expectedPassword) {
      return {
        session: null,
        error: {
          code: 'invalid_credentials',
          message: 'Invalid email or password'
        }
      }
    }

    // Create and save session
    const session = this.createSession(user)
    this.saveSession(session)

    return { session, error: null }
  }

  async signup(data: SignupData): Promise<{ session: AuthSession | null; error: AuthError | null }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1500))

    const { email, password, name, phone, role = 'traveler' } = data

    // Check if user already exists
    const existingUser = ALL_MOCK_USERS.find(u => u.email === email)
    if (existingUser) {
      return {
        session: null,
        error: {
          code: 'user_already_exists',
          message: 'A user with this email already exists'
        }
      }
    }

    // Create new user
    const newUser: User = {
      id: `user_${Date.now()}`,
      email,
      name,
      phone,
      role,
      avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=ee770a&color=fff`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_verified: false,
      is_active: true,
      preferences: {
        activities: [],
        accommodation_type: [],
        food_preferences: [],
        cultural_interests: [],
        language_preference: 'en',
        currency_preference: 'USD',
        notification_preferences: {
          email: true,
          sms: false,
          push: true,
          marketing: false
        }
      }
    }

    // Add to mock users (in real app, this would be saved to database)
    ALL_MOCK_USERS.push(newUser)

    // Create and save session
    const session = this.createSession(newUser)
    this.saveSession(session)

    return { session, error: null }
  }

  async logout(): Promise<{ error: AuthError | null }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))

    this.clearSession()
    return { error: null }
  }

  async getCurrentSession(): Promise<AuthSession | null> {
    return this.currentSession
  }

  async getCurrentUser(): Promise<User | null> {
    return this.currentSession?.user || null
  }

  async refreshSession(): Promise<{ session: AuthSession | null; error: AuthError | null }> {
    if (!this.currentSession) {
      return {
        session: null,
        error: {
          code: 'no_session',
          message: 'No active session to refresh'
        }
      }
    }

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500))

    // Create new session with updated tokens
    const session = this.createSession(this.currentSession.user)
    this.saveSession(session)

    return { session, error: null }
  }

  hasPermission(permission: Permission): boolean {
    if (!this.currentSession) return false
    
    const userRole = this.currentSession.user.role
    const rolePermissions = ROLE_PERMISSIONS[userRole]
    
    return rolePermissions.includes(permission)
  }

  hasAnyPermission(permissions: Permission[]): boolean {
    return permissions.some(permission => this.hasPermission(permission))
  }

  hasAllPermissions(permissions: Permission[]): boolean {
    return permissions.every(permission => this.hasPermission(permission))
  }

  isAuthenticated(): boolean {
    return this.currentSession !== null
  }

  isRole(role: string): boolean {
    return this.currentSession?.user.role === role
  }
}

// Export singleton instance
export const mockAuth = new MockAuthService()
export default mockAuth
