// Country Data Management Hook
// Comprehensive hook for accessing country-specific data and services

import { useState, useEffect, useCallback } from 'react'
import { CountryDataService } from '@/lib/services/country-data-service'
import { CurrencyService, PriceDisplay } from '@/lib/services/currency-service'
import { WeatherService, WeatherData, SeasonalRecommendation } from '@/lib/services/weather-service'
import { CulturalContentService, CulturalCustom, LanguagePhrase } from '@/lib/services/cultural-content-service'
import { 
  CountryData, 
  CountryDestination, 
  CountryExperience, 
  LocalGuide,
  CountryWeatherSeason 
} from '@/lib/types/country-data'

export interface UseCountryDataReturn {
  // Country Data
  countryData: CountryData | null
  destinations: CountryDestination[]
  experiences: CountryExperience[]
  guides: LocalGuide[]
  
  // Weather & Climate
  currentWeather: WeatherData | null
  seasonalData: CountryWeatherSeason[]
  currentSeason: CountryWeatherSeason | null
  travelRecommendations: SeasonalRecommendation[]
  
  // Cultural Content
  culturalCustoms: CulturalCustom[]
  languagePhrases: LanguagePhrase[]
  
  // Currency & Pricing
  formatPrice: (amount: number, options?: any) => PriceDisplay
  convertPrice: (amount: number, fromCurrency: string) => Promise<PriceDisplay>
  
  // Loading States
  isLoading: boolean
  isLoadingWeather: boolean
  isLoadingCultural: boolean
  
  // Error States
  error: string | null
  
  // Actions
  refreshData: () => Promise<void>
  searchDestinations: (query: string, filters?: any) => Promise<CountryDestination[]>
  searchExperiences: (query: string, filters?: any) => Promise<CountryExperience[]>
  getDestination: (destinationId: string) => CountryDestination | null
  getExperience: (experienceId: string) => CountryExperience | null
  getGuidesByCity: (city: string) => LocalGuide[]
}

export function useCountryData(countryCode: string): UseCountryDataReturn {
  // Services
  const countryDataService = CountryDataService.getInstance()
  const currencyService = CurrencyService.getInstance()
  const weatherService = WeatherService.getInstance()
  const culturalService = CulturalContentService.getInstance()

  // State
  const [countryData, setCountryData] = useState<CountryData | null>(null)
  const [destinations, setDestinations] = useState<CountryDestination[]>([])
  const [experiences, setExperiences] = useState<CountryExperience[]>([])
  const [guides, setGuides] = useState<LocalGuide[]>([])
  
  const [currentWeather, setCurrentWeather] = useState<WeatherData | null>(null)
  const [seasonalData, setSeasonalData] = useState<CountryWeatherSeason[]>([])
  const [currentSeason, setCurrentSeason] = useState<CountryWeatherSeason | null>(null)
  const [travelRecommendations, setTravelRecommendations] = useState<SeasonalRecommendation[]>([])
  
  const [culturalCustoms, setCulturalCustoms] = useState<CulturalCustom[]>([])
  const [languagePhrases, setLanguagePhrases] = useState<LanguagePhrase[]>([])
  
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingWeather, setIsLoadingWeather] = useState(false)
  const [isLoadingCultural, setIsLoadingCultural] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load country data
  const loadCountryData = useCallback(async () => {
    if (!countryCode) return

    try {
      setIsLoading(true)
      setError(null)

      // Load main country data
      const data = await countryDataService.getCountryData(countryCode)
      if (!data) {
        throw new Error(`Country data not found for ${countryCode}`)
      }

      setCountryData(data)
      setDestinations(data.destinations || [])
      setExperiences(data.experiences || [])
      setGuides(data.guides || [])

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load country data')
      console.error('Error loading country data:', err)
    } finally {
      setIsLoading(false)
    }
  }, [countryCode, countryDataService])

  // Load weather data
  const loadWeatherData = useCallback(async () => {
    if (!countryCode || !countryData) return

    try {
      setIsLoadingWeather(true)

      // Get seasonal data
      const seasons = weatherService.getSeasonalData(countryCode)
      setSeasonalData(seasons)

      // Get current season
      const current = weatherService.getCurrentSeason(countryCode)
      setCurrentSeason(current)

      // Get travel recommendations
      const recommendations = weatherService.getBestTimeToVisit(countryCode)
      setTravelRecommendations(recommendations)

      // Get current weather for capital or main city
      if (countryData.destinations.length > 0) {
        const mainCity = countryData.destinations[0].name
        const weather = await weatherService.getCurrentWeather(mainCity, countryCode)
        setCurrentWeather(weather)
      }

    } catch (err) {
      console.error('Error loading weather data:', err)
    } finally {
      setIsLoadingWeather(false)
    }
  }, [countryCode, countryData, weatherService])

  // Load cultural content
  const loadCulturalContent = useCallback(async () => {
    if (!countryCode || !countryData) return

    try {
      setIsLoadingCultural(true)

      // Load cultural customs
      const customs = await culturalService.getCulturalCustoms(countryCode)
      setCulturalCustoms(customs)

      // Load language phrases for primary language
      if (countryData.languages.length > 0) {
        const primaryLanguage = countryData.languages.find(lang => lang.isPrimary) || countryData.languages[0]
        const phrases = await culturalService.getLanguagePhrases(primaryLanguage.code)
        setLanguagePhrases(phrases)
      }

    } catch (err) {
      console.error('Error loading cultural content:', err)
    } finally {
      setIsLoadingCultural(false)
    }
  }, [countryCode, countryData, culturalService])

  // Currency formatting functions
  const formatPrice = useCallback((amount: number, options?: any): PriceDisplay => {
    const currency = countryData?.currency.code || 'USD'
    return currencyService.formatPrice(amount, currency, options)
  }, [countryData, currencyService])

  const convertPrice = useCallback(async (amount: number, fromCurrency: string): Promise<PriceDisplay> => {
    const targetCurrency = countryData?.currency.code || 'USD'
    return await currencyService.getPriceInCountryCurrency(amount, fromCurrency, countryCode)
  }, [countryData, countryCode, currencyService])

  // Search functions
  const searchDestinations = useCallback(async (query: string, filters?: any): Promise<CountryDestination[]> => {
    return await countryDataService.searchDestinations(countryCode, query, filters)
  }, [countryCode, countryDataService])

  const searchExperiences = useCallback(async (query: string, filters?: any): Promise<CountryExperience[]> => {
    // This would be implemented in the service
    return experiences.filter(exp =>
      exp.title.toLowerCase().includes(query.toLowerCase()) ||
      exp.description.toLowerCase().includes(query.toLowerCase())
    )
  }, [experiences])

  // Get specific items
  const getDestination = useCallback((destinationId: string): CountryDestination | null => {
    return destinations.find(dest => dest.id === destinationId) || null
  }, [destinations])

  const getExperience = useCallback((experienceId: string): CountryExperience | null => {
    return experiences.find(exp => exp.id === experienceId) || null
  }, [experiences])

  const getGuidesByCity = useCallback((city: string): LocalGuide[] => {
    return guides.filter(guide => 
      guide.location.city.toLowerCase() === city.toLowerCase() && guide.isActive
    )
  }, [guides])

  // Refresh all data
  const refreshData = useCallback(async (): Promise<void> => {
    await loadCountryData()
  }, [loadCountryData])

  // Effects
  useEffect(() => {
    loadCountryData()
  }, [loadCountryData])

  useEffect(() => {
    if (countryData) {
      loadWeatherData()
      loadCulturalContent()
    }
  }, [countryData, loadWeatherData, loadCulturalContent])

  return {
    // Country Data
    countryData,
    destinations,
    experiences,
    guides,
    
    // Weather & Climate
    currentWeather,
    seasonalData,
    currentSeason,
    travelRecommendations,
    
    // Cultural Content
    culturalCustoms,
    languagePhrases,
    
    // Currency & Pricing
    formatPrice,
    convertPrice,
    
    // Loading States
    isLoading,
    isLoadingWeather,
    isLoadingCultural,
    
    // Error States
    error,
    
    // Actions
    refreshData,
    searchDestinations,
    searchExperiences,
    getDestination,
    getExperience,
    getGuidesByCity
  }
}

// Additional specialized hooks

export function useDestinationData(countryCode: string, destinationId: string) {
  const { destinations, experiences, guides, getDestination, isLoading } = useCountryData(countryCode)
  
  const destination = getDestination(destinationId)
  const destinationExperiences = experiences.filter(exp => exp.destinationId === destinationId)
  const nearbyGuides = destination ? guides.filter(guide => 
    guide.location.city.toLowerCase() === destination.name.toLowerCase()
  ) : []

  return {
    destination,
    experiences: destinationExperiences,
    guides: nearbyGuides,
    isLoading
  }
}

export function useCurrencyConverter(countryCode: string) {
  const currencyService = CurrencyService.getInstance()
  const { countryData } = useCountryData(countryCode)

  const convert = useCallback(async (amount: number, fromCurrency: string) => {
    if (!countryData) return null
    return await currencyService.convertCurrency(amount, fromCurrency, countryData.currency.code)
  }, [countryData, currencyService])

  const format = useCallback((amount: number, options?: any) => {
    if (!countryData) return { formatted: amount.toString(), amount, currency: null, symbol: '' }
    return currencyService.formatPrice(amount, countryData.currency.code, options)
  }, [countryData, currencyService])

  return { convert, format, currency: countryData?.currency }
}

export function useWeatherData(countryCode: string, location?: string) {
  const weatherService = WeatherService.getInstance()
  const [weather, setWeather] = useState<WeatherData | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    if (!countryCode) return

    const loadWeather = async () => {
      setIsLoading(true)
      try {
        const weatherData = await weatherService.getCurrentWeather(location || 'capital', countryCode)
        setWeather(weatherData)
      } catch (error) {
        console.error('Failed to load weather:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadWeather()
  }, [countryCode, location, weatherService])

  const seasonalData = weatherService.getSeasonalData(countryCode)
  const currentSeason = weatherService.getCurrentSeason(countryCode)

  return {
    weather,
    seasonalData,
    currentSeason,
    isLoading
  }
}
