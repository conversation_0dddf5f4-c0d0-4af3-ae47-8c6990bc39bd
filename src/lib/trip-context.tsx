'use client'

import { createContext, useContext, useState, useEffect, ReactNode } from 'react'

interface Experience {
  id: string
  title: string
  description: string
  duration: string
  price: number
  rating: number
  category: string
  image: string
  highlights: string[]
  difficulty: 'Easy' | 'Moderate' | 'Challenging'
  timeOfDay: 'Morning' | 'Afternoon' | 'Evening' | 'Full Day'
}

interface TripDay {
  id: string
  date: string
  destination: string
  experiences: Experience[]
  totalCost: number
}

interface TripData {
  id?: string
  destinations: string[]
  days: TripDay[]
  totalDays: number
  totalCost: number
  travelers: number
  status: 'building' | 'saved' | 'quote-requested'
  lastModified: string
  notes?: string
}

interface TripContextType {
  tripData: TripData | null
  addDestination: (destination: string) => void
  removeDestination: (destination: string) => void
  addExperience: (experience: Experience, destination: string) => void
  removeExperience: (experienceId: string, destination: string) => void
  updateTripData: (data: Partial<TripData>) => void
  saveTrip: () => Promise<void>
  requestQuote: () => Promise<void>
  clearTrip: () => void
  isLoading: boolean
}

const TripContext = createContext<TripContextType | undefined>(undefined)

export function TripProvider({ children }: { children: ReactNode }) {
  const [tripData, setTripData] = useState<TripData | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Load trip data from localStorage on mount
  useEffect(() => {
    try {
      const savedTrip = localStorage.getItem('currentTrip')
      if (savedTrip) {
        const trip = JSON.parse(savedTrip)
        setTripData(trip)
      }
    } catch (error) {
      console.error('Error loading trip data:', error)
    }
  }, [])

  // Save to localStorage whenever tripData changes
  useEffect(() => {
    if (tripData) {
      try {
        localStorage.setItem('currentTrip', JSON.stringify(tripData))
        // Trigger storage event for other components
        window.dispatchEvent(new Event('storage'))
      } catch (error) {
        console.error('Error saving trip data:', error)
      }
    }
  }, [tripData])

  const addDestination = (destination: string) => {
    setTripData(prev => {
      const currentTrip = prev || {
        destinations: [],
        days: [],
        totalDays: 0,
        totalCost: 0,
        travelers: 2,
        status: 'building' as const,
        lastModified: new Date().toISOString()
      }

      if (currentTrip.destinations.includes(destination)) {
        return currentTrip
      }

      return {
        ...currentTrip,
        destinations: [...currentTrip.destinations, destination],
        lastModified: new Date().toISOString()
      }
    })
  }

  const removeDestination = (destination: string) => {
    setTripData(prev => {
      if (!prev) return null

      const updatedDestinations = prev.destinations.filter(d => d !== destination)
      const updatedDays = prev.days.filter(day => day.destination !== destination)
      const newTotalCost = updatedDays.reduce((sum, day) => sum + day.totalCost, 0)

      return {
        ...prev,
        destinations: updatedDestinations,
        days: updatedDays,
        totalCost: newTotalCost,
        lastModified: new Date().toISOString()
      }
    })
  }

  const addExperience = (experience: Experience, destination: string) => {
    setTripData(prev => {
      const currentTrip = prev || {
        destinations: [],
        days: [],
        totalDays: 0,
        totalCost: 0,
        travelers: 2,
        status: 'building' as const,
        lastModified: new Date().toISOString()
      }

      // Ensure destination is in the trip
      if (!currentTrip.destinations.includes(destination)) {
        currentTrip.destinations.push(destination)
      }

      // Find or create day for this destination
      let targetDay = currentTrip.days.find(day => day.destination === destination)
      
      if (!targetDay) {
        targetDay = {
          id: `day-${Date.now()}`,
          date: new Date().toISOString().split('T')[0],
          destination,
          experiences: [],
          totalCost: 0
        }
        currentTrip.days.push(targetDay)
      }

      // Add experience if not already added
      if (!targetDay.experiences.find(exp => exp.id === experience.id)) {
        targetDay.experiences.push(experience)
        targetDay.totalCost += experience.price
      }

      const newTotalCost = currentTrip.days.reduce((sum, day) => sum + day.totalCost, 0)

      return {
        ...currentTrip,
        totalCost: newTotalCost,
        lastModified: new Date().toISOString()
      }
    })
  }

  const removeExperience = (experienceId: string, destination: string) => {
    setTripData(prev => {
      if (!prev) return null

      const updatedDays = prev.days.map(day => {
        if (day.destination === destination) {
          const experienceToRemove = day.experiences.find(exp => exp.id === experienceId)
          const updatedExperiences = day.experiences.filter(exp => exp.id !== experienceId)
          const newDayCost = experienceToRemove 
            ? day.totalCost - experienceToRemove.price 
            : day.totalCost

          return {
            ...day,
            experiences: updatedExperiences,
            totalCost: newDayCost
          }
        }
        return day
      })

      const newTotalCost = updatedDays.reduce((sum, day) => sum + day.totalCost, 0)

      return {
        ...prev,
        days: updatedDays,
        totalCost: newTotalCost,
        lastModified: new Date().toISOString()
      }
    })
  }

  const updateTripData = (data: Partial<TripData>) => {
    setTripData(prev => {
      if (!prev) return null
      return {
        ...prev,
        ...data,
        lastModified: new Date().toISOString()
      }
    })
  }

  const saveTrip = async () => {
    if (!tripData) return

    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const savedTrip = {
        ...tripData,
        id: tripData.id || `trip-${Date.now()}`,
        status: 'saved' as const,
        lastModified: new Date().toISOString()
      }

      setTripData(savedTrip)
      
      // Also save to a "savedTrips" array for persistence
      const savedTrips = JSON.parse(localStorage.getItem('savedTrips') || '[]')
      const existingIndex = savedTrips.findIndex((trip: TripData) => trip.id === savedTrip.id)
      
      if (existingIndex >= 0) {
        savedTrips[existingIndex] = savedTrip
      } else {
        savedTrips.push(savedTrip)
      }
      
      localStorage.setItem('savedTrips', JSON.stringify(savedTrips))
      
    } catch (error) {
      console.error('Error saving trip:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const requestQuote = async () => {
    if (!tripData) return

    setIsLoading(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      const quotedTrip = {
        ...tripData,
        status: 'quote-requested' as const,
        lastModified: new Date().toISOString()
      }

      setTripData(quotedTrip)
      
    } catch (error) {
      console.error('Error requesting quote:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const clearTrip = () => {
    setTripData(null)
    localStorage.removeItem('currentTrip')
    window.dispatchEvent(new Event('storage'))
  }

  const value: TripContextType = {
    tripData,
    addDestination,
    removeDestination,
    addExperience,
    removeExperience,
    updateTripData,
    saveTrip,
    requestQuote,
    clearTrip,
    isLoading
  }

  return (
    <TripContext.Provider value={value}>
      {children}
    </TripContext.Provider>
  )
}

export const useTrip = () => {
  const context = useContext(TripContext)
  if (!context) {
    throw new Error('useTrip must be used within TripProvider')
  }
  return context
}