-- Super Admin System Database Schema
-- This file contains the database schema for the super admin system

-- Super Admin Roles Table
CREATE TABLE IF NOT EXISTS super_admin_roles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    role_name VARCHAR(50) UNIQUE NOT NULL,
    display_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Super Admin Users Table
CREATE TABLE IF NOT EXISTS super_admin_users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL REFERENCES super_admin_roles(role_name),
    country_access TEXT[] DEFAULT ARRAY[]::TEXT[],
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES super_admin_users(id)
);

-- Super Admin Sessions Table (for session management)
CREATE TABLE IF NOT EXISTS super_admin_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES super_admin_users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Super Admin Activity Log Table
CREATE TABLE IF NOT EXISTS super_admin_activity_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES super_admin_users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(255),
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Cultural Content Validation Table
CREATE TABLE IF NOT EXISTS cultural_content_validation (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    content_type VARCHAR(50) NOT NULL, -- 'experience', 'destination', 'guide', etc.
    content_id UUID NOT NULL,
    country_code VARCHAR(3) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'approved', 'rejected', 'needs_revision'
    validator_id UUID REFERENCES super_admin_users(id),
    validation_notes TEXT,
    cultural_sensitivity_score INTEGER CHECK (cultural_sensitivity_score >= 1 AND cultural_sensitivity_score <= 10),
    authenticity_score INTEGER CHECK (authenticity_score >= 1 AND authenticity_score <= 10),
    accuracy_score INTEGER CHECK (accuracy_score >= 1 AND accuracy_score <= 10),
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    validated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Country Configuration Table (enhanced)
CREATE TABLE IF NOT EXISTS country_configurations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    country_code VARCHAR(3) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    currency_symbol VARCHAR(10),
    timezone VARCHAR(50) NOT NULL,
    locale VARCHAR(10) DEFAULT 'en',
    flag VARCHAR(10),
    coordinates JSONB, -- {lat: number, lng: number}
    theme_config JSONB DEFAULT '{}',
    features TEXT[] DEFAULT ARRAY[]::TEXT[],
    is_active BOOLEAN DEFAULT false,
    launch_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES super_admin_users(id),
    updated_by UUID REFERENCES super_admin_users(id)
);

-- Content Management Tables
CREATE TABLE IF NOT EXISTS content_categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    country_code VARCHAR(3) REFERENCES country_configurations(country_code),
    parent_id UUID REFERENCES content_categories(id),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enhanced Destinations Table
CREATE TABLE IF NOT EXISTS destinations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    country_code VARCHAR(3) NOT NULL REFERENCES country_configurations(country_code),
    description TEXT,
    short_description TEXT,
    coordinates JSONB, -- {lat: number, lng: number}
    category_id UUID REFERENCES content_categories(id),
    images JSONB DEFAULT '[]',
    cultural_significance TEXT,
    best_time_to_visit TEXT,
    duration_recommendation TEXT,
    difficulty_level VARCHAR(20), -- 'easy', 'moderate', 'challenging'
    price_range VARCHAR(20), -- 'budget', 'mid-range', 'luxury'
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    seo_title VARCHAR(255),
    seo_description TEXT,
    is_featured BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    validation_status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES super_admin_users(id),
    updated_by UUID REFERENCES super_admin_users(id)
);

-- Enhanced Experiences Table
CREATE TABLE IF NOT EXISTS experiences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    country_code VARCHAR(3) NOT NULL REFERENCES country_configurations(country_code),
    destination_id UUID REFERENCES destinations(id),
    description TEXT,
    short_description TEXT,
    category_id UUID REFERENCES content_categories(id),
    duration VARCHAR(50),
    price DECIMAL(10,2),
    currency VARCHAR(3),
    max_participants INTEGER,
    min_age INTEGER,
    difficulty_level VARCHAR(20),
    includes TEXT[],
    excludes TEXT[],
    requirements TEXT[],
    images JSONB DEFAULT '[]',
    cultural_context TEXT,
    local_customs TEXT,
    sustainability_info TEXT,
    tags TEXT[] DEFAULT ARRAY[]::TEXT[],
    seo_title VARCHAR(255),
    seo_description TEXT,
    is_featured BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    validation_status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES super_admin_users(id),
    updated_by UUID REFERENCES super_admin_users(id)
);

-- Insert default roles
INSERT INTO super_admin_roles (role_name, display_name, description, permissions) VALUES
('super_admin', 'Super Administrator', 'Full system access with all permissions', '{
    "canManageCountries": true,
    "canManageUsers": true,
    "canManageContent": true,
    "canValidateCulturalContent": true,
    "canViewAnalytics": true,
    "canManagePartners": true,
    "countryAccess": ["*"]
}'),
('country_admin', 'Country Administrator', 'Manage specific countries and their content', '{
    "canManageCountries": false,
    "canManageUsers": false,
    "canManageContent": true,
    "canValidateCulturalContent": true,
    "canViewAnalytics": true,
    "canManagePartners": true,
    "countryAccess": []
}'),
('content_manager', 'Content Manager', 'Manage content for assigned countries', '{
    "canManageCountries": false,
    "canManageUsers": false,
    "canManageContent": true,
    "canValidateCulturalContent": false,
    "canViewAnalytics": false,
    "canManagePartners": false,
    "countryAccess": []
}'),
('cultural_validator', 'Cultural Validator', 'Validate cultural content for authenticity and sensitivity', '{
    "canManageCountries": false,
    "canManageUsers": false,
    "canManageContent": false,
    "canValidateCulturalContent": true,
    "canViewAnalytics": false,
    "canManagePartners": false,
    "countryAccess": []
}')
ON CONFLICT (role_name) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_super_admin_users_email ON super_admin_users(email);
CREATE INDEX IF NOT EXISTS idx_super_admin_users_role ON super_admin_users(role);
CREATE INDEX IF NOT EXISTS idx_super_admin_sessions_user_id ON super_admin_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_super_admin_sessions_expires_at ON super_admin_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_super_admin_activity_log_user_id ON super_admin_activity_log(user_id);
CREATE INDEX IF NOT EXISTS idx_super_admin_activity_log_created_at ON super_admin_activity_log(created_at);
CREATE INDEX IF NOT EXISTS idx_cultural_content_validation_status ON cultural_content_validation(status);
CREATE INDEX IF NOT EXISTS idx_cultural_content_validation_country ON cultural_content_validation(country_code);
CREATE INDEX IF NOT EXISTS idx_destinations_country_code ON destinations(country_code);
CREATE INDEX IF NOT EXISTS idx_destinations_validation_status ON destinations(validation_status);
CREATE INDEX IF NOT EXISTS idx_experiences_country_code ON experiences(country_code);
CREATE INDEX IF NOT EXISTS idx_experiences_validation_status ON experiences(validation_status);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_super_admin_users_updated_at BEFORE UPDATE ON super_admin_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_country_configurations_updated_at BEFORE UPDATE ON country_configurations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_destinations_updated_at BEFORE UPDATE ON destinations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_experiences_updated_at BEFORE UPDATE ON experiences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
