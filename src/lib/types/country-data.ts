// Country Data Management Types
// Comprehensive type definitions for country-specific data management

export interface CountryCoordinates {
  lat: number
  lng: number
  bounds?: {
    north: number
    south: number
    east: number
    west: number
  }
}

export interface CountryCurrency {
  code: string // ISO 4217 currency code (e.g., 'MAD', 'USD', 'EUR')
  symbol: string // Currency symbol (e.g., 'DH', '$', '€')
  name: string // Full currency name (e.g., 'Moroccan Dirham')
  exchangeRate: number // Rate to USD
  decimalPlaces: number // Number of decimal places (usually 2)
  symbolPosition: 'before' | 'after' // Symbol position relative to amount
}

export interface CountryLanguage {
  code: string // ISO 639-1 language code (e.g., 'ar', 'fr', 'en')
  name: string // Language name (e.g., 'Arabic', 'French')
  nativeName: string // Native language name (e.g., 'العربية', 'Français')
  isOfficial: boolean // Is this an official language?
  isPrimary: boolean // Is this the primary language?
  speakerPercentage: number // Percentage of population that speaks this language
}

export interface CountryWeatherSeason {
  id: string
  name: string // e.g., 'Spring', 'Summer', 'Dry Season', 'Monsoon'
  startMonth: number // 1-12
  endMonth: number // 1-12
  averageTemp: {
    min: number // Celsius
    max: number // Celsius
  }
  rainfall: number // mm per month average
  humidity: number // percentage
  description: string
  activities: string[] // Recommended activities for this season
  clothingRecommendations: string[]
}

export interface CountryTimeZone {
  name: string // e.g., 'Africa/Casablanca'
  offset: string // e.g., '+01:00'
  dstOffset?: string // Daylight saving time offset if applicable
  hasDST: boolean
}

export interface CulturalCustom {
  id: string
  category: 'greeting' | 'dining' | 'religious' | 'social' | 'business' | 'dress' | 'gift-giving' | 'general'
  title: string
  description: string
  importance: 'low' | 'medium' | 'high' | 'critical'
  dosList: string[]
  dontsList: string[]
  context?: string // When/where this applies
  consequences?: string // What happens if not followed
}

export interface CountryEtiquette {
  greetings: {
    formal: string[]
    informal: string[]
    businessCard: string[]
  }
  diningEtiquette: {
    tableManners: string[]
    tipping: {
      restaurants: number // percentage
      cafes: number
      guides: number
      drivers: number
      hotels: number
    }
    dietaryConsiderations: string[]
  }
  religiousConsiderations: {
    majorReligion: string
    religiousHolidays: Array<{
      name: string
      description: string
      impact: string // How it affects travel/business
    }>
    prayerTimes: boolean // Are prayer times observed?
    fridayPrayers: boolean // Is Friday significant?
    ramadanConsiderations: string[]
  }
  socialNorms: {
    personalSpace: string
    eyeContact: string
    handshakes: string
    publicAffection: string
    photography: string[]
    bargaining: string
  }
}

export interface LocalGuide {
  id: string
  name: string
  email: string
  phone: string
  languages: string[] // Language codes
  specializations: string[] // e.g., 'history', 'culture', 'adventure', 'culinary'
  certifications: string[]
  experience: number // years
  rating: number // 1-5
  reviewCount: number
  pricePerDay: number
  pricePerHour: number
  availability: {
    [key: string]: boolean // Date string -> available
  }
  bio: string
  profileImage: string
  coverImage: string
  isVerified: boolean
  isActive: boolean
  location: {
    city: string
    region: string
    coordinates: CountryCoordinates
  }
  createdAt: Date
  updatedAt: Date
}

export interface CountryDestination {
  id: string
  name: string
  slug: string
  description: string
  shortDescription: string
  coordinates: CountryCoordinates
  category: 'city' | 'nature' | 'historical' | 'cultural' | 'adventure' | 'beach' | 'mountain' | 'desert'
  images: Array<{
    url: string
    alt: string
    caption?: string
    photographer?: string
  }>
  culturalSignificance: string
  historicalBackground: string
  bestTimeToVisit: {
    months: number[] // 1-12
    reason: string
  }
  durationRecommendation: {
    minimum: number // days
    recommended: number // days
    maximum: number // days
  }
  difficultyLevel: 'easy' | 'moderate' | 'challenging' | 'expert'
  accessibility: {
    wheelchairAccessible: boolean
    mobilityNotes: string[]
    visuallyImpairedSupport: boolean
    hearingImpairedSupport: boolean
  }
  transportation: {
    fromCapital: {
      methods: Array<{
        type: 'flight' | 'train' | 'bus' | 'car' | 'boat'
        duration: string
        cost: number
        description: string
      }>
    }
    local: string[]
  }
  accommodation: {
    types: string[] // e.g., 'hotel', 'riad', 'guesthouse', 'camping'
    priceRange: {
      budget: { min: number; max: number }
      midRange: { min: number; max: number }
      luxury: { min: number; max: number }
    }
  }
  tags: string[]
  seoTitle: string
  seoDescription: string
  isActive: boolean
  isFeatured: boolean
  validationStatus: 'pending' | 'approved' | 'rejected' | 'needs_revision'
  createdAt: Date
  updatedAt: Date
}

export interface CountryExperience {
  id: string
  title: string
  slug: string
  destinationId: string // Reference to CountryDestination
  description: string
  shortDescription: string
  category: 'cultural' | 'adventure' | 'culinary' | 'historical' | 'nature' | 'spiritual' | 'educational' | 'entertainment'
  subcategory: string // More specific categorization
  duration: {
    value: number
    unit: 'hours' | 'days' | 'weeks'
  }
  groupSize: {
    min: number
    max: number
    recommended: number
  }
  ageRestrictions: {
    minimum?: number
    maximum?: number
    notes?: string
  }
  physicalRequirements: {
    fitnessLevel: 'low' | 'moderate' | 'high' | 'extreme'
    requirements: string[]
    restrictions: string[]
  }
  pricing: {
    basePrice: number
    currency: string
    priceIncludes: string[]
    priceExcludes: string[]
    groupDiscounts?: Array<{
      minSize: number
      discountPercentage: number
    }>
    seasonalPricing?: Array<{
      season: string
      multiplier: number // e.g., 1.2 for 20% increase
    }>
  }
  schedule: {
    availability: 'daily' | 'weekly' | 'seasonal' | 'on-demand'
    startTimes: string[] // e.g., ['09:00', '14:00']
    blackoutDates: string[] // ISO date strings
    advanceBooking: number // days required
  }
  culturalContext: {
    significance: string
    traditions: string[]
    etiquette: string[]
    respectfulBehavior: string[]
  }
  sustainability: {
    environmentalImpact: 'low' | 'medium' | 'high'
    communityBenefit: string
    conservationEfforts: string[]
    responsiblePractices: string[]
  }
  images: Array<{
    url: string
    alt: string
    caption?: string
    type: 'main' | 'gallery' | 'activity' | 'location'
  }>
  includes: string[]
  excludes: string[]
  requirements: string[]
  cancellationPolicy: {
    freeCancellation: number // hours before
    partialRefund: number // hours before
    noRefund: number // hours before
    terms: string
  }
  tags: string[]
  seoTitle: string
  seoDescription: string
  isActive: boolean
  isFeatured: boolean
  validationStatus: 'pending' | 'approved' | 'rejected' | 'needs_revision'
  providerId?: string // Reference to experience provider
  guideId?: string // Reference to local guide
  createdAt: Date
  updatedAt: Date
}

export interface CountryData {
  id: string
  code: string // ISO 3166-1 alpha-3 country code
  name: string
  displayName: string
  flag: string
  coordinates: CountryCoordinates
  currency: CountryCurrency
  languages: CountryLanguage[]
  timeZone: CountryTimeZone
  
  // Geographic and Climate Data
  climate: {
    type: string // e.g., 'Mediterranean', 'Desert', 'Tropical'
    description: string
    seasons: CountryWeatherSeason[]
  }
  
  // Cultural Information
  culture: {
    customs: CulturalCustom[]
    etiquette: CountryEtiquette
    festivals: Array<{
      name: string
      date: string // or date range
      description: string
      significance: string
    }>
    cuisine: {
      description: string
      signature_dishes: string[]
      dietary_considerations: string[]
      dining_customs: string[]
    }
  }
  
  // Travel Information
  travel: {
    visaRequirements: {
      [countryCode: string]: {
        required: boolean
        type?: 'visa_free' | 'visa_on_arrival' | 'e_visa' | 'embassy_visa'
        duration?: number // days
        notes?: string
      }
    }
    healthRequirements: {
      vaccinations: Array<{
        name: string
        required: boolean
        recommended: boolean
        notes?: string
      }>
      healthInsurance: boolean
      medicalFacilities: string
    }
    safety: {
      level: 'low' | 'moderate' | 'high' | 'extreme'
      considerations: string[]
      emergencyNumbers: {
        police: string
        medical: string
        fire: string
        tourist_police?: string
      }
    }
  }
  
  // Platform Configuration
  theme: {
    primaryColor: string
    secondaryColor: string
    accentColor: string
    gradients: {
      primary: string
      secondary: string
    }
  }
  
  features: string[] // Enabled features for this country
  isActive: boolean
  launchDate?: Date
  
  // SEO and Marketing
  seo: {
    title: string
    description: string
    keywords: string[]
    ogImage: string
  }
  
  // Data Management
  destinations: CountryDestination[]
  experiences: CountryExperience[]
  guides: LocalGuide[]
  
  createdAt: Date
  updatedAt: Date
  createdBy: string // Super admin user ID
  updatedBy: string // Super admin user ID
}
