import { createClient as createSupabaseClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createSupabaseClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
})

// Comprehensive Database types for International Platform
export interface Database {
  public: {
    Tables: {
      // Core user management
      users: {
        Row: {
          id: string
          email: string
          name: string
          avatar_url: string | null
          role: 'customer' | 'partner' | 'country_admin' | 'super_admin'
          preferred_country: string
          cultural_sensitivity_level: number
          travel_preferences: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          name: string
          avatar_url?: string | null
          role?: 'customer' | 'partner' | 'country_admin' | 'super_admin'
          preferred_country?: string
          cultural_sensitivity_level?: number
          travel_preferences?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          avatar_url?: string | null
          role?: 'customer' | 'partner' | 'country_admin' | 'super_admin'
          preferred_country?: string
          cultural_sensitivity_level?: number
          travel_preferences?: any
          created_at?: string
          updated_at?: string
        }
      }
      
      // Country management
      countries: {
        Row: {
          id: string
          code: string
          name: string
          display_name: string
          currency: string
          currency_symbol: string
          timezone: string
          is_active: boolean
          theme_config: any
          coordinates: any
          features: string[]
          flag: string
          created_at: string
        }
        Insert: {
          id?: string
          code: string
          name: string
          display_name: string
          currency: string
          currency_symbol?: string
          timezone: string
          is_active?: boolean
          theme_config?: any
          coordinates?: any
          features?: string[]
          flag?: string
          created_at?: string
        }
        Update: {
          id?: string
          code?: string
          name?: string
          display_name?: string
          currency?: string
          currency_symbol?: string
          timezone?: string
          is_active?: boolean
          theme_config?: any
          coordinates?: any
          features?: string[]
          flag?: string
          created_at?: string
        }
      }
      
      country_admins: {
        Row: {
          id: string
          user_id: string
          country_id: string
          permissions: string[]
          assigned_by: string | null
          assigned_at: string
          is_active: boolean
        }
        Insert: {
          id?: string
          user_id: string
          country_id: string
          permissions?: string[]
          assigned_by?: string | null
          assigned_at?: string
          is_active?: boolean
        }
        Update: {
          id?: string
          user_id?: string
          country_id?: string
          permissions?: string[]
          assigned_by?: string | null
          assigned_at?: string
          is_active?: boolean
        }
      }
      
      country_settings: {
        Row: {
          id: string
          country_id: string
          setting_key: string
          setting_value: any
          description: string | null
          updated_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          country_id: string
          setting_key: string
          setting_value: any
          description?: string | null
          updated_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          country_id?: string
          setting_key?: string
          setting_value?: any
          description?: string | null
          updated_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      
      // Content management
      destinations: {
        Row: {
          id: string
          country_id: string
          name: string
          slug: string
          description: string | null
          image_url: string | null
          coordinates: any
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          country_id: string
          name: string
          slug: string
          description?: string | null
          image_url?: string | null
          coordinates?: any
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          country_id?: string
          name?: string
          slug?: string
          description?: string | null
          image_url?: string | null
          coordinates?: any
          is_active?: boolean
          created_at?: string
        }
      }
      
      experiences: {
        Row: {
          id: string
          destination_id: string
          title: string
          description: string | null
          category: string
          duration: string | null
          price: number | null
          cultural_significance: string | null
          image_url: string | null
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          destination_id: string
          title: string
          description?: string | null
          category: string
          duration?: string | null
          price?: number | null
          cultural_significance?: string | null
          image_url?: string | null
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          destination_id?: string
          title?: string
          description?: string | null
          category?: string
          duration?: string | null
          price?: number | null
          cultural_significance?: string | null
          image_url?: string | null
          is_active?: boolean
          created_at?: string
        }
      }
      
      cultural_content: {
        Row: {
          id: string
          country_id: string
          content_type: 'custom' | 'etiquette' | 'language' | 'history' | 'tradition' | 'festival'
          title: string
          content: string
          media_urls: string[]
          tags: string[]
          difficulty_level: number
          cultural_sensitivity_score: number
          is_approved: boolean
          approved_by: string | null
          approved_at: string | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          country_id: string
          content_type: 'custom' | 'etiquette' | 'language' | 'history' | 'tradition' | 'festival'
          title: string
          content: string
          media_urls?: string[]
          tags?: string[]
          difficulty_level?: number
          cultural_sensitivity_score?: number
          is_approved?: boolean
          approved_by?: string | null
          approved_at?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          country_id?: string
          content_type?: 'custom' | 'etiquette' | 'language' | 'history' | 'tradition' | 'festival'
          title?: string
          content?: string
          media_urls?: string[]
          tags?: string[]
          difficulty_level?: number
          cultural_sensitivity_score?: number
          is_approved?: boolean
          approved_by?: string | null
          approved_at?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      
      // Local service providers
      guides: {
        Row: {
          id: string
          country_id: string
          user_id: string | null
          name: string
          email: string | null
          phone: string | null
          languages: string[]
          specializations: string[]
          bio: string | null
          avatar_url: string | null
          rating: number
          total_reviews: number
          is_verified: boolean
          is_active: boolean
          availability_calendar: any
          pricing: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          country_id: string
          user_id?: string | null
          name: string
          email?: string | null
          phone?: string | null
          languages?: string[]
          specializations?: string[]
          bio?: string | null
          avatar_url?: string | null
          rating?: number
          total_reviews?: number
          is_verified?: boolean
          is_active?: boolean
          availability_calendar?: any
          pricing?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          country_id?: string
          user_id?: string | null
          name?: string
          email?: string | null
          phone?: string | null
          languages?: string[]
          specializations?: string[]
          bio?: string | null
          avatar_url?: string | null
          rating?: number
          total_reviews?: number
          is_verified?: boolean
          is_active?: boolean
          availability_calendar?: any
          pricing?: any
          created_at?: string
          updated_at?: string
        }
      }
      
      // Trip management
      user_trips: {
        Row: {
          id: string
          user_id: string
          title: string
          country_code: string
          destinations: any
          experiences: any
          total_cost: number
          total_days: number
          status: 'planning' | 'booked' | 'completed' | 'cancelled'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          title: string
          country_code: string
          destinations?: any
          experiences?: any
          total_cost?: number
          total_days?: number
          status?: 'planning' | 'booked' | 'completed' | 'cancelled'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          title?: string
          country_code?: string
          destinations?: any
          experiences?: any
          total_cost?: number
          total_days?: number
          status?: 'planning' | 'booked' | 'completed' | 'cancelled'
          created_at?: string
          updated_at?: string
        }
      }
      
      trip_collaborators: {
        Row: {
          id: string
          trip_id: string
          user_id: string
          role: 'owner' | 'editor' | 'viewer'
          invited_by: string | null
          invited_at: string
          joined_at: string | null
          is_active: boolean
        }
        Insert: {
          id?: string
          trip_id: string
          user_id: string
          role?: 'owner' | 'editor' | 'viewer'
          invited_by?: string | null
          invited_at?: string
          joined_at?: string | null
          is_active?: boolean
        }
        Update: {
          id?: string
          trip_id?: string
          user_id?: string
          role?: 'owner' | 'editor' | 'viewer'
          invited_by?: string | null
          invited_at?: string
          joined_at?: string | null
          is_active?: boolean
        }
      }
      
      // Notifications
      notifications: {
        Row: {
          id: string
          user_id: string
          notification_type: string
          title: string
          content: string
          data: any
          delivery_method: 'email' | 'push' | 'in_app' | 'sms'
          status: 'pending' | 'sent' | 'delivered' | 'failed' | 'read'
          scheduled_for: string
          sent_at: string | null
          read_at: string | null
          error_message: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id: string
          notification_type: string
          title: string
          content: string
          data?: any
          delivery_method: 'email' | 'push' | 'in_app' | 'sms'
          status?: 'pending' | 'sent' | 'delivered' | 'failed' | 'read'
          scheduled_for?: string
          sent_at?: string | null
          read_at?: string | null
          error_message?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          notification_type?: string
          title?: string
          content?: string
          data?: any
          delivery_method?: 'email' | 'push' | 'in_app' | 'sms'
          status?: 'pending' | 'sent' | 'delivered' | 'failed' | 'read'
          scheduled_for?: string
          sent_at?: string | null
          read_at?: string | null
          error_message?: string | null
          created_at?: string
        }
      }
      
      // Bookings and payments
      bookings: {
        Row: {
          id: string
          user_id: string
          experience_id: string
          booking_date: string
          booking_time: string | null
          travelers: number
          total_price: number
          status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'refunded'
          special_requests: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          experience_id: string
          booking_date: string
          booking_time?: string | null
          travelers?: number
          total_price: number
          status?: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'refunded'
          special_requests?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          experience_id?: string
          booking_date?: string
          booking_time?: string | null
          travelers?: number
          total_price?: number
          status?: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'refunded'
          special_requests?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      
      // Cultural progress tracking
      cultural_progress: {
        Row: {
          id: string
          user_id: string
          country_code: string
          total_score: number
          level: number
          streak: number
          completed_quizzes: string[]
          learned_phrases: string[]
          achievements: string[]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          country_code: string
          total_score?: number
          level?: number
          streak?: number
          completed_quizzes?: string[]
          learned_phrases?: string[]
          achievements?: string[]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          country_code?: string
          total_score?: number
          level?: number
          streak?: number
          completed_quizzes?: string[]
          learned_phrases?: string[]
          achievements?: string[]
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      country_stats: {
        Row: {
          id: string
          code: string
          name: string
          display_name: string
          is_active: boolean
          total_destinations: number
          total_experiences: number
          total_guides: number
          total_users: number
          avg_guide_rating: number
        }
      }
      user_trip_summary: {
        Row: {
          id: string
          title: string
          country_code: string
          status: string
          total_cost: number
          total_days: number
          created_at: string
          updated_at: string
          owner_name: string
          owner_email: string
          collaborator_count: number
          activity_count: number
          comment_count: number
        }
      }
      experience_details: {
        Row: {
          id: string
          title: string
          description: string | null
          category: string
          duration: string | null
          cultural_significance: string | null
          image_url: string | null
          is_active: boolean
          destination_name: string
          destination_slug: string
          country_name: string
          country_code: string
          currency: string
          min_price: number | null
          max_price: number | null
          pricing_tiers: number
          available_dates: number
          avg_rating: number | null
        }
      }
    }
  }
}

// Type helpers for common operations
export type UserRole = Database['public']['Tables']['users']['Row']['role']
export type TripStatus = Database['public']['Tables']['user_trips']['Row']['status']
export type BookingStatus = Database['public']['Tables']['bookings']['Row']['status']
export type NotificationStatus = Database['public']['Tables']['notifications']['Row']['status']
export type CulturalContentType = Database['public']['Tables']['cultural_content']['Row']['content_type']

// Country context type for international platform
export interface CountryContext {
  id: string
  code: string
  name: string
  displayName: string
  currency: string
  currencySymbol: string
  timezone: string
  isActive: boolean
  themeConfig: {
    primary?: string
    secondary?: string
    accent?: string
    background?: string
  }
  coordinates: {
    lat: number
    lng: number
  }
  features: string[]
  flag: string
}

// Export createClient function for compatibility
export const createClient = createSupabaseClient