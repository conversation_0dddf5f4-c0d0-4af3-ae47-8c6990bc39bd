// Country Data Management Service
// Comprehensive service for managing country-specific data

import { 
  CountryData, 
  CountryDestination, 
  CountryExperience, 
  LocalGuide,
  CountryCurrency,
  CountryWeatherSeason 
} from '@/lib/types/country-data'

export class CountryDataService {
  private static instance: CountryDataService
  private cache: Map<string, CountryData> = new Map()
  private cacheExpiry: Map<string, number> = new Map()
  private readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  static getInstance(): CountryDataService {
    if (!CountryDataService.instance) {
      CountryDataService.instance = new CountryDataService()
    }
    return CountryDataService.instance
  }

  // Country Management
  async getCountryData(countryCode: string): Promise<CountryData | null> {
    const cacheKey = `country_${countryCode.toUpperCase()}`
    
    // Check cache first
    if (this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey) || null
    }

    try {
      // In a real implementation, this would fetch from database
      const countryData = await this.fetchCountryFromDatabase(countryCode)
      
      if (countryData) {
        this.cache.set(cacheKey, countryData)
        this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_DURATION)
      }
      
      return countryData
    } catch (error) {
      console.error(`Failed to fetch country data for ${countryCode}:`, error)
      return null
    }
  }

  async getAllCountries(): Promise<CountryData[]> {
    try {
      // In a real implementation, this would fetch from database
      return await this.fetchAllCountriesFromDatabase()
    } catch (error) {
      console.error('Failed to fetch all countries:', error)
      return []
    }
  }

  async createCountry(countryData: Omit<CountryData, 'id' | 'createdAt' | 'updatedAt'>): Promise<CountryData> {
    try {
      const newCountry: CountryData = {
        ...countryData,
        id: this.generateId(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // In a real implementation, this would save to database
      await this.saveCountryToDatabase(newCountry)
      
      // Update cache
      const cacheKey = `country_${newCountry.code.toUpperCase()}`
      this.cache.set(cacheKey, newCountry)
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_DURATION)
      
      return newCountry
    } catch (error) {
      console.error('Failed to create country:', error)
      throw error
    }
  }

  async updateCountry(countryCode: string, updates: Partial<CountryData>): Promise<CountryData | null> {
    try {
      const existingCountry = await this.getCountryData(countryCode)
      if (!existingCountry) {
        throw new Error(`Country ${countryCode} not found`)
      }

      const updatedCountry: CountryData = {
        ...existingCountry,
        ...updates,
        updatedAt: new Date()
      }

      // In a real implementation, this would update in database
      await this.updateCountryInDatabase(updatedCountry)
      
      // Update cache
      const cacheKey = `country_${countryCode.toUpperCase()}`
      this.cache.set(cacheKey, updatedCountry)
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_DURATION)
      
      return updatedCountry
    } catch (error) {
      console.error(`Failed to update country ${countryCode}:`, error)
      throw error
    }
  }

  // Destination Management
  async getDestinations(countryCode: string): Promise<CountryDestination[]> {
    const countryData = await this.getCountryData(countryCode)
    return countryData?.destinations || []
  }

  async getDestination(countryCode: string, destinationId: string): Promise<CountryDestination | null> {
    const destinations = await this.getDestinations(countryCode)
    return destinations.find(d => d.id === destinationId) || null
  }

  async createDestination(countryCode: string, destination: Omit<CountryDestination, 'id' | 'createdAt' | 'updatedAt'>): Promise<CountryDestination> {
    try {
      const newDestination: CountryDestination = {
        ...destination,
        id: this.generateId(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Add to country data
      const countryData = await this.getCountryData(countryCode)
      if (!countryData) {
        throw new Error(`Country ${countryCode} not found`)
      }

      countryData.destinations.push(newDestination)
      await this.updateCountry(countryCode, { destinations: countryData.destinations })
      
      return newDestination
    } catch (error) {
      console.error('Failed to create destination:', error)
      throw error
    }
  }

  // Experience Management
  async getExperiences(countryCode: string, destinationId?: string): Promise<CountryExperience[]> {
    const countryData = await this.getCountryData(countryCode)
    let experiences = countryData?.experiences || []
    
    if (destinationId) {
      experiences = experiences.filter(e => e.destinationId === destinationId)
    }
    
    return experiences
  }

  async createExperience(countryCode: string, experience: Omit<CountryExperience, 'id' | 'createdAt' | 'updatedAt'>): Promise<CountryExperience> {
    try {
      const newExperience: CountryExperience = {
        ...experience,
        id: this.generateId(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Add to country data
      const countryData = await this.getCountryData(countryCode)
      if (!countryData) {
        throw new Error(`Country ${countryCode} not found`)
      }

      countryData.experiences.push(newExperience)
      await this.updateCountry(countryCode, { experiences: countryData.experiences })
      
      return newExperience
    } catch (error) {
      console.error('Failed to create experience:', error)
      throw error
    }
  }

  // Guide Management
  async getGuides(countryCode: string, city?: string): Promise<LocalGuide[]> {
    const countryData = await this.getCountryData(countryCode)
    let guides = countryData?.guides || []
    
    if (city) {
      guides = guides.filter(g => g.location.city.toLowerCase() === city.toLowerCase())
    }
    
    return guides.filter(g => g.isActive)
  }

  async createGuide(countryCode: string, guide: Omit<LocalGuide, 'id' | 'createdAt' | 'updatedAt'>): Promise<LocalGuide> {
    try {
      const newGuide: LocalGuide = {
        ...guide,
        id: this.generateId(),
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Add to country data
      const countryData = await this.getCountryData(countryCode)
      if (!countryData) {
        throw new Error(`Country ${countryCode} not found`)
      }

      countryData.guides.push(newGuide)
      await this.updateCountry(countryCode, { guides: countryData.guides })
      
      return newGuide
    } catch (error) {
      console.error('Failed to create guide:', error)
      throw error
    }
  }

  // Currency Management
  async updateCurrencyRates(countryCode: string): Promise<void> {
    try {
      const countryData = await this.getCountryData(countryCode)
      if (!countryData) {
        throw new Error(`Country ${countryCode} not found`)
      }

      // In a real implementation, this would fetch from a currency API
      const newRate = await this.fetchCurrencyRate(countryData.currency.code)
      
      const updatedCurrency: CountryCurrency = {
        ...countryData.currency,
        exchangeRate: newRate
      }

      await this.updateCountry(countryCode, { currency: updatedCurrency })
    } catch (error) {
      console.error(`Failed to update currency rates for ${countryCode}:`, error)
      throw error
    }
  }

  // Weather Data Management
  async updateWeatherData(countryCode: string): Promise<void> {
    try {
      const countryData = await this.getCountryData(countryCode)
      if (!countryData) {
        throw new Error(`Country ${countryCode} not found`)
      }

      // In a real implementation, this would fetch from a weather API
      const updatedSeasons = await this.fetchWeatherData(countryCode)
      
      const updatedClimate = {
        ...countryData.climate,
        seasons: updatedSeasons
      }

      await this.updateCountry(countryCode, { climate: updatedClimate })
    } catch (error) {
      console.error(`Failed to update weather data for ${countryCode}:`, error)
      throw error
    }
  }

  // Search and Filtering
  async searchDestinations(countryCode: string, query: string, filters?: {
    category?: string
    difficultyLevel?: string
    priceRange?: { min: number; max: number }
  }): Promise<CountryDestination[]> {
    const destinations = await this.getDestinations(countryCode)
    
    let filtered = destinations.filter(d => 
      d.name.toLowerCase().includes(query.toLowerCase()) ||
      d.description.toLowerCase().includes(query.toLowerCase()) ||
      d.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    )

    if (filters?.category) {
      filtered = filtered.filter(d => d.category === filters.category)
    }

    if (filters?.difficultyLevel) {
      filtered = filtered.filter(d => d.difficultyLevel === filters.difficultyLevel)
    }

    return filtered
  }

  // Utility Methods
  private isCacheValid(key: string): boolean {
    const expiry = this.cacheExpiry.get(key)
    return expiry ? Date.now() < expiry : false
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  private async fetchCountryFromDatabase(countryCode: string): Promise<CountryData | null> {
    // Mock implementation - replace with actual database call
    return this.getMockCountryData(countryCode)
  }

  private async fetchAllCountriesFromDatabase(): Promise<CountryData[]> {
    // Mock implementation - replace with actual database call
    return [
      this.getMockCountryData('MAR'),
      this.getMockCountryData('JPN'),
      this.getMockCountryData('ITA')
    ].filter(Boolean) as CountryData[]
  }

  private async saveCountryToDatabase(country: CountryData): Promise<void> {
    // Mock implementation - replace with actual database call
    console.log('Saving country to database:', country.code)
  }

  private async updateCountryInDatabase(country: CountryData): Promise<void> {
    // Mock implementation - replace with actual database call
    console.log('Updating country in database:', country.code)
  }

  private async fetchCurrencyRate(currencyCode: string): Promise<number> {
    // Mock implementation - replace with actual currency API call
    const mockRates: { [key: string]: number } = {
      'MAD': 0.10, // 1 MAD = 0.10 USD
      'JPY': 0.0067, // 1 JPY = 0.0067 USD
      'EUR': 1.08 // 1 EUR = 1.08 USD
    }
    return mockRates[currencyCode] || 1
  }

  private async fetchWeatherData(countryCode: string): Promise<CountryWeatherSeason[]> {
    // Mock implementation - replace with actual weather API call
    return []
  }

  private getMockCountryData(countryCode: string): CountryData | null {
    // This would be replaced with actual database queries
    // For now, return null to indicate no data
    return null
  }
}
