// Multi-Currency Support System
// Handles currency conversion, formatting, and country-specific pricing

import { CountryCurrency } from '@/lib/types/country-data'

export interface CurrencyConversion {
  from: string
  to: string
  rate: number
  amount: number
  convertedAmount: number
  timestamp: Date
}

export interface PriceDisplay {
  amount: number
  currency: CountryCurrency
  formatted: string
  symbol: string
}

export class CurrencyService {
  private static instance: CurrencyService
  private exchangeRates: Map<string, number> = new Map()
  private ratesLastUpdated: Date | null = null
  private readonly RATE_CACHE_DURATION = 60 * 60 * 1000 // 1 hour

  // Supported currencies with their configurations
  private readonly SUPPORTED_CURRENCIES: { [code: string]: CountryCurrency } = {
    'USD': {
      code: 'USD',
      symbol: '$',
      name: 'US Dollar',
      exchangeRate: 1.0, // Base currency
      decimalPlaces: 2,
      symbolPosition: 'before'
    },
    'EUR': {
      code: 'EUR',
      symbol: '€',
      name: 'Euro',
      exchangeRate: 0.92,
      decimalPlaces: 2,
      symbolPosition: 'before'
    },
    'MAD': {
      code: 'MAD',
      symbol: 'DH',
      name: 'Moroccan Dirham',
      exchangeRate: 10.12,
      decimalPlaces: 2,
      symbolPosition: 'after'
    },
    'JPY': {
      code: 'JPY',
      symbol: '¥',
      name: 'Japanese Yen',
      exchangeRate: 149.50,
      decimalPlaces: 0,
      symbolPosition: 'before'
    },
    'GBP': {
      code: 'GBP',
      symbol: '£',
      name: 'British Pound',
      exchangeRate: 0.79,
      decimalPlaces: 2,
      symbolPosition: 'before'
    },
    'CAD': {
      code: 'CAD',
      symbol: 'C$',
      name: 'Canadian Dollar',
      exchangeRate: 1.36,
      decimalPlaces: 2,
      symbolPosition: 'before'
    },
    'AUD': {
      code: 'AUD',
      symbol: 'A$',
      name: 'Australian Dollar',
      exchangeRate: 1.53,
      decimalPlaces: 2,
      symbolPosition: 'before'
    }
  }

  static getInstance(): CurrencyService {
    if (!CurrencyService.instance) {
      CurrencyService.instance = new CurrencyService()
    }
    return CurrencyService.instance
  }

  constructor() {
    this.initializeExchangeRates()
  }

  // Currency Conversion
  async convertCurrency(amount: number, fromCurrency: string, toCurrency: string): Promise<CurrencyConversion> {
    await this.ensureRatesAreUpdated()

    const fromRate = this.exchangeRates.get(fromCurrency.toUpperCase()) || 1
    const toRate = this.exchangeRates.get(toCurrency.toUpperCase()) || 1
    
    // Convert to USD first, then to target currency
    const usdAmount = amount / fromRate
    const convertedAmount = usdAmount * toRate
    const rate = toRate / fromRate

    return {
      from: fromCurrency.toUpperCase(),
      to: toCurrency.toUpperCase(),
      rate,
      amount,
      convertedAmount,
      timestamp: new Date()
    }
  }

  // Price Formatting
  formatPrice(amount: number, currencyCode: string, options?: {
    showSymbol?: boolean
    showCode?: boolean
    precision?: number
  }): PriceDisplay {
    const currency = this.getCurrencyConfig(currencyCode)
    const showSymbol = options?.showSymbol !== false
    const showCode = options?.showCode || false
    const precision = options?.precision ?? currency.decimalPlaces

    // Round to appropriate decimal places
    const roundedAmount = Number(amount.toFixed(precision))
    
    // Format the number with appropriate decimal places
    const numberFormatted = roundedAmount.toLocaleString('en-US', {
      minimumFractionDigits: precision,
      maximumFractionDigits: precision
    })

    let formatted = numberFormatted

    // Add symbol
    if (showSymbol) {
      if (currency.symbolPosition === 'before') {
        formatted = `${currency.symbol}${formatted}`
      } else {
        formatted = `${formatted} ${currency.symbol}`
      }
    }

    // Add currency code
    if (showCode) {
      formatted = `${formatted} ${currency.code}`
    }

    return {
      amount: roundedAmount,
      currency,
      formatted,
      symbol: currency.symbol
    }
  }

  // Country-Specific Pricing
  async getPriceInCountryCurrency(basePrice: number, baseCurrency: string, targetCountryCode: string): Promise<PriceDisplay> {
    const targetCurrency = this.getCountryCurrency(targetCountryCode)
    
    if (baseCurrency.toUpperCase() === targetCurrency.code.toUpperCase()) {
      return this.formatPrice(basePrice, targetCurrency.code)
    }

    const conversion = await this.convertCurrency(basePrice, baseCurrency, targetCurrency.code)
    return this.formatPrice(conversion.convertedAmount, targetCurrency.code)
  }

  // Price Range Formatting
  formatPriceRange(minPrice: number, maxPrice: number, currencyCode: string): string {
    const minFormatted = this.formatPrice(minPrice, currencyCode)
    const maxFormatted = this.formatPrice(maxPrice, currencyCode)
    
    if (minPrice === maxPrice) {
      return minFormatted.formatted
    }
    
    return `${minFormatted.formatted} - ${maxFormatted.formatted}`
  }

  // Multi-Currency Price Display
  getMultiCurrencyPrices(basePrice: number, baseCurrency: string, targetCurrencies: string[]): Promise<PriceDisplay[]> {
    return Promise.all(
      targetCurrencies.map(async (currency) => {
        if (currency.toUpperCase() === baseCurrency.toUpperCase()) {
          return this.formatPrice(basePrice, currency)
        }
        
        const conversion = await this.convertCurrency(basePrice, baseCurrency, currency)
        return this.formatPrice(conversion.convertedAmount, currency)
      })
    )
  }

  // Currency Detection and Preferences
  detectUserCurrency(): string {
    // Try to detect user's currency from various sources
    
    // 1. Check localStorage for saved preference
    const savedCurrency = localStorage.getItem('preferred_currency')
    if (savedCurrency && this.isSupportedCurrency(savedCurrency)) {
      return savedCurrency
    }

    // 2. Try to detect from browser locale
    try {
      const locale = navigator.language || 'en-US'
      const currencyFromLocale = this.getCurrencyFromLocale(locale)
      if (currencyFromLocale) {
        return currencyFromLocale
      }
    } catch (error) {
      console.warn('Could not detect currency from locale:', error)
    }

    // 3. Default to USD
    return 'USD'
  }

  setUserCurrencyPreference(currencyCode: string): void {
    if (this.isSupportedCurrency(currencyCode)) {
      localStorage.setItem('preferred_currency', currencyCode.toUpperCase())
    }
  }

  // Exchange Rate Management
  async updateExchangeRates(): Promise<void> {
    try {
      // In a real implementation, this would fetch from a currency API
      const rates = await this.fetchLatestExchangeRates()
      
      for (const [currency, rate] of Object.entries(rates)) {
        this.exchangeRates.set(currency, rate)
        
        // Update the currency config
        if (this.SUPPORTED_CURRENCIES[currency]) {
          this.SUPPORTED_CURRENCIES[currency].exchangeRate = rate
        }
      }
      
      this.ratesLastUpdated = new Date()
    } catch (error) {
      console.error('Failed to update exchange rates:', error)
    }
  }

  // Utility Methods
  getSupportedCurrencies(): CountryCurrency[] {
    return Object.values(this.SUPPORTED_CURRENCIES)
  }

  isSupportedCurrency(currencyCode: string): boolean {
    return currencyCode.toUpperCase() in this.SUPPORTED_CURRENCIES
  }

  getCurrencyConfig(currencyCode: string): CountryCurrency {
    const currency = this.SUPPORTED_CURRENCIES[currencyCode.toUpperCase()]
    if (!currency) {
      throw new Error(`Unsupported currency: ${currencyCode}`)
    }
    return currency
  }

  private getCountryCurrency(countryCode: string): CountryCurrency {
    // Map country codes to their currencies
    const countryCurrencyMap: { [countryCode: string]: string } = {
      'MAR': 'MAD', // Morocco
      'JPN': 'JPY', // Japan
      'USA': 'USD', // United States
      'GBR': 'GBP', // United Kingdom
      'CAN': 'CAD', // Canada
      'AUS': 'AUD', // Australia
      'FRA': 'EUR', // France
      'DEU': 'EUR', // Germany
      'ITA': 'EUR', // Italy
      'ESP': 'EUR', // Spain
    }

    const currencyCode = countryCurrencyMap[countryCode.toUpperCase()] || 'USD'
    return this.getCurrencyConfig(currencyCode)
  }

  private getCurrencyFromLocale(locale: string): string | null {
    const localeMap: { [locale: string]: string } = {
      'en-US': 'USD',
      'en-GB': 'GBP',
      'en-CA': 'CAD',
      'en-AU': 'AUD',
      'fr-FR': 'EUR',
      'de-DE': 'EUR',
      'it-IT': 'EUR',
      'es-ES': 'EUR',
      'ja-JP': 'JPY',
      'ar-MA': 'MAD'
    }

    return localeMap[locale] || null
  }

  private async ensureRatesAreUpdated(): Promise<void> {
    const now = new Date()
    const shouldUpdate = !this.ratesLastUpdated || 
      (now.getTime() - this.ratesLastUpdated.getTime()) > this.RATE_CACHE_DURATION

    if (shouldUpdate) {
      await this.updateExchangeRates()
    }
  }

  private initializeExchangeRates(): void {
    // Initialize with default rates
    for (const [code, currency] of Object.entries(this.SUPPORTED_CURRENCIES)) {
      this.exchangeRates.set(code, currency.exchangeRate)
    }
  }

  private async fetchLatestExchangeRates(): Promise<{ [currency: string]: number }> {
    // Mock implementation - in production, this would call a real currency API
    // like exchangerate-api.com, fixer.io, or similar

    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          'USD': 1.0,
          'EUR': 0.92 + (Math.random() - 0.5) * 0.02,
          'GBP': 0.79 + (Math.random() - 0.5) * 0.02,
          'JPY': 149.50 + (Math.random() - 0.5) * 2,
          'MAD': 10.12 + (Math.random() - 0.5) * 0.2,
          'CAD': 1.36 + (Math.random() - 0.5) * 0.02,
          'AUD': 1.53 + (Math.random() - 0.5) * 0.02
        })
      }, 100)
    })
  }
}
}
