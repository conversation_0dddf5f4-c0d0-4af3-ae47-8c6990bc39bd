// Weather and Seasonal Data Service
// Manages weather information, seasonal recommendations, and climate data

import { CountryWeatherSeason } from '@/lib/types/country-data'

export interface WeatherData {
  location: string
  countryCode: string
  current: {
    temperature: number // Celsius
    humidity: number // percentage
    windSpeed: number // km/h
    condition: string
    icon: string
    uvIndex: number
    visibility: number // km
  }
  forecast: Array<{
    date: string
    high: number
    low: number
    condition: string
    icon: string
    precipitation: number // mm
    humidity: number
  }>
  lastUpdated: Date
}

export interface SeasonalRecommendation {
  season: string
  months: number[]
  recommendation: 'excellent' | 'good' | 'fair' | 'poor'
  reasons: string[]
  activities: string[]
  packingList: string[]
  healthConsiderations: string[]
}

export interface ClimateInfo {
  type: string
  description: string
  averageTemperature: {
    annual: { min: number; max: number }
    seasonal: { [season: string]: { min: number; max: number } }
  }
  rainfall: {
    annual: number // mm
    seasonal: { [season: string]: number }
  }
  bestMonths: number[]
  worstMonths: number[]
}

export class WeatherService {
  private static instance: WeatherService
  private weatherCache: Map<string, WeatherData> = new Map()
  private cacheExpiry: Map<string, number> = new Map()
  private readonly CACHE_DURATION = 30 * 60 * 1000 // 30 minutes

  // Seasonal data for different countries
  private readonly SEASONAL_DATA: { [countryCode: string]: CountryWeatherSeason[] } = {
    'MAR': [ // Morocco
      {
        id: 'spring',
        name: 'Spring',
        startMonth: 3,
        endMonth: 5,
        averageTemp: { min: 15, max: 25 },
        rainfall: 40,
        humidity: 60,
        description: 'Mild temperatures with occasional rain. Perfect for sightseeing and outdoor activities.',
        activities: ['hiking', 'city tours', 'desert trips', 'coastal visits'],
        clothingRecommendations: ['light layers', 'light jacket', 'comfortable walking shoes', 'sun hat']
      },
      {
        id: 'summer',
        name: 'Summer',
        startMonth: 6,
        endMonth: 8,
        averageTemp: { min: 20, max: 35 },
        rainfall: 5,
        humidity: 45,
        description: 'Hot and dry, especially inland. Coastal areas are more comfortable.',
        activities: ['beach activities', 'early morning tours', 'coastal cities'],
        clothingRecommendations: ['light cotton clothing', 'sun protection', 'sandals', 'wide-brimmed hat']
      },
      {
        id: 'autumn',
        name: 'Autumn',
        startMonth: 9,
        endMonth: 11,
        averageTemp: { min: 18, max: 28 },
        rainfall: 25,
        humidity: 55,
        description: 'Excellent weather with warm days and cool nights. Ideal for all activities.',
        activities: ['desert camping', 'mountain trekking', 'cultural tours', 'photography'],
        clothingRecommendations: ['versatile layers', 'light sweater', 'comfortable shoes', 'light scarf']
      },
      {
        id: 'winter',
        name: 'Winter',
        startMonth: 12,
        endMonth: 2,
        averageTemp: { min: 8, max: 18 },
        rainfall: 60,
        humidity: 70,
        description: 'Mild temperatures with more rainfall. Good for cultural activities and southern regions.',
        activities: ['indoor cultural sites', 'southern desert', 'spa treatments', 'cooking classes'],
        clothingRecommendations: ['warm layers', 'waterproof jacket', 'closed shoes', 'warm accessories']
      }
    ],
    'JPN': [ // Japan
      {
        id: 'spring',
        name: 'Spring (Cherry Blossom)',
        startMonth: 3,
        endMonth: 5,
        averageTemp: { min: 10, max: 20 },
        rainfall: 120,
        humidity: 65,
        description: 'Famous cherry blossom season with mild temperatures and occasional rain.',
        activities: ['hanami (cherry blossom viewing)', 'temple visits', 'garden tours', 'festivals'],
        clothingRecommendations: ['light layers', 'rain jacket', 'comfortable walking shoes', 'umbrella']
      },
      {
        id: 'summer',
        name: 'Summer',
        startMonth: 6,
        endMonth: 8,
        averageTemp: { min: 22, max: 30 },
        rainfall: 180,
        humidity: 80,
        description: 'Hot and humid with rainy season (tsuyu). Festival season.',
        activities: ['summer festivals', 'mountain hiking', 'beach visits', 'air-conditioned museums'],
        clothingRecommendations: ['light breathable clothing', 'rain gear', 'sun protection', 'cooling towels']
      },
      {
        id: 'autumn',
        name: 'Autumn (Fall Foliage)',
        startMonth: 9,
        endMonth: 11,
        averageTemp: { min: 15, max: 25 },
        rainfall: 80,
        humidity: 60,
        description: 'Beautiful fall colors with comfortable temperatures. Peak tourist season.',
        activities: ['autumn leaf viewing', 'hiking', 'cultural sites', 'food tours'],
        clothingRecommendations: ['layered clothing', 'light jacket', 'comfortable shoes', 'camera gear']
      },
      {
        id: 'winter',
        name: 'Winter',
        startMonth: 12,
        endMonth: 2,
        averageTemp: { min: 2, max: 10 },
        rainfall: 40,
        humidity: 55,
        description: 'Cold with occasional snow. Great for winter sports and hot springs.',
        activities: ['skiing', 'hot springs', 'winter illuminations', 'indoor cultural activities'],
        clothingRecommendations: ['warm winter clothing', 'thermal layers', 'waterproof boots', 'gloves and hat']
      }
    ]
  }

  static getInstance(): WeatherService {
    if (!WeatherService.instance) {
      WeatherService.instance = new WeatherService()
    }
    return WeatherService.instance
  }

  // Current Weather Data
  async getCurrentWeather(location: string, countryCode: string): Promise<WeatherData | null> {
    const cacheKey = `weather_${countryCode}_${location}`
    
    if (this.isCacheValid(cacheKey)) {
      return this.weatherCache.get(cacheKey) || null
    }

    try {
      const weatherData = await this.fetchWeatherData(location, countryCode)
      
      if (weatherData) {
        this.weatherCache.set(cacheKey, weatherData)
        this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_DURATION)
      }
      
      return weatherData
    } catch (error) {
      console.error(`Failed to fetch weather for ${location}, ${countryCode}:`, error)
      return null
    }
  }

  // Seasonal Information
  getSeasonalData(countryCode: string): CountryWeatherSeason[] {
    return this.SEASONAL_DATA[countryCode.toUpperCase()] || []
  }

  getCurrentSeason(countryCode: string): CountryWeatherSeason | null {
    const seasons = this.getSeasonalData(countryCode)
    const currentMonth = new Date().getMonth() + 1 // 1-12

    return seasons.find(season => {
      if (season.startMonth <= season.endMonth) {
        return currentMonth >= season.startMonth && currentMonth <= season.endMonth
      } else {
        // Handle seasons that cross year boundary (e.g., Dec-Feb)
        return currentMonth >= season.startMonth || currentMonth <= season.endMonth
      }
    }) || null
  }

  getSeasonForMonth(countryCode: string, month: number): CountryWeatherSeason | null {
    const seasons = this.getSeasonalData(countryCode)
    
    return seasons.find(season => {
      if (season.startMonth <= season.endMonth) {
        return month >= season.startMonth && month <= season.endMonth
      } else {
        return month >= season.startMonth || month <= season.endMonth
      }
    }) || null
  }

  // Travel Recommendations
  getBestTimeToVisit(countryCode: string, preferences?: {
    activities?: string[]
    weatherPreference?: 'hot' | 'mild' | 'cool'
    avoidRain?: boolean
  }): SeasonalRecommendation[] {
    const seasons = this.getSeasonalData(countryCode)
    
    return seasons.map(season => {
      let recommendation: 'excellent' | 'good' | 'fair' | 'poor' = 'good'
      const reasons: string[] = []

      // Temperature preferences
      if (preferences?.weatherPreference) {
        const avgTemp = (season.averageTemp.min + season.averageTemp.max) / 2
        
        if (preferences.weatherPreference === 'hot' && avgTemp > 25) {
          recommendation = 'excellent'
          reasons.push('Hot weather as preferred')
        } else if (preferences.weatherPreference === 'mild' && avgTemp >= 15 && avgTemp <= 25) {
          recommendation = 'excellent'
          reasons.push('Mild temperatures as preferred')
        } else if (preferences.weatherPreference === 'cool' && avgTemp < 15) {
          recommendation = 'excellent'
          reasons.push('Cool weather as preferred')
        }
      }

      // Rain preferences
      if (preferences?.avoidRain && season.rainfall > 100) {
        recommendation = recommendation === 'excellent' ? 'good' : 'fair'
        reasons.push('High rainfall period')
      }

      // Activity matching
      if (preferences?.activities) {
        const matchingActivities = preferences.activities.filter(activity =>
          season.activities.some(seasonActivity => 
            seasonActivity.toLowerCase().includes(activity.toLowerCase())
          )
        )
        
        if (matchingActivities.length > 0) {
          reasons.push(`Good for: ${matchingActivities.join(', ')}`)
        }
      }

      return {
        season: season.name,
        months: this.getMonthsInSeason(season),
        recommendation,
        reasons,
        activities: season.activities,
        packingList: season.clothingRecommendations,
        healthConsiderations: this.getHealthConsiderations(season)
      }
    })
  }

  // Climate Information
  getClimateInfo(countryCode: string): ClimateInfo | null {
    const seasons = this.getSeasonalData(countryCode)
    if (seasons.length === 0) return null

    const climateTypes: { [key: string]: string } = {
      'MAR': 'Mediterranean/Desert',
      'JPN': 'Temperate/Subtropical',
      'ITA': 'Mediterranean',
      'ESP': 'Mediterranean'
    }

    // Calculate annual averages
    const annualMinTemp = Math.min(...seasons.map(s => s.averageTemp.min))
    const annualMaxTemp = Math.max(...seasons.map(s => s.averageTemp.max))
    const annualRainfall = seasons.reduce((sum, s) => sum + (s.rainfall * 3), 0) // Approximate annual

    // Determine best and worst months
    const bestMonths = seasons
      .filter(s => s.rainfall < 50 && s.averageTemp.max < 35 && s.averageTemp.min > 5)
      .flatMap(s => this.getMonthsInSeason(s))
    
    const worstMonths = seasons
      .filter(s => s.rainfall > 100 || s.averageTemp.max > 35 || s.averageTemp.min < 5)
      .flatMap(s => this.getMonthsInSeason(s))

    return {
      type: climateTypes[countryCode.toUpperCase()] || 'Temperate',
      description: this.getClimateDescription(countryCode),
      averageTemperature: {
        annual: { min: annualMinTemp, max: annualMaxTemp },
        seasonal: seasons.reduce((acc, season) => {
          acc[season.name] = season.averageTemp
          return acc
        }, {} as { [season: string]: { min: number; max: number } })
      },
      rainfall: {
        annual: annualRainfall,
        seasonal: seasons.reduce((acc, season) => {
          acc[season.name] = season.rainfall * 3 // Convert to seasonal total
          return acc
        }, {} as { [season: string]: number })
      },
      bestMonths,
      worstMonths
    }
  }

  // Utility Methods
  private isCacheValid(key: string): boolean {
    const expiry = this.cacheExpiry.get(key)
    return expiry ? Date.now() < expiry : false
  }

  private getMonthsInSeason(season: CountryWeatherSeason): number[] {
    const months: number[] = []
    
    if (season.startMonth <= season.endMonth) {
      for (let i = season.startMonth; i <= season.endMonth; i++) {
        months.push(i)
      }
    } else {
      // Handle seasons crossing year boundary
      for (let i = season.startMonth; i <= 12; i++) {
        months.push(i)
      }
      for (let i = 1; i <= season.endMonth; i++) {
        months.push(i)
      }
    }
    
    return months
  }

  private getHealthConsiderations(season: CountryWeatherSeason): string[] {
    const considerations: string[] = []
    
    if (season.averageTemp.max > 30) {
      considerations.push('Stay hydrated', 'Use sun protection', 'Avoid midday sun')
    }
    
    if (season.rainfall > 80) {
      considerations.push('Waterproof gear recommended', 'Watch for flooding in some areas')
    }
    
    if (season.humidity > 70) {
      considerations.push('High humidity - dress in breathable fabrics')
    }
    
    return considerations
  }

  private getClimateDescription(countryCode: string): string {
    const descriptions: { [key: string]: string } = {
      'MAR': 'Morocco has a diverse climate ranging from Mediterranean along the coast to desert inland, with hot summers and mild winters.',
      'JPN': 'Japan has a temperate climate with four distinct seasons, influenced by monsoons and varying significantly from north to south.',
      'ITA': 'Italy enjoys a Mediterranean climate with hot, dry summers and mild, wet winters, varying from Alpine in the north to subtropical in the south.',
      'ESP': 'Spain has a predominantly Mediterranean climate with regional variations, from oceanic in the north to semi-arid in the southeast.'
    }
    
    return descriptions[countryCode.toUpperCase()] || 'Temperate climate with seasonal variations.'
  }

  private async fetchWeatherData(location: string, countryCode: string): Promise<WeatherData | null> {
    // Mock implementation - replace with actual weather API call
    // This would typically call OpenWeatherMap, WeatherAPI, or similar service
    
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          location,
          countryCode,
          current: {
            temperature: 22 + Math.random() * 10,
            humidity: 50 + Math.random() * 30,
            windSpeed: Math.random() * 20,
            condition: 'Partly Cloudy',
            icon: 'partly-cloudy',
            uvIndex: Math.floor(Math.random() * 11),
            visibility: 10 + Math.random() * 10
          },
          forecast: Array.from({ length: 7 }, (_, i) => ({
            date: new Date(Date.now() + i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            high: 20 + Math.random() * 15,
            low: 10 + Math.random() * 10,
            condition: ['Sunny', 'Partly Cloudy', 'Cloudy', 'Light Rain'][Math.floor(Math.random() * 4)],
            icon: 'sunny',
            precipitation: Math.random() * 10,
            humidity: 40 + Math.random() * 40
          })),
          lastUpdated: new Date()
        })
      }, 200)
    })
  }
}
