import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// List of available countries (in real app, this could be fetched from API)
const AVAILABLE_COUNTRIES = ['morocco', 'ma'] // Support both full name and ISO code
const DEFAULT_COUNTRY = 'morocco'

// Pages that should be country-aware
const COUNTRY_AWARE_PAGES = [
  'destinations',
  'experiences',
  'cultural-immersion',
  'explore',
  'trip-builder',
]

// Pages that should remain global (not country-specific)
const GLOBAL_PAGES = [
  'api',
  'admin',
  'super-admin',
  'dashboard',
  'login',
  'signup',
  'countries',
  'about',
  'contact',
  'help',
  'ui-showcase',
  'live-enhancement',
  'trip-builder',
  'international-demo',
  'country-data-demo',
  '_next',
  'favicon.ico',
  'robots.txt',
  'sitemap.xml',
]

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  const pathSegments = pathname.split('/').filter(Boolean)
  
  // Skip middleware for global pages and API routes
  if (pathSegments.length === 0 || GLOBAL_PAGES.some(page => pathSegments[0].startsWith(page))) {
    return NextResponse.next()
  }
  
  const firstSegment = pathSegments[0].toLowerCase()
  const isCountryRoute = AVAILABLE_COUNTRIES.includes(firstSegment)
  
  // If it's already a valid country route, continue
  if (isCountryRoute) {
    return NextResponse.next()
  }
  
  // Check if the first segment is a country-aware page
  if (COUNTRY_AWARE_PAGES.includes(firstSegment)) {
    // Get user's preferred country from cookie or use default
    const preferredCountry = request.cookies.get('preferred-country')?.value || DEFAULT_COUNTRY
    
    // Redirect to country-specific version
    const newUrl = new URL(`/${preferredCountry}/${pathname}`, request.url)
    return NextResponse.redirect(newUrl)
  }
  
  // For now, let existing pages work as they are
  // The international structure is ready but we keep existing functionality
  
  // Keep existing destination routes working as they are
  
  // For any other unrecognized routes, let Next.js handle them
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}