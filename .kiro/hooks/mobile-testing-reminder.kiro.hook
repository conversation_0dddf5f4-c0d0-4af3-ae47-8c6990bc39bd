{"enabled": true, "name": "Mobile Testing Reminder", "description": "Presents an interactive checklist and recommendations to ensure comprehensive mobile testing of components including device testing, performance, and accessibility", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/components/**/*.tsx", "src/app/**/*.tsx"]}, "then": {"type": "askAgent", "prompt": "A developer has been working on React components. Present them with a comprehensive mobile testing checklist and recommendations:\n\n## 📱 Mobile Testing Checklist\nPlease verify the following before considering your component complete:\n\n### Device Testing\n- [ ] Test on actual mobile device (iOS/Android)\n- [ ] Verify touch interactions work properly\n- [ ] Check responsive layout at different screen sizes\n- [ ] Test keyboard navigation on mobile\n- [ ] Verify loading performance on slower networks\n- [ ] Test offline functionality (if applicable)\n- [ ] Check PWA features work correctly\n\n### Recommended Test Devices/Sizes\n- **iPhone SE** (375px width) - Smallest modern screen\n- **iPhone 12/13** (390px width) - Common iOS size\n- **Samsung Galaxy S21** (360px width) - Common Android size\n- **iPad** (768px width) - Tablet experience\n- **Desktop** (1024px+ width) - Full desktop experience\n\n### Performance Testing\nTest your component with:\n- Slow 3G network simulation\n- CPU throttling enabled\n- Battery saver mode\n- Different orientations (portrait/landscape)\n\n### Accessibility Testing\nEnsure you've tested:\n- VoiceOver (iOS) or TalkBack (Android)\n- High contrast mode\n- Large text settings\n- Reduced motion preferences\n\n### Success Actions\nOnce testing is complete:\n- [ ] Mark mobile testing as complete\n- [ ] Update component documentation\n- [ ] Add mobile testing notes to PR\n\n### Resources\n- Mobile testing guidelines in project docs\n- Device testing lab access\n- Performance testing tools\n- Accessibility testing resources\n\nRemember: This is a mobile-first PWA project - mobile experience is critical!"}}