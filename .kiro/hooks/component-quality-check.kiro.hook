{"enabled": true, "name": "Component Quality Check", "description": "Automatically check component quality when saving React components including TypeScript validation, component structure, accessibility audit, mobile responsiveness, and performance analysis", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/components/**/*.tsx"]}, "then": {"type": "askAgent", "prompt": "Please perform a comprehensive quality check on the React component that was just saved. Analyze the following aspects:\n\n1. **TypeScript Validation**: Run `npx tsc --noEmit --project tsconfig.json` to check for compilation errors\n\n2. **Component Structure Check**: Verify the component follows standard structure:\n   - Proper imports organization (React imports first, then third-party, then local)\n   - TypeScript interface definitions with proper typing\n   - Accessibility attributes (ARIA labels, roles, semantic HTML)\n   - Error boundaries and proper error handling\n   - Mobile-responsive design implementation\n\n3. **Accessibility Audit**: Check for:\n   - ARIA labels and roles are properly implemented\n   - Keyboard navigation support (tab order, focus management)\n   - Semantic HTML elements are used appropriately\n   - Color contrast compliance with WCAG standards\n   - Screen reader compatibility\n\n4. **Mobile Responsiveness Check**: Verify:\n   - Mobile-first design approach is followed\n   - Proper breakpoint usage (sm:, md:, lg:, xl:)\n   - Touch-friendly interactions with minimum 44px touch targets\n   - Responsive layout implementation with proper spacing\n\n5. **Performance Analysis**: Check for:\n   - Proper lazy loading implementation where appropriate\n   - Efficient re-render patterns (React.memo, useCallback, useMemo)\n   - Bundle size impact considerations\n   - Image optimization using Next.js Image component\n\nProvide a detailed report with:\n- ✅ Success: Green checkmark with summary for passed checks\n- ⚠️ Warning: Yellow warning with recommendations for improvements\n- ❌ Error: Red error with required fixes for critical issues\n\nInclude specific suggestions for fixes and link to relevant documentation when issues are found. If critical issues are found that would prevent deployment, clearly indicate this."}}