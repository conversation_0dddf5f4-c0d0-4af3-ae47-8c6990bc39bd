{"name": "comet<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@stripe/stripe-js": "^7.8.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.54.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "framer-motion": "^11.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.400.0", "next": "14.2.0", "react": "^18", "react-dom": "^18", "stripe": "^18.4.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.2.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "postcss": "^8", "tailwindcss": "^3.4.0", "typescript": "^5"}}